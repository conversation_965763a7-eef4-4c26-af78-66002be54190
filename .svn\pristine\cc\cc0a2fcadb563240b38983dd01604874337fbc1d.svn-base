// packageA/pages/trainBusinessDetail/trainBusinessDetail.js
var ports = require("@/utils/ports")
const utils = require('../../../utils/util')
var app = getApp();
Page({

  /**
   * 页面的初始数据
   */
  data: {
    showPrice: false,
    selectedId: null,
    selectedCoupon: null,
    MemberBonusList: [],
    logoImg: app.logoimg,
    info: {},
    maionshw: false,
    // Enclosureimg: [],
    trainBusinessID: '',
    trainCompanyID: undefined,
    price: -1,
    trainCompanyPrice:'',//人社报名价格
    previousUrl: undefined,
    // 个人信息完善弹窗相关
    showPersonalInfoModal: false,
    personalInfo: {
      titleID: '', // 性别
      shortDescription: '', // 地区
      birthdayStr: '' // 生日
    },
    // 性别选择器
    genderArray: ['先生', '女士'],
    genderIndex: 0,
    // 地区选择器
    provinceList: [],
    cityList: [],
    districtList: [],
    currentPickerValue: [0, 0, 0],
    selectedRegion: '',
    // 生日选择器
    birthday: '',
    startTime: '',
    endTime: '',
    // 直辖市列表
    municipalities: ['北京市', '天津市', '上海市', '重庆市'],
  },
//红包列表
getMyMemberBonusList() {
  var data = {
    pageNumber:99,
    status:10
  }
  ports.ModuleAll.getMyMemberBonusList(data).then(res => {
    var that = this
    if (res.data.header.code == 0) {
      var MemberBonusList = res.data.body.data.rows
      
      console.log(MemberBonusList,'realMemberBonusList');
      that.setData({
        MemberBonusList: MemberBonusList
      })
    }
  })
},
  /**
   * 生命周期函数--监听页面加载
   */
  async onLoad(options) {
    let nostu = options.nostu
    if (wx.getStorageSync('USER_OPENID') || wx.getStorageSync('USER_UNIONID')) {
      // 任一存在，跳过如下代码
    } else {
      // 两者都不存在，执行获取操作
      let res = await ports.ModuleAll.loginWX();
      let resOne = await ports.ModuleAll.getWeiXinAppOpenId({
        publicNo: app.publicNo,
        js_code: res.code,
      })
      var openID = resOne.data.body.openID
      var unionID = resOne.data.body.unionid
      wx.setStorageSync('USER_OPENID', openID)
      wx.setStorageSync('USER_UNIONID', unionID)
    }
    const {id,trainCompanyID,previousUrl,price,mid,toCompanyID,fromInvite}=options;
    this.setData({
      nostu,
      trainBusinessID: id,
      trainCompanyID: trainCompanyID||undefined,
      previousUrl: previousUrl||`/packageA/pages/machineDetail/machineDetail?id=`+mid,
      trainCompanyPrice:price || '', //人社报名价格
      toCompanyID:toCompanyID || '',
      fromInvite: fromInvite === 'true' // 记录是否来自邀请页面
    })
    this.getTrainBusinessDetail()
    
    // 初始化个人信息弹窗
    this.initPersonalInfoModal()
  },
  getTrainBusinessDetail() {
    let params = {
      trainBusinessID: this.data.trainBusinessID,
    }
    ports.ModuleAll.getTrainBusinessDetail(params).then(res => {
      wx.hideLoading()
      // console.log(res.data.body.trainBusiness, 'detail');
      console.log(wx.getStorageSync('USER_STUDENT'),'45645');
      if(wx.getStorageSync('USER_STUDENT').companyName == res.data.body.trainBusiness.companyName){
        this.setData({
          companyName: wx.getStorageSync('USER_STUDENT').companyName
        })
      }
      this.setData({
        info: res.data.body.trainBusiness || {}
      })
      this.getMyMemberBonusList()
      this.baoming()
    })
  },
  //是否报名
  baoming() {
    function getCurrentDateTime() {
      const now = new Date();
      const year = now.getFullYear();
      const month = (now.getMonth() + 1).toString().padStart(2, '0');
      const day = now.getDate().toString().padStart(2, '0');
      const hours = now.getHours().toString().padStart(2, '0');
      const minutes = now.getMinutes().toString().padStart(2, '0');
      const seconds = now.getSeconds().toString().padStart(2, '0');
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    }
    let info = this.data.info
    var that = this
    //获取当前时间
    var date1 = new Date();
    var beginTime = getCurrentDateTime()
    //获取当前时间截
    var timestamp2 = (new Date(beginTime)).valueOf();

    if (info.beginDate <= timestamp2 && timestamp2 <= info.endDate && info.priceCash) {
      that.setData({
        maionshw: true
      })
    }
  },
  async getDevice() {
    let res = await ports.ModuleAll.getSystemInfo();
    let ADCID = new Date().getTime().toString(36) + Math.random().toString(36).substr(2, 9);
    let params = {
      applicationID: app.applicationID,
      siteID: app.siteID,
      company: res.brand, //制造商名称
      name: res.model, //设备型号
      shortName: res.brand, //设备型号
      category: 3, //设备类型
      OSname: res.system, //操作系统
      model: res.model, //品牌型号
      // udid: openId, //一个字符串
      // imei: openId, //手机特有的1个串
      mac: ADCID, //设备的mac地址
    }
    let resDevice = await ports.ModuleAll.submitDeviceInfo(params)
    wx.setStorageSync('deviceID', resDevice.data.body.deviceID)
    this.setData({
      clearFlag: true
    })
  },
  onClose(){
    this.setData({showPrice: false})
  },
  choosePrice(){
    this.setData({showPrice: true})
  },
  onCouponChange(e) {
    const selectedId = e.detail; 
    console.log(selectedId,'selectedId');
    const selected = this.data.MemberBonusList.find(i => i.memberBonusID == selectedId);
    console.log(selected,'selected');
    this.setData({
      selectedId,
      selectedCoupon: selected,
      MemberBonusList: this.data.MemberBonusList.map(item => ({
        ...item,
        _checked: item.memberBonusID == selectedId,
      })),
    });
  },
  async newapply() {
    let that = this
    if (!wx.getStorageSync('deviceID')) this.getDevice();
    if (!wx.getStorageSync('USER_MEMBER')) {
      wx.showLoading({
        title: '加载中',
      })
      wx.setStorageSync('previousUrl', `/packageA/pages/trainBusinessDetail/trainBusinessDetail`)
      await utils.LOGIN(0);
      this.setData({
        memberInfo: wx.getStorageSync('USER_MEMBER')
      })  
      if (!wx.getStorageSync('USER_MEMBER')) {
        return
      }
    }

    // 检查个人信息是否完善
    const member = wx.getStorageSync('USER_MEMBER');
    if (!this.checkPersonalInfo(member)) {
      this.showPersonalInfoModal();
      return;
    }

    // if (!wx.getStorageSync('USER_SESSIONID')) {
    //   wx.showToast({
    //     icon:'none',
    //     title: '请先登录',
    //   })
    //   setTimeout(() => {
    //     wx.switchTab({
    //       url: '/pages/memberSelf/memberSelf',
    //     })
    //   }, 1000)
    //   return
      
    // }
    var unionid = wx.getStorageSync("USER_UNIONID")
    var openid = wx.getStorageSync("USER_OPENID")
    let playCompanyID = ''
    let customerCompanyID = ''
    if(wx.getStorageSync('checkedMachine').id){
      const {machineID} = wx.getStorageSync('checkedMachine')
      const resmachine = await ports.ModuleAll.getOneMachineDetail({
        machineID,
      })
      if(resmachine.data.header.code == 0){
        customerCompanyID = resmachine.data.body.data.customerCompanyID
        playCompanyID = resmachine.data.body.data.playCompanyID
      }
    }
    let params = {
      siteID: app.siteID,
      shopID: app.shopID,
      buyType: 1, //类型为培训业务
      applyFromType:1,
      trainBusinessID: this.data.trainBusinessID,
      joinMemberID: wx.getStorageSync('USER_MEMBERID'),
      joinStudentID:wx.getStorageSync('USER_STUDENT').studentID||'',
      customerCompanyID,
      playCompanyID,
      governmentCompanyID:this.data.toCompanyID,
      joinName: wx.getStorageSync('USER_MEMBER').memberName, //姓名
      joinPhone: wx.getStorageSync('USER_MEMBER').phone, //手机号
    }
    if (this.data.trainCompanyID!=="undefined") {
      params.trainCompanyID = this.data.trainCompanyID //补贴政府公司ID
    }
    ports.ModuleAll.createOneMemberTrain(params).then(res => {
      if (res.data.header.code !== 0)
        return this.showFail()

      this.setData({
        price: res.data.body.price
      })
      if (res.data.body.price === 0)
        wx.showToast({
          title: '报名成功……',icon:'success'
        })
      else if (res.data.body.price !== 0) {
        wx.showToast({
          title: '报名成功,正在创建订单……',
          icon: 'none'
        })
        ports.ModuleAll.standardCreateOneMemberOrder({
          price: this.data.price - (this.data.selectedCoupon?this.data.selectedCoupon.amount: 0),
          goodsQTY:1,
          byCart: '0',
          orderType: '60',
          payFrom: 1,
          payWayID: app.payWayID,
          goodsShopID: '28b3625d8c3c463eac1b1d6308e449f0', //固定使用一个专门用来是培训付款的商品ID
          shopID : app.shopID,
          trainBusinessID : this.data.trainBusinessID,
          memberBonusIDChain: this.data.selectedId
        }).then(res => {
          let that = this
          if (res.data.header.code == 0) {
            let payData = {}
            payData.memberOrderID = res.data.body.memberOrderID;
            payData.openid = openid
            payData.payWayID = app.paywayID
            payData.body = '购买商品'
            if (that.data.companyclass == '21') {
              that.setData({
                paymentOf: true
              })
              return
            }
            ports.ModuleAll.getWeixinPayInfo(payData).then(res => {
              if (res.data.header.code == 0) {
                var data = res.data.body;
                wx.requestPayment({
                  timeStamp: data.timeStamp,
                  nonceStr: data.nonceStr,
                  package: data.package,
                  signType: data.signType,
                  paySign: data.paySign,
                  success(res) {
                    let newinfo = that.data.info
                    newinfo.isBuy = 1
                    that.setData({info:newinfo})
                    wx.showToast({
                      title: '支付成功',
                      icon: 'none',
                      mask: true,
                      duration: 1000
                    })

                    wx.showLoading({
                      title: '',
                    })

                    // 支付成功后处理返回逻辑
                    if (that.data.fromInvite) {
                      wx.hideLoading()
                      setTimeout(() => {
                        that.handleRegistrationSuccess();
                      }, 1000);
                    } else if (that.data.previousUrl) {
                      wx.hideLoading()
                      // setTimeout(() => {
                      //   wx.redirectTo({
                      //     url: that.data.previousUrl,
                      //   })
                      // }, 0);
                    } else {
                      that.getTrainBusinessDetail()
                    }

                  },
                  fail() {
                    wx.showToast({
                      title: '取消支付',
                      icon: 'none',
                      mask: true,
                      duration: 1000
                    })
                    wx.showLoading({
                      title: '',
                    })
                    that.getTrainBusinessDetail()
                    // setTimeout(() => {
                    //   wx.redirectTo({
                    //     url: '/packageA/pages/goodsShop/orderList/orderList',
                    //   })
                    // }, 1000);
                  }
                })
              } else {
                wx.showToast({
                  title: res.data.header.msg,
                  icon: 'none'
                })
              }
            })
          } else {
            wx.showToast({
              title: res.data.header.msg,
              icon: 'none'
            })
          }
        })
      }
    })
  },
  //进行报名
  async apply() {
    if(this.data.nostu != 1){
      if(this.data.MemberBonusList.length>=1){
        this.choosePrice()
        return
      }
    }
    
    let that = this
    if (!wx.getStorageSync('deviceID')) this.getDevice();
    if (!wx.getStorageSync('USER_MEMBER')) {
      wx.showLoading({
        title: '加载中',
      })
      wx.setStorageSync('previousUrl', `/packageA/pages/trainBusinessDetail/trainBusinessDetail`)
      await utils.LOGIN(0);
      this.setData({
        memberInfo: wx.getStorageSync('USER_MEMBER')
      })  
      if (!wx.getStorageSync('USER_MEMBER')) {
        return
      }
    }

    // 检查个人信息是否完善
    const member = wx.getStorageSync('USER_MEMBER');
    if (!this.checkPersonalInfo(member)) {
      this.showPersonalInfoModal();
      return;
    }

    // if (!wx.getStorageSync('USER_SESSIONID')) {
    //   wx.showToast({
    //     icon:'none',
    //     title: '请先登录',
    //   })
    //   setTimeout(() => {
    //     wx.switchTab({
    //       url: '/pages/memberSelf/memberSelf',
    //     })
    //   }, 1000)
    //   return
      
    // }
    var unionid = wx.getStorageSync("USER_UNIONID")
    var openid = wx.getStorageSync("USER_OPENID")
    let playCompanyID = ''
    let customerCompanyID = ''
    if(wx.getStorageSync('checkedMachine').id){
      const {machineID} = wx.getStorageSync('checkedMachine')
      const resmachine = await ports.ModuleAll.getOneMachineDetail({
        machineID,
      })
      if(resmachine.data.header.code == 0){
        customerCompanyID = resmachine.data.body.data.customerCompanyID
        playCompanyID = resmachine.data.body.data.playCompanyID
      }
    }
    let params = {
      siteID: app.siteID,
      shopID: app.shopID,
      buyType: 1, //类型为培训业务
      applyFromType:1,
      trainBusinessID: this.data.trainBusinessID,
      joinMemberID: wx.getStorageSync('USER_MEMBERID'),
      joinStudentID:wx.getStorageSync('USER_STUDENT').studentID||'',
      customerCompanyID,
      playCompanyID,
      governmentCompanyID:this.data.toCompanyID,
      joinName: wx.getStorageSync('USER_MEMBER').memberName, //姓名
      joinPhone: wx.getStorageSync('USER_MEMBER').phone, //手机号
    }
    if (this.data.trainCompanyID!=="undefined") {
      params.trainCompanyID = this.data.trainCompanyID //补贴政府公司ID
    }
    ports.ModuleAll.createOneMemberTrain(params).then(res => {
      if (res.data.header.code !== 0)
        return this.showFail()

      this.setData({
        price: res.data.body.price
      })
      if (res.data.body.price === 0)
        wx.showToast({
          title: '报名成功……',icon:'success'
        })
      else if (res.data.body.price !== 0) {
        wx.showToast({
          title: '报名成功,正在创建订单……',
          icon: 'none'
        })
        ports.ModuleAll.standardCreateOneMemberOrder({
          price: this.data.price - (this.data.selectedCoupon?this.data.selectedCoupon.amount: 0),
          goodsQTY:1,
          byCart: '0',
          orderType: '60',
          payFrom: 1,
          payWayID: app.payWayID,
          goodsShopID: '28b3625d8c3c463eac1b1d6308e449f0', //固定使用一个专门用来是培训付款的商品ID
          shopID : app.shopID,
          trainBusinessID : this.data.trainBusinessID,
          memberBonusIDChain: this.data.selectedId
        }).then(res => {
          let that = this
          if (res.data.header.code == 0) {
            let payData = {}
            payData.memberOrderID = res.data.body.memberOrderID;
            payData.openid = openid
            payData.payWayID = app.paywayID
            payData.body = '购买商品'
            if (that.data.companyclass == '21') {
              that.setData({
                paymentOf: true
              })
              return
            }
            ports.ModuleAll.getWeixinPayInfo(payData).then(res => {
              if (res.data.header.code == 0) {
                var data = res.data.body;
                wx.requestPayment({
                  timeStamp: data.timeStamp,
                  nonceStr: data.nonceStr,
                  package: data.package,
                  signType: data.signType,
                  paySign: data.paySign,
                  success(res) {
                    let newinfo = that.data.info
                    newinfo.isBuy = 1
                    that.setData({info:newinfo})
                    wx.showToast({
                      title: '支付成功',
                      icon: 'none',
                      mask: true,
                      duration: 1000
                    })

                    wx.showLoading({
                      title: '',
                    })

                    // 支付成功后处理返回逻辑
                    if (that.data.fromInvite) {
                      wx.hideLoading()
                      setTimeout(() => {
                        that.handleRegistrationSuccess();
                      }, 1000);
                    } else if (that.data.previousUrl) {
                      wx.hideLoading()
                      setTimeout(() => {
                        wx.redirectTo({
                          url: that.data.previousUrl,
                        })
                      }, 0);
                    } else {
                      that.getTrainBusinessDetail()
                    }

                  },
                  fail() {
                    wx.showToast({
                      title: '取消支付',
                      icon: 'none',
                      mask: true,
                      duration: 1000
                    })
                    wx.showLoading({
                      title: '',
                    })
                    that.getTrainBusinessDetail()
                    // setTimeout(() => {
                    //   wx.redirectTo({
                    //     url: '/packageA/pages/goodsShop/orderList/orderList',
                    //   })
                    // }, 1000);
                  }
                })
              } else {
                wx.showToast({
                  title: res.data.header.msg,
                  icon: 'none'
                })
              }
            })
          } else {
            wx.showToast({
              title: res.data.header.msg,
              icon: 'none'
            })
          }
        })
      }
    })
  },
  companyjoin(){
    let params = {
      sessionID: wx.getStorageSync('USER_SESSIONID'),
      siteID: app.siteID,
      shopID: app.shopID,
      buyType: 1, //类型为培训业务
      applyFromType:2,
      applyType:1,
      checkStatus:2,
      status:15,
      joinStudentID:wx.getStorageSync('USER_STUDENT').studentID,
      trainBusinessID: this.data.trainBusinessID,
      joinMemberID: wx.getStorageSync('USER_MEMBERID'),
      governmentCompanyID:this.data.toCompanyID,
      joinName: wx.getStorageSync('USER_MEMBER').memberName, //姓名
      joinPhone: wx.getStorageSync('USER_MEMBER').phone, //手机号
    }
    if (this.data.trainCompanyID!=="undefined") {
      params.trainCompanyID = this.data.trainCompanyID //补贴政府公司ID
    }
    ports.ModuleAll.createOneMemberTrain(params).then(res => {
      if (res.data.header.code !== 0)
        return this.showFail()
      this.setData({
        price: res.data.body.price
      })
      if (res.data.body.price === 0){
        wx.showToast({
          title: '报名成功',icon:'success',duration: 1500
        })
        setTimeout(() => {
          // 免费报名成功，直接处理返回逻辑
          this.handleRegistrationSuccess();
        }, 2000);
      } else if (res.data.body.price !== 0) {
        wx.showToast({
          title: '报名成功',
          icon: 'none'
        })
        // 需要付费的情况，不在这里处理返回，等待付款成功后处理
      }
    })
  },

  /**
   * 处理报名成功后的逻辑
   */
  handleRegistrationSuccess() {
    if (this.data.fromInvite) {
      // 如果来自邀请页面，设置标识并返回到邀请页面
      wx.setStorageSync('REGISTRATION_SUCCESS', true);

      // 获取邀请页面的完整路径
      const inviteInfo = wx.getStorageSync('INVITE_RETURN_INFO');
      if (inviteInfo) {
        // 构建返回邀请页面的URL参数
        const urlParams = [];
        Object.keys(inviteInfo).forEach(key => {
          if (inviteInfo[key] !== undefined && inviteInfo[key] !== 'undefined') {
            urlParams.push(`${key}=${encodeURIComponent(inviteInfo[key])}`);
          }
        });

        const inviteUrl = `/pages/sharePageForInvite/sharePageForInvite?${urlParams.join('&')}`;

        // 返回到邀请页面
        wx.reLaunch({
          url: inviteUrl
        });
      } else {
        // 如果没有邀请信息，直接返回
        wx.navigateBack();
      }
    } else {
      // 正常返回
      wx.navigateBack();
    }
  },

  //去付款
  payOrder(e) {
    console.log(e.currentTarget.dataset.id,"e.currentTarget.dataset.id");
    
    var id = e.currentTarget.dataset.id
    if (!id) {
      wx.showToast({
        title: '订单异常',
        icon: 'none'
      })
      return
    }

    // 检查个人信息是否完善
    const member = wx.getStorageSync('USER_MEMBER');
    if (!this.checkPersonalInfo(member)) {
      this.showPersonalInfoModal();
      return;
    }

    var openid = wx.getStorageSync("USER_OPENID")
    if (!openid) {
      wx.showToast({
        title: '用户信息异常',
        icon: 'none'
      })
      return
    }
    wx.showToast({
      title: '提交中',
      icon: 'loading',
      mask: true
    })

    let payData = {}
    payData.memberOrderID = id
    payData.openid = openid
    payData.payWayID = app.paywayID
    payData.body = '购买商品'
    ports.ModuleAll.getWeixinPayInfo(payData).then(  res => {
      if (res.data.header.code == 0) {
        var data = res.data.body;
        wx.requestPayment({
          timeStamp: data.timeStamp,
          nonceStr: data.nonceStr,
          package: data.package,
          signType: data.signType,
          paySign: data.paySign,
          success(res) {
            wx.showToast({
              title: '支付成功',
              icon: 'none',
              mask: true,
              duration: 1000
            })

            // 支付成功后处理返回逻辑
            setTimeout(() => {
              if (that.data.fromInvite) {
                that.handleRegistrationSuccess();
              }
            }, 1000);
          },
          fail() {
            wx.showToast({
              title: '取消支付',
              icon: 'none',
              mask: true,
              duration: 1000
            })
            // setTimeout(() => {
            //   wx.redirectTo({
            //     url: '/packageA/pages/goodsShop/orderList/orderList',
            //   })
            // }, 1000);
          }
        })
      } else {
        wx.showToast({
          title: res.data.header.msg,
          icon: 'none'
        })
      }
    })

  },

  // 检查个人信息是否完善
  checkPersonalInfo(member) {
    // 检查性别是否已设置
    if (!member.titleID) {
      return false;
    }
    
    // 检查地区是否已设置
    if (!member.xianID || member.xianID.trim() === '') {
      return false;
    }
    
    // 检查生日是否已设置
    if (!member.birthdayStr || member.birthdayStr.trim() === '') {
      return false;
    }
    
    return true;
  },

  showFail() {
    wx.showToast({
      title: '报名失败，请联系管理员',
      icon: 'none'
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },

  // 初始化个人信息弹窗
  initPersonalInfoModal() {
    // 计算日期范围：当前年份的前60年到前20年（即20岁-60岁区间）
    const today = new Date();
    const currentYear = today.getFullYear();
    const startYear = currentYear - 60; // 60年前
    const endYear = currentYear - 20;   // 20年前
    const startDate = new Date(startYear, 0, 1);
    const endDate = new Date(endYear, 11, 31);

    // 格式化日期为 YYYY-MM-DD
    const formatDate = (date) => {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    };

    this.setData({
      startTime: formatDate(startDate),
      endTime: formatDate(endDate),
    });

    // 获取省份列表
    this.getProvinceList();
  },

  // 显示个人信息完善弹窗
  showPersonalInfoModal() {
    const member = wx.getStorageSync('USER_MEMBER');
    
    // 设置当前值
    let genderIndex = 0;
    if (member.titleID === app.man) {
      genderIndex = 0;
    } else if (member.titleID === app.men) {
      genderIndex = 1;
    }

    let birthday = '';
    if (member.birthdayStr) {
      birthday = member.birthdayStr.split(' ')[0];
    }

    this.setData({
      showPersonalInfoModal: true,
      personalInfo: {
        titleID: member.titleID || '',
        shortDescription: member.shortDescription || '',
        birthdayStr: member.birthdayStr || ''
      },
      genderIndex: genderIndex,
      birthday: birthday,
      selectedRegion: member.shortDescription || ''
    });
  },

  // 关闭个人信息完善弹窗
  closePersonalInfoModal() {
    this.setData({
      showPersonalInfoModal: false
    });
  },

  // 性别选择器变化
  onGenderChange(e) {
    const index = e.detail.value;
    const titleID = index === 0 ? app.man : app.men;
    
    this.setData({
      genderIndex: index,
      'personalInfo.titleID': titleID
    });
  },

  // 地区选择器变化
  onRegionChange(e) {
    const values = e.detail.value;
    const province = this.data.provinceList[values[0]];
    const city = this.data.cityList[values[1]];
    const district = this.data.districtList[values[2]];
    
    if (province && city && district) {
      const region = `${province.name}-${city.name}-${district.name}`;
      this.setData({
        currentPickerValue: values,
        selectedRegion: region,
        'personalInfo.shortDescription': region
      });
    }
  },

  // 地区选择器列变化
  onRegionColumnChange(e) {
    const { column, value } = e.detail;
    const currentPickerValue = [...this.data.currentPickerValue];
    currentPickerValue[column] = value;

    if (column === 0) {
      // 省份变化，重新获取城市列表
      const province = this.data.provinceList[value];
      if (province) {
        if (this.data.municipalities.includes(province.name)) {
          // 如果是直辖市，直接获取区县列表
          this.getMunicipalityDistricts(province.cityID, province.name);
        } else {
          // 普通省份，获取市级列表
          this.getCityList(province.cityID);
        }
        currentPickerValue[1] = 0;
        currentPickerValue[2] = 0;
      }
    } else if (column === 1) {
      // 城市变化，重新获取区县列表
      const city = this.data.cityList[value];
      if (city) {
        this.getDistrictList(city.cityID);
        currentPickerValue[2] = 0;
      }
    }

    this.setData({
      currentPickerValue
    });
  },

  // 生日选择器变化
  onBirthdayChange(e) {
    const birthday = e.detail.value;
    this.setData({
      birthday: birthday,
      'personalInfo.birthdayStr': birthday + ' 00:00:00'
    });
  },

  // 获取省份列表
  getProvinceList() {
    ports.ModuleAll.getCityList({
      sessionID: wx.getStorageSync('USER_SESSIONID'),
      cityID: app.ChinaID,
      pageNumber: 9999,
      sortTypeName: 1,
      depth: 1
    }).then(res => {
      if (res.data.header.code === 0) {
        const provinceList = res.data.body.data.rows || [];
        
        // 对省份列表进行排序：优先直辖市，然后按字母顺序
        provinceList.sort((a, b) => {
          const aIsMunicipality = this.data.municipalities.includes(a.name);
          const bIsMunicipality = this.data.municipalities.includes(b.name);
          
          // 如果都是直辖市，按直辖市列表中的顺序排序
          if (aIsMunicipality && bIsMunicipality) {
            return this.data.municipalities.indexOf(a.name) - this.data.municipalities.indexOf(b.name);
          }
          
          // 如果只有a是直辖市，a排在前面
          if (aIsMunicipality && !bIsMunicipality) {
            return -1;
          }
          
          // 如果只有b是直辖市，b排在前面
          if (!aIsMunicipality && bIsMunicipality) {
            return 1;
          }
          
          // 都不是直辖市，按字母顺序排序
          return a.name.localeCompare(b.name, 'zh-CN');
        });
        
        this.setData({
          provinceList: provinceList
        });
        
        // 获取第一个省份的城市列表
        if (provinceList.length > 0) {
          const firstProvince = provinceList[0];
          if (this.data.municipalities.includes(firstProvince.name)) {
            // 如果是直辖市，直接获取区县列表
            this.getMunicipalityDistricts(firstProvince.cityID, firstProvince.name);
          } else {
            // 普通省份，获取市级列表
            this.getCityList(firstProvince.cityID);
          }
        }
      }
    });
  },

  // 获取直辖市的区县列表
  getMunicipalityDistricts(cityID, cityName) {
    // 为直辖市创建市级列表（重复显示市名）
    const cityList = [{
      cityID: cityID,
      name: cityName
    }];
    
    this.setData({
      cityList: cityList
    });
    
    // 获取区县列表
    ports.ModuleAll.getCityList({
      sessionID: wx.getStorageSync('USER_SESSIONID'),
      cityID: cityID,
      pageNumber: 9999,
      sortTypeName: 1,
      depth: 1
    }).then(res => {
      if (res.data.header.code === 0) {
        const districtList = res.data.body.data.rows || [];
        this.setData({
          districtList: districtList
        });
      }
    });
  },

  // 获取城市列表
  getCityList(provinceId) {
    ports.ModuleAll.getCityList({
      sessionID: wx.getStorageSync('USER_SESSIONID'),
      cityID: provinceId,
      pageNumber: 9999,
      sortTypeName: 1,
      depth: 1
    }).then(res => {
      if (res.data.header.code === 0) {
        const cityList = res.data.body.data.rows || [];
        this.setData({
          cityList: cityList
        });
        
        // 获取第一个城市的区县列表
        if (cityList.length > 0) {
          this.getDistrictList(cityList[0].cityID);
        }
      }
    });
  },

  // 获取区县列表
  getDistrictList(cityId) {
    ports.ModuleAll.getCityList({
      sessionID: wx.getStorageSync('USER_SESSIONID'),
      cityID: cityId,
      pageNumber: 9999,
      sortTypeName: 1,
      depth: 1
    }).then(res => {
      if (res.data.header.code === 0) {
        const districtList = res.data.body.data.rows || [];
        this.setData({
          districtList: districtList
        });
      }
    });
  },

  // 提交个人信息
  submitPersonalInfo() {
    const { personalInfo } = this.data;
    
    // 验证必填项
    if (!personalInfo.titleID) {
      wx.showToast({
        title: '请选择性别',
        icon: 'none'
      });
      return;
    }
    
    if (!personalInfo.shortDescription) {
      wx.showToast({
        title: '请选择地区',
        icon: 'none'
      });
      return;
    }
    
    if (!personalInfo.birthdayStr) {
      wx.showToast({
        title: '请选择生日',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({
      title: '提交中...'
    });

    // 分别提交每个字段
    const promises = [];
    
    // 提交性别
    if (personalInfo.titleID) {
      const genderData = {
        sessionID: wx.getStorageSync('USER_SESSIONID'),
        titleID: personalInfo.titleID,
        sex: personalInfo.titleID === app.man ? 0 : 1
      };
      promises.push(ports.ModuleAll.updateMyMemberTitle(genderData));
    }
    
    // 提交地区
    if (personalInfo.shortDescription) {
      // 解析地区字符串，获取省市区信息
      const regionParts = personalInfo.shortDescription.split('-');
      if (regionParts.length === 3) {
        const [shengName, shiName, xianName] = regionParts;
        
        // 查找对应的省市区ID
        const province = this.data.provinceList.find(p => p.name === shengName);
        const city = this.data.cityList.find(c => c.name === shiName);
        const district = this.data.districtList.find(d => d.name === xianName);
        
        if (province && city && district) {
          const regionData = {
            sessionID: wx.getStorageSync('USER_SESSIONID'),
            shortDescription: personalInfo.shortDescription,
            memberID: wx.getStorageSync('USER_MEMBER').memberID,
            shengID: province.cityID,
            shengName: province.name,
            shiID: city.cityID,
            shiName: city.name,
            xianID: district.cityID,
            xianName: district.name
          };
          promises.push(ports.ModuleAll.updateMyMemberShengShXianZhen(regionData));
        } else {
          // 如果找不到对应的ID，使用简化版本
          const regionData = {
            sessionID: wx.getStorageSync('USER_SESSIONID'),
            shortDescription: personalInfo.shortDescription,
            memberID: wx.getStorageSync('USER_MEMBER').memberID
          };
          promises.push(ports.ModuleAll.updateMyMemberShengShXianZhen(regionData));
        }
      } else {
        // 如果地区格式不正确，使用简化版本
        const regionData = {
          sessionID: wx.getStorageSync('USER_SESSIONID'),
          shortDescription: personalInfo.shortDescription,
          memberID: wx.getStorageSync('USER_MEMBER').memberID
        };
        promises.push(ports.ModuleAll.updateMyMemberShengShXianZhen(regionData));
      }
    }
    
    // 提交生日
    if (personalInfo.birthdayStr) {
      const birthday = personalInfo.birthdayStr.split(' ')[0];
      const birthdayPromise = new Promise((resolve, reject) => {
        wx.request({
          url: `${app.baseUrl}/updateMyMemberBirthday?sessionID=${wx.getStorageSync('USER_SESSIONID')}&birthday=${birthday}`,
          method: 'POST',
          success: (res) => {
            if (res.data?.header?.code === 0) {
              resolve(res);
            } else {
              reject(res);
            }
          },
          fail: reject
        });
      });
      promises.push(birthdayPromise);
    }

    // 等待所有请求完成
    Promise.all(promises).then(results => {
      wx.hideLoading();
      
      wx.showToast({
        title: '个人信息完善成功',
        icon: 'success'
      });
      
      // 更新本地存储的用户信息
      const member = wx.getStorageSync('USER_MEMBER');
      const updatedMember = {
        ...member,
        titleID: personalInfo.titleID,
        shortDescription: personalInfo.shortDescription,
        birthdayStr: personalInfo.birthdayStr
      };
      wx.setStorageSync('USER_MEMBER', updatedMember);
      
      // 关闭弹窗
      this.closePersonalInfoModal();
      
      // 继续之前的操作（报名或付款）
      this.continuePreviousAction();
    }).catch(error => {
      wx.hideLoading();
      wx.showToast({
        title: '提交失败',
        icon: 'none'
      });
    });
  },

  // 继续之前的操作
  continuePreviousAction() {
    // 这里可以根据需要继续之前的报名或付款操作
    // 由于我们已经完善了个人信息，可以继续之前的操作
    const member = wx.getStorageSync('USER_MEMBER');
    if (this.checkPersonalInfo(member)) {
      // 如果个人信息已完善，可以继续之前的操作
      // 这里需要根据具体场景来判断是继续报名还是付款
      // 暂时不做自动继续，让用户手动操作
    }
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 空函数，用于阻止事件冒泡
  },
})