<view class="xiang<PERSON>">
	<!-- 标题 -->
  <view class="biaotibox">
    <view class="biaoti">{{info.title}}</view>
    <view class="paybiaoti" wx:if="{{!info.myMemberTrainID}}" bindtap="apply">我要报名</view>
    <view class="paybiaoti" style="animation: none;" wx:elif="{{info.myMemberTrainID&&(info.myMemberTrainStatus===10||info.myMemberTrainStatus===15)}}">已经报名</view>
    <view class="paybiaoti" bindtap='payOrder' data-id="{{info.myMemberOrderID}}" wx:elif="{{info.myMemberTrainStatus===1}}">立刻付款</view>  
    <!-- 没购买显示 -->
    <view class="paybiaoti" style="filter: grayscale(100%);" wx:elif="{{info.status===10}}">已结束</view>
    <!-- 没有开始 -->
    <view class="paybiaoti" wx:elif="{{!maionshw}}">敬请期待</view>
  </view>

  <view class="biaotibox" wx:if="{{companyName}}">
	  <view class="biaoti">{{companyName}}</view>
    <!-- <view class="paybiaoti" bind:tap="companyjoin" wx:if="{{!info.myMemberTrainID}}">学生报名</view> -->
    <view class="" wx:if="{{nostu == 1}}"></view>
    <view class="paybiaoti" bind:tap="companyjoin" wx:elif="{{!info.myMemberTrainID}}">学生报名</view>
  </view>
  
	<!-- 详情图 -->
	<view class="xiangqingimg">
		<image mode="aspectFill" src="{{info.faceImage?info.faceImage:logoImg}}"></image>
	</view>
	<view class="motesi">
		<!-- 价格 -->
		<view class="rensu doemg">
			<!-- <view class="ren" wx:if="{{RenRMB}}">￥<text class="sdeint">{{RenRMB}}</text></view> -->
			<view class="ren">￥<text class="sdeint">{{trainCompanyPrice?trainCompanyPrice:info.priceCash?info.priceCash:'无'}}</text></view>
		</view>
		<!-- 渠道公司 -->
		<!-- <view class="rensu" wx-if='{{quDaoJ.distributeCompanyName}}'>
			<view class="canyu">渠道公司:</view>
			<view class="ren">{{quDaoJ.distributeCompanyName}}</view>
		</view> -->

		<!-- 主讲 -->
		<!-- <view class="rensu doemg">
			<view class="rensu drint">
				<text class="sdeintd">主讲人：{{info.teacherMemberName?info.teacherMemberName:'无'}}</text>
			</view>
		</view> -->
		<!--线下地址  -->
		<!-- <view class="drint">
			<view class="canyu">地址：{{info.address?info.address:'无'}}</view>
		</view> -->
		<!-- 上课时间 -->
		<view>
			<view class="drint">开始时间：{{info.beginDateStr?info.beginDateStr:'无'}}</view>
		</view>
    <view>
			<view class="drint">结束时间：{{info.endDateStr?info.endDateStr:'无'}}</view>
		</view>
		<!-- 简介 -->
		<view>
			<view class="drint">简介：</view>
		</view>
		<!-- 简介 -->
		<view class="drint">
			<rich-text wx:if="{{info.mobileDescription}}" class="ql-editor" nodes="{{info.mobileDescription}}"></rich-text>
      <text wx:else>无</text>
		</view>
		<!-- 有购买显示 -->
		<!-- <view class="modianbnt" wx:if="{{info.myMemberOrderPayStatus===null||info.myMemberOrderPayStatus===1}}" bindtap="apply">我要报名</view>
    <view class="modianbnt" wx:if="{{info.myMemberOrderPayStatus===0}}">立刻付款</view>
		<view class="modianbnt" style="animation: none;" wx:if="{{info.myMemberOrderPayStatus===4}}">已经报名</view> -->
    
	</view>
</view>

<!-- 个人信息完善弹窗 -->
<view class="personal-info-modal" wx:if="{{showPersonalInfoModal}}" bindtap="closePersonalInfoModal">
  <view class="modal-content" catchtap="stopPropagation">
    <view class="modal-header">
      <text class="modal-title">完善个人信息</text>
      <text class="modal-close" bindtap="closePersonalInfoModal">×</text>
    </view>
    
    <view class="modal-body">
      <!-- 性别选择 -->
      <view class="form-item">
        <text class="form-label">性别 *</text>
        <picker bindchange="onGenderChange" value="{{genderIndex}}" range="{{genderArray}}">
          <view class="picker-value">
            {{genderArray[genderIndex] || '请选择性别'}}
          </view>
        </picker>
      </view>
      
      <!-- 地区选择 -->
      <view class="form-item">
        <text class="form-label">地区 *</text>
        <picker mode="multiSelector" 
                bindchange="onRegionChange" 
                bindcolumnchange="onRegionColumnChange" 
                value="{{currentPickerValue}}" 
                range="{{[provinceList, cityList, districtList]}}" 
                range-key="name">
          <view class="picker-value">
            {{selectedRegion || '请选择地区'}}
          </view>
        </picker>
      </view>
      
      <!-- 生日选择 -->
      <view class="form-item">
        <text class="form-label">生日 *</text>
        <picker mode="date" 
                value="{{birthday}}" 
                bindchange="onBirthdayChange" 
                start="{{startTime}}" 
                end="{{endTime}}">
          <view class="picker-value">
            {{birthday || '请选择生日'}}
          </view>
        </picker>
      </view>
    </view>
    
    <view class="modal-footer">
      <button class="btn-cancel" bindtap="closePersonalInfoModal">取消</button>
      <button class="btn-submit" bindtap="submitPersonalInfo">提交</button>
    </view>
  </view>
</view>

<van-popup show="{{ showPrice }}" round close-on-click-overlay="false"
    bind:close="onClose">
<!-- bind:tap="onClose" -->
  <view class="tryoutbox">
    <van-radio-group value="{{ selectedId }}" bind:change="onCouponChange">
    <view class="tryoutbox">
      <view wx:for="{{ MemberBonusList }}" wx:key="id">
        <!-- <view>获取{{item.getTimeStr}}-结束{{item.endDateStr}}</view> -->
        <van-cell
          clickable
          title="{{ item.bonusName }} - ￥{{ item.amount }}"
        >
          <van-radio name="{{ item.memberBonusID }}" slot="right-icon" />
        </van-cell>
      </view>
    </view>
    <button bind:tap="newapply" data-oid="{{info.myMemberOrderID}}" 
        data-tid="{{info.myMemberTrainID}}">确认支付</button>
  </van-radio-group>
  </view>
</van-popup>