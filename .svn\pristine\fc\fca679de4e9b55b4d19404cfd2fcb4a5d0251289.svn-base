import {
    request
} from '../request.js'


//通用接口  
var ModuleAll = {};

function removeNullValues(obj) {
    for (var key in obj) {
        if (obj[key] == '' || obj[key] === null || obj[key] === undefined || obj[key] == 'null') {
            delete obj[key]
        } else if (typeof obj[key] === 'object' && Object.keys(obj[key]).length === 0) {
            delete obj[key];
        }
    }
    return obj;
}

function AD(url, data) {
    if (!data.sessionID && wx.getStorageSync('USER_SESSIONID')) {
        data.sessionID = wx.getStorageSync('USER_SESSIONID')
    }
    data = removeNullValues(data)
    return request(url, data, 'POST', 'application/x-www-form-urlencoded')
}

function ADC(url, data) {
    return request(url, data, 'GET', 'application/json')
}
//promise封装微信自带login
ModuleAll.loginWX = () => {
    return new Promise(function (reslove, reject) {
        wx.login({
            success(res) {
                reslove(res);
            },
            fail(err) {
                reject(err)
            }
        });
    });
}
//promise封装微信自带getSystemInfo
ModuleAll.getSystemInfo = () => {
    return new Promise(function (reslove, reject) {
        wx.getSystemInfo({
            success(res) {
                reslove(res);
            },
        });
    });
}

ModuleAll.submitOneObjectBrowse = (objectDefineID, objectID, objectName) => {
    let params = {
        // applicationID:app.applicationID,
        // siteID:app.siteID,
        objectDefineID,
        objectID,
        objectName
    }
    AD('submitOneObjectBrowse.json', params)
}
ModuleAll.submitOneObjectBrowse1 = data => AD("submitOneObjectBrowse", data)
//刷新培训的会员订阅记录
ModuleAll.refreshMemberBuyReadFromTrain = data => AD("refreshMemberBuyReadFromTrain", data)
//判断是否可立刻进入实训舱
ModuleAll.isCanInOneMachineByAppointment = data => AD("isCanInOneMachineByAppointment", data)
//结束一个会员学习记录
ModuleAll.doneOneMemberStudy = data => AD("doneOneMemberStudy", data)
//删除一个会员学习记录
ModuleAll.deleteOneMemberStudy = data => AD("deleteOneMemberStudy", data)
//开始一个会员学习记录
ModuleAll.beginOneMemberStudy = data => AD("beginOneMemberStudy", data)
//创建一个会员学习记录
ModuleAll.ceateOneMemberStudy = data => AD("ceateOneMemberStudy", data)
//获取培训课时列表
ModuleAll.getTrainSectionList = data => AD("getTrainSectionList", data)
//获取段落列表
ModuleAll.getSectionList = data => ADC("getSectionList", data)
//获取段落列表
ModuleAll.getSectionDetail = data => ADC(data, null)
//获取一个对象的备注信息列表
ModuleAll.getMemoList = data => AD("getMemoList", data)
//设置地址信息
ModuleAll.updateMyMemberShengShXianZhen = data => AD("updateMyMemberShengShXianZhen", data)
//获取省市县数据列表
ModuleAll.getCityList = (data) => AD('getCityList.json', data)
//开始一次机器使用记录
ModuleAll.operateOneMachine = (data) => AD('operateOneMachine.json', data)
//开始一次机器使用记录
ModuleAll.openOneMachineRecord = (data) => AD('openOneMachineRecord.json', data)
//取消我的会员专业记录
ModuleAll.cancelOneMemberMajor = (data) => AD('cancelOneMemberMajor.json', data)
//申请会员专业
ModuleAll.submitMemberMajor = (data) => AD('submitMemberMajor.json', data)
//获取一个机器命令记录的详情
ModuleAll.getOneMachineCommandDetail = (data) => AD('getOneMachineCommandDetail.json', data)
//获取一个机器仪器的详细信息
ModuleAll.getMachineDetail = (data) => AD('getMachineDetail.json', data)
//自助签到一次预约记录
ModuleAll.signinOneAppointment = (data) => AD('signinOneAppointment.json', data)
ModuleAll.signinOneAppointmentTo3One = (data) => AD('signinOneAppointmentTo3One.json', data)
//快速开门实训舱三旺咖星
ModuleAll.fastOpenDoorOneMachineForSanOne = (data) => AD('fastOpenDoorOneMachineForSanOne.json', data)
//离开签到一次预约记录
ModuleAll.leftOneAppointment = (data) => AD('leftOneAppointment.json', data)
//删除一条预约记录
ModuleAll.deleteOneAppointment = (data) => AD('deleteOneAppointment.json', data)

//获取会员专业列表
ModuleAll.getMyMemberMajorList = (data) => AD('getMyMemberMajorList.json', data)
//获取轮播图
ModuleAll.getFocusPictureList = (data) => ADC('getFocusPictureList.json', data)
//获取会员专业列表
ModuleAll.getCompanyMemberMajorList = (data) => AD('getCompanyMemberMajorList.json', data)
//获取会员专业详情
ModuleAll.getOneMemberMajorDetail = (data) => AD('getOneMemberMajorDetail.json', data)

//获取公告列表
ModuleAll.getNoticeArticleList = (data) => AD('getNoticeArticleList.json', data)

// 获取导航列表
ModuleAll.getNavigatorList = (data) => AD('getNavigatorList.json', data)

// 获取导航推荐文章列表
ModuleAll.getArticlePublishList = (data) => AD('getArticlePublishList.json', data)

// 获取导航文章列表
ModuleAll.getNavigatorArticleList = (data) => ADC('getNavigatorArticleList.json', data)

//获取文章详情
ModuleAll.getArticleMoreDetail = (data) => AD('getArticleMoreDetail.json', data)
//获取文章详情
ModuleAll.getArticlePublishDetail = (data) => AD('getArticlePublishDetail.json', data)
//阅读文章
ModuleAll.readingOneArticle = (data) => AD('readingOneArticle.json', data)
//查询是否阅读文章
ModuleAll.isReadingOneArticle = (data) => AD('isReadingOneArticle.json', data)
//浏览文章
ModuleAll.browseOneArticle = (data) => AD('browseOneArticle.json', data)

//推荐商品
ModuleAll.getGoodsRecommendList = (data) => AD('getGoodsRecommendList.json', data)

//推荐会员
ModuleAll.getAllMemberRecommendList = (data) => AD('getAllMemberRecommendList.json', data)

//创建微信小程序关注信息
ModuleAll.createOneWeixinAppMember = (data) => AD('createOneWeixinAppMember.json', data)

//获取微信小程序关注信息
ModuleAll.queryWeixinAppMember = (data) => AD('queryWeixinAppMember.json', data)

//根据id获取一个关注信息
ModuleAll.getOneWeixinAppMemberDetail = (data) => AD('getOneWeixinAppMemberDetail.json', data)

//修改微信小程序关注信息
ModuleAll.updateWeixinAppMember = (data) => AD('updateWeixinAppMember.json', data)

//独立接口：获取我的预约当前统计数据
ModuleAll.getMyAppointmentStatistics = (data) => AD('getMyAppointmentStatistics.json', data)

//获取版本
ModuleAll.getVersionDetail = (data) => AD('getVersionDetail.json', data)

//获取登录code
ModuleAll.getWeiXinAppOpenId = (data) => AD('getWeiXinAppOpenId.json', data)

// 查询openID存不存在
ModuleAll.getWeixinTokenFromMember = (data) => AD('getWeixinTokenFromMember.json', data)

//提交设备信息
ModuleAll.submitDeviceInfo = (data) => AD('submitDeviceInfo.json', data)

//检查session
ModuleAll.checkSessionIsOK = (data) => AD('checkSessionIsOK.json', data)

//通过wxlogin获取手机号
ModuleAll.getWeixinMemberPhone = (data) => AD('getWeixinMemberPhone.json', data)

//手机号查询是否有会员
ModuleAll.searchMemberByPhone = (data) => AD('searchMemberByPhone.json', data)

//已注册会员直接登录
ModuleAll.openIDorUnionIDFastLogin = (data) => AD('openIDorUnionIDFastLogin.json', data)

//会员登录
ModuleAll.memberLogin = (data) => AD('memberLogin.json', data)
ModuleAll.getOneSensorDetail = (data) => AD('getOneSensorDetail.json', data)
ModuleAll.getStoryRecommendList = (data) => AD('getStoryRecommendList.json', data)

//会员注册
ModuleAll.memberPhoneRegister = (data) => AD('memberPhoneRegister.json', data)

//session过期直接登录
ModuleAll.sessionTimeoutLogin = (data) => AD('sessionTimeoutLogin.json', data)

//已经在其他地方有账号   直接登录的时候绑定openid
ModuleAll.bandingMemberByPhone = (data) => AD('bandingMemberByPhone.json', data)

//获取一个会员的信息
ModuleAll.getOneMemberDetail = (data) => AD('getOneMemberDetail.json', data)

// 会员群参加详情
ModuleAll.getOneMemberGroupDetail = (data) => AD("getOneMemberGroupDetail.json", data)
ModuleAll.deleteOneMemberGroup = (data) => AD("deleteOneMemberGroup.json", data)
ModuleAll.deleteOneMemberGroupJoin = (data) => AD("deleteOneMemberGroupJoin.json", data)

//查询实名
ModuleAll.queryMemberRealNameStatus = (data) => AD('queryMemberRealNameStatus.json', data)
ModuleAll.getCurrentMemberStudyList = (data) => AD('getCurrentMemberStudyList.json', data)
ModuleAll.getTrainingMachineStatistics = (data) => ADC('getTrainingMachineStatistics.json', data)

//提交实名
ModuleAll.applyRealNameVerify = (data) => AD('applyRealNameVerify.json', data)

//获取附件
ModuleAll.getObjectAttachmentList = (data) => AD('getObjectAttachmentList.json', data)

//签署空白协议
ModuleAll.signOneBlankAgreement = (data) => AD('signOneBlankAgreement.json', data)

//空白协议绑定
ModuleAll.registerSignOneAgreement = (data) => AD('registerSignOneAgreement.json', data)

//上传对象的附件
ModuleAll.submObjectAttachment = (data) => AD('submObjectAttachment.json', data)

//获取对象附件
ModuleAll.getObjectAttachmentList = (data) => AD('getObjectAttachmentList.json', data)

//提交一个意见反馈
ModuleAll.submitOneFeed = (data) => AD('submitOneFeed.json', data)

//获取意见反馈列表
ModuleAll.getMyFeedList = (data) => AD('getMyFeedList.json', data)

//获取意见反馈回复列表
ModuleAll.getOneFeedBackList = (data) => AD('getOneFeedBackList.json', data)

//阅读反馈回复
ModuleAll.readOneFeedback = (data) => AD('readOneFeedback.json', data)

//获取邀请好友二维码
ModuleAll.getWeixinAppQrcode = (data) => AD('getWeixinAppQrcode.json', data)

//获取邀请好友列表
ModuleAll.getMemberRecommendList = (data) => AD('getMemberRecommendList.json', data)

//修改会员昵称
ModuleAll.updateMyMemberShortname = (data) => AD('updateMyMemberShortname.json', data)

//修改会员头像
ModuleAll.updateMyMemberAvatar = (data) => AD('updateMyMemberAvatar.json', data)

//修改会员环信时间
ModuleAll.updateOneMember = (data) => AD('updateOneMember.json', data)

//获取分类定义列表
ModuleAll.getCategoryList = (data) => AD('getCategoryList.json', data)

//获取故事列表
ModuleAll.getStoryList = (data) => AD('getStoryList.json', data)

//获取故事列表
ModuleAll.getAllStoryCategoryList = (data) => AD('getAllStoryCategoryList.json', data)

//获取一个故事详情
ModuleAll.getStoryDetail = (data) => AD('getStoryDetail.json', data)

//获取目录
ModuleAll.getChapterList = (data) => AD('getChapterList.json', data)

//获取故事音频视频
ModuleAll.getStorySectionList = (data) => AD('getStorySectionList.json', data)

//获取一个对象的评论列表
ModuleAll.getObjectDiscussList = (data) => AD('getObjectDiscussList.json', data)

//发布评论
ModuleAll.submitOneDiscuss = (data) => AD('submitOneDiscuss.json', data)

//删除评论
ModuleAll.deleteOneDiscuss = (data) => AD('deleteOneDiscuss.json', data)

//关注一个会员
ModuleAll.submitOneMemberFollow = (data) => AD('submitOneMemberFollow.json', data)

//获取我的会员专业列表
ModuleAll.getMyMemberMajorList = (data) => AD('getMyMemberMajorList.json', data)

//获取搜索定义
ModuleAll.getSearchDefineList = (data) => AD('getSearchDefineList.json', data)

//获取热门搜索
ModuleAll.getHotWordList = (data) => AD('getHotWordList.json', data)

//查询公司
ModuleAll.queryCompanyByNameOrCode = (data) => AD('queryCompanyByNameOrCode.json', data)

//申请公司
ModuleAll.applyOneBusinessCompany = (data) => AD('applyOneBusinessCompany.json', data)

//获取公司详情
ModuleAll.getOneCompanyDetail = (data) => AD('getOneCompanyDetail.json', data)

//获取用户详情
ModuleAll.getOneUserDetail = (data) => AD('getOneUserDetail.json', data)

//获取我的公司和员工
ModuleAll.getMyCompanyAndEmployeeList = (data) => AD('getMyCompanyAndEmployeeList.json', data)

//获取员工角色列表
ModuleAll.getRoleEmployeeList = (data) => AD('getRoleEmployeeList.json', data)

//获取角色菜单
ModuleAll.getRoleFunctionList = (data) => AD('getRoleFunctionList.json', data)

//加入公司
ModuleAll.joinCompanyWithKingID = (data) => AD('joinCompanyWithKingID.json', data)

//生成二维码
ModuleAll.getWeixinAppQrcode = (data) => AD('getWeixinAppQrcode.json', data)

//获取客服列表
ModuleAll.getSiteServicerList = (data) => AD('getSiteServicerList.json', data)

//获取客服列表
ModuleAll.getSiteServicerIsList = (data) => AD('getSiteServicerIsList.json', data)

//获取客服事件列表
ModuleAll.getServiceEventList = (data) => AD('getServiceEventList.json', data)

//创建客服群和客服事件
ModuleAll.applyOneShopSiteService = (data) => AD('applyOneShopSiteService.json', data)

//专家客服
ModuleAll.applyOneOnlineSiteService = (data) => AD('applyOneOnlineSiteService.json', data)


//获取会员群
ModuleAll.getMemberGroupList = (data) => AD('getMemberGroupList.json', data)

//创建会员群
ModuleAll.createOneMemberGroup = (data) => AD('createOneMemberGroup.json', data)

//创建membergroupjoin
ModuleAll.createOneMemberGroupJoin = (data) => AD('createOneMemberGroupJoin.json', data)

//获取会员群成员
ModuleAll.getMemberGroupJoinList = (data) => AD('getMemberGroupJoinList.json', data)

//更新会员群
ModuleAll.updateOneMemberGroup = (data) => AD('updateOneMemberGroup.json', data)

//根据kingid查询公司
ModuleAll.getCompanyIDByKingID = (data) => AD('getCompanyIDByKingID.json', data)

//发送消息
ModuleAll.sendOneTalkMemberMessage = (data) => AD('sendOneTalkMemberMessage.json', data)

//获取tome聊天记录
ModuleAll.getToMeTalkingMessageList = (data) => AD('getToMeTalkingMessageList.json', data)

//获取meto聊天记录
ModuleAll.getMeToTalkingMessageList = (data) => AD('getMeToTalkingMessageList.json', data)

//结束客服事件
ModuleAll.workScoreOneServiceEvent = (data) => AD('workScoreOneServiceEvent.json', data)
//会员对客服问题评分
ModuleAll.askScoreServiceEvent= (data) => AD('askScoreServiceEvent.json', data)
//获取广告内容
ModuleAll.getAdContentByCodeList = (data) => AD('getAdContentByCodeList.json', data)

//修改客服
ModuleAll.updateOneSiteServicer = (data) => AD('updateOneSiteServicer.json', data)
ModuleAll.generateOneMemberTrainPDF = (data) => AD('generateOneMemberTrainPDF.json', data)

//提交举报
ModuleAll.submitOneAgainst = (data) => AD('submitOneAgainst.json', data)
ModuleAll.getCanGetBonusList = (data) => AD('getCanGetBonusList.json', data)

//获取举报列表
ModuleAll.getAgainstList = (data) => AD('getAgainstList.json', data)

//获取举报详情
ModuleAll.getOneAgainstDetail = (data) => AD('getOneAgainstDetail.json', data)

//获取课程列表
ModuleAll.getTrainBusinessList = (data) => AD('getTrainBusinessList.json', data)

//获取课程推荐列表
ModuleAll.getOneStoryRecommendList = (data) => ADC('getOneStoryRecommendList.json', data)

//获取课程详情
ModuleAll.getTrainBusinessDetail = (data) => AD('getTrainBusinessDetail.json', data)
//获取导航详情
ModuleAll.getNavigatorDetail = (data) => AD('getNavigatorDetail.json', data)
//获取预约列表
// ModuleAll.getMachineList = (data) => AD('getAppointmentList', data)
//获取机器列表
ModuleAll.getMachineList = (data) => AD('getMachineList.json', data)

//获取机器详情
ModuleAll.getOneMachineDetail = (data) => AD('getOneMachineDetail.json', data)

//获取TrainCompany列表
ModuleAll.getTrainCompanyList = (data) => AD('getTrainCompanyList.json', data)
//获取MemberTrain列表
ModuleAll.getMemberTrainList = (data) => AD('getMemberTrainList.json', data)
//获取MemberTrain详情
ModuleAll.getOneMemberTrainDetail = (data) => AD('getOneMemberTrainDetail.json', data)
//创建一个会员培训报名记录	
ModuleAll.createOneMemberTrain = (data) => AD('createOneMemberTrain.json', data)

//获取机器信息
ModuleAll.getMachineDayList = (data) => AD('getMachineDayList.json', data)

//获取我的点赞列表
ModuleAll.getMyPraiseList = (data) => AD('getMyPraiseList.json', data)

//点赞一个对象
ModuleAll.submitOnePraise = (data) => AD('submitOnePraise.json', data)

//取消点赞
ModuleAll.cancelOnePraise = (data) => AD('cancelOnePraise.json', data)

//获取收藏列表
ModuleAll.getMyCollectList = (data) => AD('getMyCollectList.json', data)

//收藏一个对象
ModuleAll.submitOneCollect = (data) => AD('submitOneCollect.json', data)

//取消收藏
ModuleAll.deleteOneCollect = (data) => AD('deleteOneCollect.json', data)

//创建预约
ModuleAll.createOneAppointment = (data) => AD('createOneAppointment.json', data)

//获取会员列表
ModuleAll.getMemberList = (data) => AD('getMemberList.json', data)

//获取预约记录列表
ModuleAll.getAppointmentList = (data) => AD('getAppointmentList.json', data)

//获取预约记录详情
ModuleAll.getOneAppointmentDetail = (data) => AD('getOneAppointmentDetail.json', data)

//获取时间段列表
ModuleAll.getTimeIntervalInstanceList = (data) => AD('getTimeIntervalInstanceList.json', data)

////////////下面是和签到积分页面有关的接口
//会员统计数据
ModuleAll.getMyStatisticsData = (data) => AD('getMemberStatisticsWallet', data)
//获取签到节点列表
ModuleAll.getSigninDefineList = (data) => AD('getSigninDefineList', data)
//用户签到初始化
ModuleAll.memberSigninInit = (data) => AD('memberSigninInit', data)
//签到
ModuleAll.memberSignin = (data) => AD('memberSignin', data)
//获取积分规则
ModuleAll.getArticleSimpleDetail = (data) => AD('getArticleSimpleDetail', data)
//获取积分明细列表
ModuleAll.getMyPointList = (data) => AD('getMyPointList', data)
//获取信用分统计数据
ModuleAll.getMemberStatisticsCreditPoint = (data) => AD('getMemberStatisticsCreditPoint', data)
//获取信用分明细列表
ModuleAll.getMemberCreditScoreList = (data) => AD('getMemberCreditScoreList', data)
//获取文章列表（操作手册按钮点进去的
ModuleAll.getArticleList = (data) => AD('getArticleList', data)
//独立接口：系统创建一个会员操作记录
ModuleAll.createOneOperateCode = (data) => AD('createOneOperateCode', data)
// 好友
ModuleAll.getFriendList = (data) => AD("getFriendList.json", data)
// 添加好友
ModuleAll.createOneFriend = (data) => AD("createOneFriend.json", data)
// 删除好友
ModuleAll.deleteOneFriend = (data) => AD("deleteOneFriend.json", data)
// 同意/拒绝 好友请求
ModuleAll.answerOneFriendApply = (data) => AD("answerOneFriendApply.json", data)



// //查询省市县cityIDd
// ModuleAll.queryCityIDbyName = utils.request1("ds", "queryCityIDbyName.json")
// //删除我的公司
// ModuleAll.deleteMyCompany = utils.request1("ds", "deleteMyCompany.json")
// //验证码
// ModuleAll.sendSMSVerifyCode = utils.request1("ds", "sendSMSVerifyCode.json")
// //获取公司职位列表
// ModuleAll.getJobList = utils.request1("ds", "getJobList.json")

////////////////
// 下面有关商城的接口不知为何全部注释，按需启用部分
////////////////

// //获取店铺详情
// ModuleAll.getShopDetail = utils.request1("ds", "getShopDetail.json")
// //创建一个预约记录
// ModuleAll.createOneAppointment = utils.request1("ds", "createOneAppointment.json")

// //获取预约记录列表
// ModuleAll.deleteOneAppointment = utils.request1("ds", "deleteOneAppointment.json")
// //获取单个预约记录详细信息
// ModuleAll.getOneAppointmentDetail = utils.request1("ds", "getOneAppointmentDetail.json")
// //预约签到
// ModuleAll.signinOneAppointment = utils.request1("ds", "signinOneAppointment.json")
// //获取店铺、公司列表
// ModuleAll.getSimpleCompanyList = utils.request1("ds", "getSimpleCompanyList.json")
// //获取品牌列表
// ModuleAll.getBrandList = utils.request1("ds", "getBrandList.json")

// //获取搜索关键词列表
// ModuleAll.getSearchWordsList = utils.request1("ds", "getSearchWordsList.json")
// //获取店铺商品列表
// ModuleAll.getGoodsShopList = utils.request1("ds", "getGoodsShopList.json")

// //获取公司服务范围和发货范围
// ModuleAll.getCompanyCityList = utils.request1("ds", "getCompanyCityList.json")
// //获取公司旗下品牌
// ModuleAll.getCompanyBrandList = utils.request1("ds", "getCompanyBrandList.json")
//获取商品属性
ModuleAll.getAttributeValueList = data => AD("getAttributeValueList", data)
//获取商品详情
ModuleAll.getGoodsShopDetail = data => AD("getGoodsShopDetail", data)
//购物车
ModuleAll.addGoodsToCart = data => AD("addGoodsToCart", data)
//购物车中某商品的数量
ModuleAll.getCartGoodsTotal = data => AD("getCartGoodsTotal", data)
//购物车中的所有东西
ModuleAll.getCartGoodsList = data => AD("getCartGoodsList", data)
//购物车加
//改变购物车中的某商品水量
ModuleAll.updateCartGoodsQTY = data => AD("updateCartGoodsQTY", data)
//购物车删除某商品
ModuleAll.deleteGoodsFromCart = data => AD("deleteGoodsFromCart", data)
//购物车选中商品
ModuleAll.selectCartGoods = data => AD("selectCartGoods", data)
//获取优惠券信息
ModuleAll.getMyMemberBonusList = data => AD("getMyMemberBonusList", data)

ModuleAll.getOneBonus = data => AD("getOneBonus", data)

//创建商品订单
ModuleAll.standardCreateOneMemberOrder = data => AD("standardCreateOneMemberOrder", data)
//微信付款方式
ModuleAll.getWeixinPayInfo = data => AD("getWeixinPayInfo", data)
//获取用户收货地址
ModuleAll.getMemberAddressList = data => AD("getMemberAddressList", data)
//删除收货地址
ModuleAll.deleteMyMemberAddress = data => AD("deleteMyMemberAddress", data)
//获取城市
ModuleAll.getCityList = data => AD("getCityList", data)
//新增收货地址
ModuleAll.createMemberAddress = data => AD("createMemberAddress", data)
//更新收货地址
ModuleAll.updateMemberAddress = data => AD("updateMemberAddress", data)
//获取某个地址的详情
ModuleAll.getOneMemberAddressDetail = data => AD("getOneMemberAddressDetail", data)
//提交用户发票定义信息
ModuleAll.submitOneMemberInvoiceDefine = data => AD("submitOneMemberInvoiceDefine", data)
//编辑用户发票定义信息
ModuleAll.updateMemberInvoiceDefine = data => AD("updateMemberInvoiceDefine", data)
//获取发票定义详情
ModuleAll.getMemberInvoiceDefineDetail = data => AD("getMemberInvoiceDefineDetail", data)
//获取用户发票定义列表
ModuleAll.getMemberInvoiceDefineList = data => AD("getMemberInvoiceDefineList", data)
//获取发票列表
ModuleAll.getInvoiceList = data => AD("getInvoiceList", data)
//删除用户发票定义
ModuleAll.deleteOneMemberInvoiceDefine = data => AD("deleteOneMemberInvoiceDefine", data)
//获取订单列表
ModuleAll.getMultiMemberOrderList = data => AD("getMultiMemberOrderList", data)
//取消某订单
ModuleAll.cancelOneMemberOrder = data => AD("cancelOneMemberOrder", data)
//获取付款信息
ModuleAll.getWeixinPayInfo = data => AD("getWeixinPayInfo", data)
//订单退货
ModuleAll.applyReturnOneMemberOrder = data => AD("applyReturnOneMemberOrder", data)
//订单收获
ModuleAll.getGoodsOneMemberOrder = data => AD("getGoodsOneMemberOrder", data)
//订单详情
ModuleAll.getOneMemberOrderDetail = data => AD("getOneMemberOrderDetail", data)
//物流详情
ModuleAll.getExpressLogisticsInfo = data => AD("getExpressLogisticsInfo", data)
//品牌
ModuleAll.getBrandList = data => AD("getBrandList", data)
//蓝牙使用1次手机设备获取mac地址
ModuleAll.useOneDevice = data => AD("useOneDevice", data)
//获取实训仓detail ！不是上面的getOneMachineDetail

//创建一个证书
ModuleAll.createOneAgreement = data => AD('createOneAgreement', data);

//获取我的证书列表
ModuleAll.getMyAgreementList = data => AD('getMyAgreementList.json', data);

//获取证书详情
ModuleAll.getAgreementDetail = data => AD('getAgreementDetail.json', data);

//更新证书信息
ModuleAll.updateOneAgreement = data => AD('updateOneAgreement.json', data);

//删除证书
ModuleAll.deleteOneAgreement = data => AD('deleteOneAgreement.json', data);

//新增考试
ModuleAll.createOneExamApply = data => AD('createOneExamApply.json', data);

//获取考试申请列表
ModuleAll.getExamApplyList = data => AD('getExamApplyList.json', data);

//获取考试申请详情
ModuleAll.getOneExamApplyDetail = data => AD('getOneExamApplyDetail.json', data);

//更新考试申请信息
ModuleAll.updateOneExamApply = data => AD('updateOneExamApply.json', data);

//删除考试申请
ModuleAll.deleteOneExamApply = data => AD('deleteOneExamApply.json', data);

//撤销考试申请
ModuleAll.cancelOneExamApply = data => AD('cancelOneExamApply.json', data);

//申请考试
ModuleAll.applyOneExamApply = data => AD('applyOneExamApply.json', data);

//创建补贴申请
ModuleAll.createOneGovernmentSubsidiesApply = data => AD('createOneGovernmentSubsidiesApply.json', data);

//获取补贴列表
ModuleAll.getGovernmentSubsidiesApplyList = data => AD('getGovernmentSubsidiesApplyList.json', data);

//获取补贴详情
ModuleAll.getOneGovernmentSubsidiesApplyDetail = data => AD('getOneGovernmentSubsidiesApplyDetail.json', data);

//修改补贴信息
ModuleAll.updateOneGovernmentSubsidiesApply = data => AD('updateOneGovernmentSubsidiesApply.json', data);

//删除一个补贴信息
ModuleAll.deleteOneGovernmentSubsidiesApply = data => AD('deleteOneGovernmentSubsidiesApply.json', data)

//申请一个补贴
ModuleAll.applyOneGovernmentSubsidiesApply = data => AD('applyOneGovernmentSubsidiesApply.json', data);

//撤销一个补贴
ModuleAll.cancelOneGovernmentSubsidiesApply = data => AD('cancelOneGovernmentSubsidiesApply.json', data);

//获取会员钱包统计数据
ModuleAll.getMemberStatisticsWallet = data => AD("getMemberStatisticsWallet", data)
//获取会员资金变动余额记录列表
ModuleAll.getMemberBalanceList = data => AD("getMemberBalanceList.json", data)
//获取会员收入列表
ModuleAll.getMemberIncomeList = data => AD("getMemberIncomeList.json", data)
//获取会员提现明细
ModuleAll.getMemberWithdrawList = data => AD("getMemberWithdrawList.json", data)
//获取会员提现统计数据
ModuleAll.getMemberWithdrawTotal = data => AD("getMemberWithdrawTotal.json", data)
//获取提现定义列表
ModuleAll.getWithdrawDefineList = data => AD("getWithdrawDefineList.json", data)
//获取我的会员银行卡列表
ModuleAll.soaQueryBankCard = data => AD("getMemberBankList.json", data)
//获取会员收入详细信息
ModuleAll.getMemberIncomeInfo = data => AD("getMemberIncomeInfo.json", data)
//会员申请提现
ModuleAll.applyOneMemberWithdraw = data => AD("applyOneMemberWithdraw.json", data)
//删除我的会员银行卡-解除绑定	
ModuleAll.deleteOneMemberBank = data => AD("deleteOneMemberBank.json", data)
//获取银行列表
ModuleAll.getBankList = data => AD("getBankList.json", data)
//创建一个新的会员银行卡
ModuleAll.submitOneMemberBank = data => AD("submitOneMemberBank.json", data)

//获取传感器列表
ModuleAll.getSensorList = data => AD("getSensorList.json", data)
// //获取我关注的会员列表
// ModuleAll.getMyMemberFollowList = utils.request1("ds", "getMyMemberFollowList.json")
// //获取关注我的会员列表
// ModuleAll.getFollowMeMemberFollowList = utils.request1("ds", "getFollowMeMemberFollowList.json")
// //取消关注
// ModuleAll.deleteOneMemberFollow = utils.request1("ds", "deleteOneMemberFollow.json")
// //提交关注
// ModuleAll.submitOneMemberFollow = utils.request1("ds", "submitOneMemberFollow.json")
// //获取收藏列表
// ModuleAll.getMyCollectList = utils.request1("ds", "getMyCollectList.json")
// //提交收藏
// ModuleAll.submitOneCollect = utils.request1("ds", "submitOneCollect.json")
//取消收藏
ModuleAll.removeOneObjectCollect = data => AD("removeOneObjectCollect", data)
//退出登录，清除session
ModuleAll.loginOut = data => AD("loginOut", data)
//萤石云获取下载链接
ModuleAll.getYSRecordVideoDownloadURL = data => AD("getYSRecordVideoDownloadURL", data)
//修改性别
ModuleAll.updateMyMemberTitle = data => AD("updateMyMemberTitle ", data)
//当前手机号 是否 存在1个学生
ModuleAll.queryOneStudentByPhone = data => AD("queryOneStudentByPhone", data)
//发送1个消息
ModuleAll.sendOneMessage = data => AD("sendOneMessage", data)
//获取消息列表
ModuleAll.getMessageList = data => AD("getMessageList", data)
//获取我的接收消息列表
ModuleAll.getMyReceivedMessageList = (data) => AD('getMyReceivedMessageList.json', data)

//绑定学生
ModuleAll.bandingOneStudent = data => AD("bandingOneStudent", data)
//通过萤石云停止录制摄像头视频
ModuleAll.beginYSRecordVideo = data => AD("beginYSRecordVideo", data)
//通过萤石云停止录制摄像头视频
ModuleAll.endYSRecordVideo = data => AD("endYSRecordVideo", data)
ModuleAll.updateMyMemberInfo = data =>AD('updateMyMemberInfo',data)
ModuleAll.updateMyMemberBirthday = data => AD('updateMyMemberBirthday', data)
// 创建转发记录
ModuleAll.createOneForward = data => AD('createOneForward',data)
// //批量取消收藏
// ModuleAll.batchDeleteMyCollect = utils.request1("ds", "batchDeleteMyCollect.json")

// //浏览商品
// ModuleAll.submitOneObjectBrowse = utils.request1("ds", "submitOneObjectBrowse.json")
// //获取浏览商品列表
// ModuleAll.getMyGoodsShopBrowseList = utils.request1("ds", "getMyGoodsShopBrowseList.json")
// //获取公司分类
// ModuleAll.getCompanyCategoryList = utils.request1("ds", "getCompanyCategoryList.json")
// //获取一个海报图片
// ModuleAll.getUIPageImage = utils.request1("d","getUIPageImage.json")

//下拉刷新
ModuleAll.PullDownRefresh = function (that, fun, ffn) {
    setTimeout(() => {
        wx.stopPullDownRefresh();
    }, 750)

    wx.showLoading({
        title: '加载中',
        mask: true
    })
    that.setData({
        showAjaxLoad: false,
        showAjaxNomore: false,
        currentPage: 1
    })
    if (fun) fun()
    setTimeout(function () {
        wx.hideLoading()
    }, 1000)

}

//上拉加载 
ModuleAll.ReachBottom = function (name, that, fun, ffn) {
    name = name + "Page"
    var page = that.data[name];
    name = name + ".currentPage"
    if (page.hasNextPage) {
        var currentPage = page.currentPage ? page.currentPage : 1;
        currentPage++;
        that.setData({
            showAjaxLoad: true,
            [name]: currentPage
        })
        if (fun) fun()
    } else {
        that.setData({
            showAjaxNomore: true
        })
    }
}

// //上传文件
// ModuleAll.uploadFile = function (data, that, fun, ffn) {
//   wx.showToast({
//     title: '上传中...',
//     icon: 'loading',
//     mask: true
//   })
//   wx.uploadFile({
//     url: app.dServerUrl + 'uploadFile.json',
//     filePath: data,
//     name: 'file',
//     dataType: 'json',
//     success(res) {
//       wx.hideToast()
//       if (res.data) {
//         res.data = JSON.parse(res.data)
//       }
//       if (fun) fun(res)
//     },
//     fail(err) {
//       wx.hideToast()
//       if (ffn) ffn(err)
//     }
//   })
// }


module.exports = ModuleAll