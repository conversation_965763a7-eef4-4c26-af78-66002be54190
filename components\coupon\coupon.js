const ports = require("../../utils/ports")
const app = getApp();

Component({

    /**
     * 组件的属性列表
     */
    properties: {

    },

    /**
     * 组件的初始数据
     */
    data: {
        scrollLeft: 0,
        isAutoScrolling: true,
        startX: 0,
        slideThreshold: 10
    },
    timer: null,
    lifetimes: {
        attached: function () {
            this.getCouponList();
        },
    },
    /**
     * 组件的方法列表
     */
    methods: {
        init() {
            this.getCouponList();
        },
        getCouponList() {
            ports.ModuleAll.getCanGetBonusList({
                applicationID: app.applicationID,
                pageNumber: 999,
                sortTypeTime: 1
                // companyID: app.companyID,
            }).then(res => {
                console.log(res, "红包红包");
                let data = res.data.body.data.rows;
                //可领取
                data.btnStatus = 0
                this.setData({
                    dataList: data
                })
                // this.startAutoScroll()
            })
        },

        async get(e) {
            const {
                bonusid,
                info
            } = e.currentTarget.dataset;
            console.log('ports.ModuleAll:', ports.ModuleAll);
            await ports.ModuleAll.getOneBonus({
                bonusID: bonusid
            }).then(res => {
                console.log(res, "领取红包");
                if (res.data.header.code !== 0) {
                  wx.showToast({
                      title: '领取失败',
                      icon: 'none',
                  })
                    return
                }
                wx.showToast({
                    title: '领取成功',
                    icon: 'none',
                })
                this.getCouponList();
            })
        },
    }
})