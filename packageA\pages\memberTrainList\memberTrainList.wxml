<!--packageA/pages/memberTrainList/memberTrainList.wxml-->
<view>
    <view class="noText" wx:if="{{memberTrainList.length === 0}}">暂无数据</view>
    <view wx:else>
        <view class="item flex-column justify-around" wx:for="{{memberTrainList}}" wx:key="memberTrainID">
        <!-- bindtap="href" data-item="{{item}}" 注释掉跳转功能，暂时不需要-->
            <view class="item-title" wx:if="{{type==1}}">
                <text wx:if="{{item.trainBusinessName}}">{{item.trainBusinessName}}</text>
                <text class="item-text price">价格：{{item.priceCash}}</text>
            </view>
            <view class="item-title" wx:elif="{{type==2}}">{{item.trainCourseName}}</view>
            <view class="item-title" wx:elif="{{type==3}}">{{item.trainHourName}}</view>


            <view wx:if="{{timeType==1}}">
                <view class="item-text">培训开始日期：{{item.beginDateStr}}</view>
                <view class="item-text">培训结束日期：{{item.endDateStr}}</view>
            </view>
            <view wx:elif="{{timeType==2}}">
                <view class="item-text">上课日期：{{item.beginDateStr}}</view>
            </view>
            <view wx:elif="{{timeType==3}}">
                <view class="item-text">上课日期：{{item.beginDateStr}}</view>
                <view class="item-text">结束日期：{{item.endDateStr}}</view>
            </view>



            <view class="item-text" style="margin: 20rpx 0;">报名时间：{{item.applyTimeStr?item.applyTimeStr:""}}</view>
            <!-- 价格放到了标题右侧 -->
            <!-- <view class="item-text">价格：{{item.priceCash}}</view> -->
            <!-- <view class="item-text">付款状态：{{item.payStatusText}}</view> -->
            <view class="item-text" style="display: flex;justify-content: space-between;">
              <text>状态：{{item.statusText}}</text>
              <button style="display: flex;margin: 0!important;" wx:if="{{item.status == 1 && item.payStatus == 0}}" bindtap='payOrder' data-id="{{item.memberOrderID}}">未付款</button>
            </view>
            <button catch:tap="btn" data-trainid="{{item.memberTrainID}}" wx:if="{{item.statusText != '新'}}">下载学习档案</button>
            <!--<view wx:if="{{item.trainCompanyName}}" class="item-text">培训公司: {{item.trainCompanyName}}</view>-->
            <view wx:if="{{item.governmentCompanyName}}" class="item-text">人社机构: {{item.governmentCompanyName}}</view>
        </view>
    </view>

</view>