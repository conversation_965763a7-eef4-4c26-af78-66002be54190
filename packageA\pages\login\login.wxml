<view style="{{mainColor}}">
  <view class="container">
    <view class="logoBox">
      <view class="imgBox">
        <image class="imgItem" src="{{logoimg}}"></image>
      </view>
    </view>
    <view class="auth-title">{{weixinAppTitle}}</view>
    <view class="auth-title">申请授权登录</view>
    <view class="agreementBox flex-row justify-center">
      <checkbox-group bindchange="checkboxChange">
        <label class="checkbox">
          <checkbox checked="{{agreementFlag}}" class="checkbox" />
        </label>
      </checkbox-group>
      <view class="checkText">
        同意并接受<span data-type='0' bindtap="href">《隐私协议》</span>和<span data-type='1' bindtap="href">《注册协议》</span>
      </view>
    </view>
    <view class="flex-row">
      <button class="backBtn login-btn" bindtap="backHome">取消/拒绝</button>
      <button class="logBtn login-btn" style="opacity: {{disabled?'0.7':'1'}};" disabled="{{disabled}}" open-type="getPhoneNumber" bindgetphonenumber="getPhoneNumber">授权登录</button>
    </view>
  </view>

  <!-- 注册  -->
  <!-- catchtouchmove="ture" 解决遮罩层滚动穿透问题 -->
  <view wx:if="{{showRegister}}" class="maskBg" catchtouchmove="ture">
    <view class='setInfoBox'>
      <view class='title'>设置您的注册信息</view>
      <view class='content flex-column align-center'>
        <view class='imgBox'>
          <image class='imgItem' src='/assets/images/mine_defaultImg.png'></image>
        </view>
        <view class="form-container">
          <view class="form-item">
            <view class="reminder_title">* 以英文字母或者汉字开头。限4-16个字符，一个汉字为2个字符</view>
            <input class="nameInput" auto-focus placeholder="请输入昵称" bindinput="bindNameInput" placeholder-style="color:#fff" />
          </view>

          <view class="form-item">
            
            <picker class="form-picker" bindchange="bindGenderChange" value="{{genderIndex}}" range="{{genderArray}}" style="display:block;line-height: 70rpx;">
              <view class="picker-display">
                <text wx:if="{{genderIndex >= 0}}">{{genderArray[genderIndex]}}</text>
                <text wx:else class="placeholder">请选择性别</text>
              </view>
            </picker>
          </view>

          <view class="form-item">
        
            <picker class="form-picker" mode="date" value="{{member.birthday}}" bindchange="bindBirthdayChange" start="{{startTime}}" end="{{endTime}}" style="display:block;line-height: 70rpx;">
              <view class="picker-display">
                <text wx:if="{{member.birthday}}">{{member.birthday}}</text>
                <text wx:else class="placeholder">请选择出生年月日</text>
              </view>
            </picker>
          </view>

          <view class="form-item">
            
            <picker class="form-picker" mode="multiSelector" bindchange="MyNativeplace" bindcolumnchange="PickerChange" value="{{currentPickerValue}}" range="{{Myshenglsit}}" range-key="name" style="display:block;line-height: 70rpx;">
              <view class="picker-display">
                <text wx:if="{{shengobj}}">{{shengobj}}</text>
                <text wx:else class="placeholder">请选择所在地区</text>
              </view>
            </picker>
          </view>
        </view>

        <button class='ask_btn' bindtap="toRegister">注册</button>
      </view>
    </view>
  </view>
</view>