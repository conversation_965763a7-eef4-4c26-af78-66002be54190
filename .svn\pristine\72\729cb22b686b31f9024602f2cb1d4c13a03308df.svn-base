<!--packageA/pages/machineListNew/machineListNew.wxml-->
<view>
  <view class="noText" wx:if="{{machineList.length === 0}}">暂无数据</view>
  <view wx:else>
    <view class="search-container">
    <input 
      type="text" 
      value="{{keyWords}}" 
      placeholder="请输入搜索内容" 
      bindinput="onSearchInput" 
      class="search-input"
    />
    <button type="primary" size="mini" bindtap="getMachineList" class="search-button">搜索</button>
    </view>
    <view class="item flex-column justify-around" wx:for="{{machineList}}" wx:key="id" bindtap="{{item.currentStatus ? 'checkMachine':''}}" data-id="{{item.id}}" data-name="{{item.name}}" data-distance="{{item.distanceReal}}" data-biztype="{{item.bizType}}" data-code="{{item.code}}" data-address="{{item.address}}">
      <view class="item-title flex-row justify-between">
        <text>{{item.name}}</text>
        <text>{{item.code}}</text>
      </view>
      <view class="item-text">地址：{{item.address}} {{item.distance?"（距离"+item.distance+"）":''}}</view>
      <view class="item-text">在线状态：{{item.onlineType==1?'在线':item.onlineType==2?'离线':'未知'}}
      <button type="primary" size="mini" class="bookingBtn" wx:if="{{item.currentStatus}}">选择</button></view>
      <view class="item-text ">
      <!-- <text>课程名称：{{item.trainBusinessName || '无'}}</text> -->
      </view>
      <view class="item-text flex-row justify-between">
      <!-- <text decode="true">使用状态：{{item.runType==4?'培训中':(item.runType==null || item.runType==1 )?'未&nbsp;&nbsp;&nbsp;&nbsp;知':'空闲中'}}</text> -->
      <!-- <button type="primary" size="mini" class="bookingBtn" catch:tap="appointment" data-item="{{item}}" wx:if="{{item.isShow}}">预约</button> -->
      </view>
      <!-- <button class="btn-sm" catchtap="appointment" data-item="{{item}}">预约</button> -->
    </view>
  </view>

</view>