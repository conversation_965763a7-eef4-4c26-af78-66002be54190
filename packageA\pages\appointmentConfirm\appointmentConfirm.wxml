<!-- packageA/pages/appointmentConfirm/appointmentConfirm.wxml -->
<view class="mainUse">
  <scroll-view scroll-y style="height: 100%;">
  <view class="guid-location-box">
    <view class="location-box page-box flex-a-c">
      <!-- 选择城市 -->
      <view style="display: flex;padding-top: 25rpx;padding-bottom: 32rpx;">
        <view class="city-box cloum-box">
          <view class="text-small">城市</view>
          <view bindtap="Cityshow" class="text-blod" wx:if="{{!city.name}}">
            {{isLocating?"定位中":"定位失败"}}
          </view>
          <view bindtap="Cityshow" class="text-blod" wx:else>{{ city.name }}</view>
        </view>
        <view class="sx-box cloum-box">
          <view class="text-small">实训舱</view>
          <view class="text-blod flex-a-c" bind:tap="toHref">
            <view>
              {{checkedMachine.machineName?checkedMachine.machineName:'选择实训舱'}}
            </view>
            <image  class="icon" src='/assets/icon/arrow_right.png'></image>
          </view>
        </view>
        <view class="guid-box icon-box flex-a-c" bind:tap="toHelp">
        <image class="icon" src="/assets/icon/article_b.png" mode="widthFix"></image>
        <text style="margin-left: 5rpx;">指引</text>
        </view>


        <!-- <view class="loc-box">
          <view class="loca">
            <view class="loc-img">
              <image class="img" src="/assets/icon/ic_l.png" mode='aspectFill'></image>
            </view>
            <view class="text-small">
              {{distance==9999?'定位失败': '距您'+distance+'m'}}
            </view>
          </view>
        </view> -->
      </view>
    </view>
  </view>
    <!-- 确认预约 new -->
    <!-- <view class="check-item">
    <view class="box">
      <view class="title flex-a-c">
        <image class="img" mode="scaleToFill" src="/assets/icon/bell.png" mode="" />
        <text>请仔细核对预约信息</text>
      </view>
      <view class="content">
        <view class="line-item flex-a-c">
          <view>时间：</view>
          <view class="flex-1">
            <text>{{nextBeginTime}}</text>
            <text>→</text>
            <text>{{nextEndTime}}</text>
          </view>
          <image class="img" mode="scaleToFill" src="/assets/icon/edit.png" mode="" bind:tap="chooseTime" />
        </view>
        <view class="line-item flex-a-c">
          <text>地址：</text>
          <view class="flex-1">{{checkedMachine.machineName}}</view>
          <image class="img" mode="scaleToFill" src="/assets/icon/edit.png" mode="" bind:tap="toHref" />
        </view>
        <view class="line-item flex-a-c">
          <view>好友：</view>
          <view class="flex-1">xxx</view>
          <image class="img" mode="scaleToFill" src="/assets/icon/edit.png" mode="" bind:tap="chooseTime" />
        </view>
        <view class="line-item flex-a-c">
          <view>内容：</view>
          <view class="flex-1">{{workTypeStr}}</view>
          <image class="img" mode="scaleToFill" src="/assets/icon/edit.png" mode="" bind:tap="chooseWorkType" />
        </view>
      </view>
    </view>
  </view> -->
    <view class="page-box">
      <view class="check-item">
        <view class="block2">
          <!-- 预约时段 -->
          <view class="title-box flex-a-c">
            <view class="text-blod">预约信息</view>
            <view class="edit-box flex-a-c" bind:tap="handleEditClick" hidden="{{isEdit}}">
              <image class="icon" src='/assets/icon/edit_b.png' bindtap="deleteFriend2"/>
              <view>修改预约</view>
            </view>
          </view>
          <view class="canUseTime flex-a-c" bind:tap="{{isEdit ? 'chooseTime' :''}}">
            <view class="flex-1">
              <view style=" display: flex;">
                <text class="text text6">预约时段：</text>
              </view>
              <view wx:if="{{nextBeginTime}}" style="display: flex;justify-content: space-between;">
                <view>
                  <text class="text text7">{{nextBeginTime}}</text>
                  <image class="text text8" src="/assets/images/ic_arrow.png" style="width: 90rpx;height: 13rpx;" />
                  <text class="text text9">{{nextEndTime}}</text>
                </view>
              </view>
              <view wx:else>
                <text class="text text7">今日无可用预约时段，请选择其他时段</text>
              </view>
            </view>
            <image hidden="{{!isEdit}}" class="icon" src='/assets/icon/arrow_right.png'></image>
          </view>

          <view class="address-box line-box flex-a-c">
            <view class="flex-1">
              <view class="text6">{{checkedLearnTypeName}}地点：</view>
              <view style="display: flex;justify-content: space-between;">
                <view class="text-blod">{{checkedMachine.address}}</view>
                <image hidden="{{!isEdit}}" class="icon" style="padding-right: 24rpx;" src='/assets/icon/arrow_right.png'></image>
              </view>
            </view>
          </view>
          <!-- <view class="address-box line-box flex-a-c">
            <view class="flex-1">
              <view class="text6">实训舱编号：</view>
              <view class="text-blod">{{checkedMachine.code}}</view>
            </view>
          </view> -->
          <view class="add-friends">
            <view style="text-align: left;font-weight: 400;font-size: 24rpx;color: #999999;line-height: 33rpx;">
              <text>预约内容：</text>
            </view>
            <view class="weui-cells weui-cells_after-titl">
              <view wx:for="{{checkLearnType}}" wx:key="index" class="line-box flex-a-c" hidden="{{!isEdit && !item.checked}}">
                <!-- 选中/未选中 勾选框 -->
                <view bindtap="radioChange_n" data-type="{{item.workType}}" data-name="{{item.name}}">
                  <view hidden="{{!isEdit}}" class="check-outbox {{item.checked ?'checked':''}}">
                    <image hidden="{{!item.checked}}" class="checked-img" src="/assets/icon/radio.png" />
                  </view>
                </view>
                <view class="weui-cell__bd new" style="{{!item.checked?'color:gray;':''}}">
                  {{item.name}}
                </view>
                <!-- 头像/邀请 -->
                <!-- <view style="position: absolute;left: 40%;" class="{{!item.checked?'gray;':''}}">
                <view wx:if="{{item.checked}}" style="display:flex;align-items: center;" bindtap="{{checkedOrInvite==1 ?'inviteFriend2' : (friend1.memberName?'':'handleShare')}}" data-item="2">
                  <image src="{{friend1.memberAvatar ? friend1.memberAvatar : friend1.toMemberName ? '/assets/icon/ic_2.png': '/assets/icon/ic_inv.png'}}" style="width:48rpx;height:48rpx;border-radius: 50%;margin-right: 8rpx;" />
                  <text>{{friend1.toMemberName?friend1.toMemberName:'邀请好友'}}</text>
                </view>
                <view wx:else style="display:flex;align-items: center;">
                  <image style="width:48rpx;height:48rpx;border-radius: 50%;margin-right: 8rpx;" src="/assets/icon/ic_no.png" />
                  <tex>邀请好友</tex>
                </view>
              </view>
              <view style="position: absolute;left: 70%;" class="{{!item.checked?'gray;':''}}">
                <view wx:if="{{item.checked}}" style="display:flex;align-items: center;" bindtap="{{checkedOrInvite==1 ?'inviteFriend3' : (friend2.memberName?'':'handleShare')}}" data-item="3">
                  <image src="{{friend2.memberAvatar ? friend2.memberAvatar : friend2.toMemberName ? '/assets/icon/ic_2.png': '/assets/icon/ic_inv.png'}}" style="width:48rpx;height:48rpx;border-radius: 50%;margin-right: 8rpx;" />
                  <text>{{friend2.toMemberName?friend2.toMemberName:'邀请好友'}}</text>
                </view>
                <view wx:else style="display:flex;align-items: center;">
                  <image style="width:48rpx;height:48rpx;border-radius: 50%;margin-right: 8rpx;" src="/assets/icon/ic_no.png" />
                  <tex>邀请好友</tex>
                </view>
              </view> -->
              </view>
            </view>
          </view>
          <!-- 非修改状态下，一个好友都没有 -->
          <view class="add-friends" hidden="{{!isEdit && !friend1.toMemberName && !friend2.toMemberName }}">
            <view class="text6">好友：</view>
            <view class="flex-a-c line-box">
              <!-- 头像/邀请 -->
              <view class='flex-1 friend-item line-r-box'>
                <view class="flex-1 flex-a-c" bindtap="{{checkedOrInvite==1 ?'inviteFriend2' : (friend1.memberName?'':'handleShare')}}" data-item="2">
                  <image src="{{friend1.toMemberAvatar ? friend1.toMemberAvatar : friend1.toMemberName ? '/assets/icon/ic_2.png': '/assets/icon/ic_inv.png'}}" style="width:48rpx;height:48rpx;border-radius: 50%;margin-right: 8rpx;" />
                  <view><text>{{friend1.toMemberName?friend1.toMemberName:'邀请好友'}}</text></view>
                </view>
                <image hidden="{{!( isEdit && friend1.toMemberName)}}" class="icon" src='/assets/icon/delete.png' bindtap="deleteFriend1" />
              </view>
              <view class='flex-1 friend-item'>
                <view class="flex-1 flex-a-c" bindtap="{{checkedOrInvite==1 ?'inviteFriend3' : (friend2.memberName?'':'handleShare')}}" data-item="3">
                  <image src="{{friend2.toMemberAvatar ? friend2.toMemberAvatar : friend2.toMemberName ? '/assets/icon/ic_2.png': '/assets/icon/ic_inv.png'}}" style="width:48rpx;height:48rpx;border-radius: 50%;margin-right: 8rpx;" />
                  <text>{{friend2.toMemberName?friend2.toMemberName:'邀请好友'}}</text>
                </view>
                <image hidden="{{!( isEdit && friend2.toMemberName )}}" class="icon" src='/assets/icon/delete.png' bindtap="deleteFriend2" />
              </view>
            </view>
          </view>
          <view>
            
            <!-- <view class="flex-a-c line-box" hidden="{{!isEdit && !friend1.toMemberName && !friend2.toMemberName }}">
              <view class='flex-1 friend-item line-r-box'>
                <text style="margin-left: 27px;">2号学习位</text>
              </view>
              <view class='flex-1 friend-item line-r-box'>
                <text style="margin-left: 27px;">3号学习位</text>
              </view>
            </view> -->
          </view>
        </view>
      </view>
    </view>
    <!--  -->
    <dialog showModal="{{showMemberGroup}}" bind:hideModalEvent="hideModal" title="检测到您有学习小组">
      <view>请选择您的预约方式：</view>
      <view class="item page-distance" wx:for="{{memberGroupList}}" wx:key="memberGroupID">
        <view class="flex-row justify-between">
          <text>
            <text>{{item.memberGroupName}}</text>
            {{item.name}} 成员({{item.sonList.length +1}})
          </text>
          <text>{{item.buildTimeStr}}</text>
        </view>
        <view data-idx="{{index}}" data-memberid="{{item.memberGroupID}}" class="flex-row align-center" style="margin-top: 20rpx;">
          <text>群主：</text>
          <image class="avatarImg" src="{{item.headMemberAvatarURL || logoImg}}"></image>
          <text>{{item.headMemberName}}</text>
          <button type="primary" class="subBtn btn-yesclick" bind:tap="checkMyGroup" data-item="{{item}}">
            选择
          </button>
        </view>
      </view>
      <button type="primary" class="subBtn btn-yesclick" bind:tap="appForMe">仅为我自己预约</button>
    </dialog>
    <view wx:if="{{loading}}">
      <text style="font-size: larger;font-weight: bold;">数据加载中……</text>
    </view>
  </scroll-view>

</view>
<view class="fixed-bottom" hidden="{{isLoginVisible || (!isLoginVisible && isEdit)}}">
  <view class="flex-a-c">
  <image class="icon" src='/assets/icon/bell_g.png'/>
  <view class="g-text">请仔细核对预约信息</view>
  </view>
  <!-- 数据加载中：显示灰色的确认预约按钮 -->
  <button class="topAppBtn loading" wx:if="{{trainBusinessInfoLoading}}" disabled>确认预约</button>
  <!-- 数据加载完成：根据报名状态显示对应按钮 -->
  <button class="topAppBtn" wx:elif="{{!trainBusinessInfo.myMemberTrainID}}" catchtap="toRegister">我要报名</button>
  <button class="topAppBtn" wx:elif="{{trainBusinessInfo.myMemberTrainStatus == 1}}" catchtap="toasttext">确认预约</button>
  <button class="topAppBtn" wx:else bind:tap="goApp" disabled="{{!(nextBeginTime&&checkedMachine.machineName)}}">确认预约</button>
</view>
<view class="fixed-bottom edit" hidden="{{isLoginVisible || (!isLoginVisible && !isEdit)}}">
  <button class="topAppBtn" bind:tap="handleEditYes">确认</button>
  <button class="topAppBtn cancel" bind:tap="handleEditNo">取消</button>
  </view>
<!-- 预约时间 选择 -->
<appointmentBox id="appointmentBox" isDrawerOpen="{{isDrawerOpen}}" info="{{info}}" bind:closeDrawer="closeDrawer" />

<!-- 城市选择 -->
<!-- <view class="drawer {{getCityshow}}" style="top: {{height + 44}}px"> -->
<view class="drawer {{getCityshow}}" style="top: {{0}}px">
  <view class="sticky">
    <!-- 搜索栏 -->
    <view class="title">
      <span bindtap="getCityshowFn">
        <i></i>
      </span>
      <input type="text" placeholder="搜索城市" bindinput="filterCityInput" />
    </view>
    <view class="geolocationCity">
      <span>
        当前：
        <span wx:if="{{!isLocating}}" bindtap="selectedCity" data-item="{{city}}">
          {{ city.name }}
        </span>
        <span wx:else>定位中</span>
      </span>
      <span>
        <span wx:if="{{geolocationError}}">定位失败</span>
        <span style="color: #0A82FF" bindtap="initAMap">重新定位</span>
      </span>
    </view>
  </view>
  <view class="cityListALL">
    <view class="recentCities" wx:if="{{recentCities.length!==0}}">
      <p>最近访问</p>
      <ul>
        <li wx:for="{{recentCities}}" wx:key="index" bindtap="selectedCity" data-item="{{item}}">
          {{ item.name }}
        </li>
      </ul>
    </view>
    <!-- <view class="hotCities">
        <p>热门城市</p>
        <ul>
          <li wx:for="{{hotCities}}" wx:key="index" bindtap="selectedCity" data-item="{{item}}">{{ item.name }}</li>
        </ul>
      </view> -->
    <view class="cityList">
      <p>城市列表</p>
      <ul class='cityItem' wx:if="{{filterCity.length===0}}">
        <li wx:for="{{cityList}}" wx:key="index" bindtap="selectedCity" data-item="{{item}}">
          {{item.name }}
        </li>
      </ul>
      <ul class='cityItem' wx:else>
        <li wx:for="{{filterCity}}" wx:key="index" bindtap="selectedCity" data-item="{{item}}">
          {{ item.name }}
        </li>
      </ul>
    </view>
  </view>
</view>


<loginModal isLoginVisible="{{isLoginVisible}}"></loginModal>
<noticeForUNLogin show-modal="{{showModal}}" content="{{content}}" bindclose="handleCloseModal"></noticeForUNLogin>


<!-- 邀请好友 -->
<dialog showModal="{{friendShowModal}}" bind:hideModalEvent="hideModal" title1="我的好友" title2="我的群组" defaultType="2" bindchangeIsChecked="handleChangeIsChecked" bindaddFriend="addFriend">
  <view wx:if="{{friendOrGroup}}">
    <view class="box" wx:for="{{memberGroupList}}" wx:key="memberGroupID">
      <view data-idx="{{index}}" data-memberid="{{item.memberGroupID}}" class="friends-Item">
        <view class="friends-Item_left">
          <image src="{{item.headMemberAvatarURL || logoImg}}" class="img" />
        </view>
        <view class="friends-Item_main">
          <view class="friends-Item_info"><text>群主：{{item.headMemberName}}</text></view>
          <view class="friends-Item_num text-nowrap">
            <text>
              <text>{{item.memberGroupName}}</text> {{item.name}} 成员({{item.sonList.length}})
            </text>
            <text>{{item.buildTimeStr}}</text>
          </view>
        </view>
        <view class="friends-Item-btn-box checked" bind:tap="checkMyGroup" data-item="{{item}}">
          <view class="btn" style="background-color: green;">选择</view>
        </view>
      </view>
    </view>
  </view>
  <view wx:for="{{friendList}}" wx:key="memberID" class='box' wx:if="{{!friendOrGroup}}">
    <view class="friends-Item">
      <view class="friends-Item_left">
        <image src="{{item.toMemberAvatar ? item.toMemberAvatar : '/assets/images/mine_defaultImg.png'}}" class="img" />
      </view>
      <view class="friends-Item_main">
        <view class="friends-Item_info">{{item.toMemberName}}</view>
        <view class="friends-Item_num text-nowrap">{{item.toMemberPhone||''}}</view>
      </view>
      <view class="friends-Item-btn-box checked" bind:tap="checkFriend" data-item="{{item}}">
        <view class="btn" style="background-color: green;">选择</view>
      </view>
    </view>
  </view>
</dialog>