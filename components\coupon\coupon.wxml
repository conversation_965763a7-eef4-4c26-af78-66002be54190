<view class="coupon">
    <scroll-view scroll-x="true" class="scroll-box" scroll-left="{{scrollLeft}}" bindtouchstart="onTouchStart">
        <view wx:if="{{dataList.length == 0}}" style="color: grey;">暂无优惠券,敬请期待</view>
        <view class="content">
            <view class="item" wx:for="{{dataList}}" wx:key="bonusID">
                <view class="text-1" wx:if="{{item.subType == 1}}">
                    {{item.price}}元
                </view>
                <view class="text-1" wx:if="{{item.subType == 2}}">
                    {{item.offRate}}%
                </view>
                <!-- <view class="text-2">
                    {{item.shopName}}
                </view> -->
                <view class="text-3">
                    {{item.name || '无'}}
                </view>
                <view class="btn {{item.canGetNumber == 0 ? 'grey':''}}"  bind:tap="get" data-bonusID="{{item.bonusID}}" data-info="{{item}}">
                    立即领取
                </view>
            </view>
        </view>
    </scroll-view>
</view>