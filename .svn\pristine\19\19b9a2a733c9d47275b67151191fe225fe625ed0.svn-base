var ports = require("../../../utils/ports.js")
var app = getApp()
// packageA/pages/assistantDetail/assistantdetail.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    memberMajor: [],
    memberInfo: [],
    memberID: '',
    myself:false,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log(options, 'options');
    this.setData({
      memberID: options.memberid
    })
    this.getOneMemberMajorDetail(options.membermajorid);
    this.getOneMemberDetail()
    if (wx.getStorageSync('USER_MEMBERID') == options.memberid) {
      this.setData({
        myself:true
      })
    }
  },

  //获取专业列表
  async getOneMemberMajorDetail(memberMajorID) {
    let params = {
      sessionID: wx.getStorageSync('USER_SESSIONID'),
      memberMajorID,
    }
    let res = await ports.ModuleAll.getOneMemberMajorDetail(params)
    console.log(res.data.body.apiMemberMajorDetailDto, '详情');
    const data = res.data.body.apiMemberMajorDetailDto
    this.setData({
      memberMajor: data
    })
  },

  //获取用户信息
  async getOneMemberDetail() {
    let params = {
      sessionID: wx.getStorageSync('USER_SESSIONID'),
      memberID: this.data.memberID,
      checkMePraise: 1,
      checkMeFriend: 1,
    }
    let res = await ports.ModuleAll.getOneMemberDetail(params)
    console.log(res.data.body, '用户信息');
    const data = res.data.body
    this.setData({
      memberInfo: data
    })
  },

  //点赞
  praiseBtn(e) {
    // console.log(e.currentTarget.dataset.item);
    let sessionID = wx.getStorageSync('USER_SESSIONID')
    if (!sessionID) {
      return wx.showToast({
        title: '请先登录',
        icon: 'none'
      })
    }
    let item = e.currentTarget.dataset.item
    console.log(item, '.isMePraise');
    if (item.isMePraise == 1) {
      console.log('取消点赞');
      let params = {
        sessionID,
        objectDefineID: app.memberObjectDefineID,
        objectID: item.memberID,
      }
      ports.ModuleAll.getMyPraiseList(params).then(res => {
        // console.log(res, '列表');
        let list = res.data.body.data.rows || []
        if (list.length === 0) return

        let AD = {
          sessionID,
          praiseID: list[0].praiseID
        }
        ports.ModuleAll.cancelOnePraise(AD).then(ad => {
          if (res.data.header.code == 0) {
            wx.showToast({
              title: '取消点赞成功',
            })
            this.getOneMemberDetail()
          } else {
            wx.showToast({
              title: res.data.header.msg,
              icon: 'none'
            })
          }
        })
      })
    } else {
      let params = {
        sessionID,
        objectDefineID: '8af5993a4fdaf145014fde1a732e00ff',
        objectID: item.memberID,
        objectName: item.name,
        toMemberID: this.data.memberID,
      }
      ports.ModuleAll.submitOnePraise(params).then(res => {
        console.log(res);
        if (res.data.header.code == 0) {
          wx.showToast({
            title: '点赞成功',
          })
          this.getOneMemberDetail()
        } else {
          wx.showToast({
            title: res.data.header.msg,
            icon: 'none'
          })
        }
      })
    }
  },

  callPhone(event) { //拨打电话
    var phone = event.currentTarget.dataset.phone;
    console.log(phone, '1');
    wx.showModal({
      title: '提示',
      content: '是否拨打' + phone,
      success: (res) => {
        if (res.confirm) {
          wx.makePhoneCall({
            phoneNumber: phone,
          })
        }
      }
    })
  },

  // 添加好友
  async createOneFriend() {
    // sessionID: wx.getStorageSync('USER_SESSIONID'),
    let res = await ports.ModuleAll.createOneFriend({
      phone: this.data.memberInfo.phone,
      toMemberID: this.data.memberID,
      name: this.data.memberMajor.name,
    })
      if (res.data.header.code == 0) {
        wx.showToast({
          title: '添加成功',
          icon: 'success'
        })
        this.getOneMemberDetail()
      } else {
        wx.showToast({
          title: '添加失败',
          icon: 'error'
        })
      }
  },

  async toTalk(e){
    const memberID=this.data.memberID;

    let url = `/packageA/pages/onLine/onLine?memberID=${memberID}` 
    wx.navigateTo({
      url,
    })
    return
    let params = {
      sessionID:wx.getStorageSync('USE_SESSIONID'),
      memberID:this.data.memberInfo.memberID,
      shopID:app.shopID
    }
    let res = await ports.ModuleAll.applyOneOnlineSiteService(params)
    console.log(res,'9527');
    const serviceEventID = res.data.body.data.serviceEventID
    wx.navigateTo({
      url: `/packageA/pages/onLine/onLine?memberID=${this.data.memberID}&serviceEventID=${serviceEventID}`,
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})