// packageA/pages/memberTrainList/memberTrainList.js
var ports = require("../../../utils/ports")
var app = getApp()

const statusList = [
  {value: 1, name: '新'},
  {value: 2, name: '已经取消'},
  {value: 3, name: '报名审核通过'},
  {value: 4, name: '报名审核拒绝'},
  {value: 5, name: '取消审核通过'},
  {value: 6, name: '取消审核拒绝'},
  {value: 10, name: '已经付款'},
  {value: 11, name: '已经退款'},
  {value: 15, name: '报名成功'},
  {value: 20, name: '签到完成'},
  {value: 21, name: '验票完成'},
  {value: 24, name: '考试完成'},
  {value: 25, name: '开票完成'},
  {value: 30, name: '完成'},
]

const payStatusList = [
  {value: 0, name: '未付款'},
  {value: 1, name: '已取消'},
  {value: 2, name: '已付款'},
  {value: 3, name: '付款过期'},
]
Page({

  /**
   * 页面的初始数据
   */
  data: {
    type:0,
    timeType:0,
    memberTrainList:[]
  },
  btn(e){
    let trainid = e.currentTarget.dataset.trainid
    let data = {
        memberTrainID: trainid
    }
    // 先显示加载提示
    wx.showLoading({
        title: '文件生成中...',
    })
    
    ports.ModuleAll.generateOneMemberTrainPDF(data).then(res=>{
        wx.hideLoading()
        wx.showLoading({
            title: '文件下载中...',
        })
        
        wx.downloadFile({
            url: res.data.body.url,
            success (res) {
                wx.hideLoading()
                // 检查文件是否存在
                if (!res.tempFilePath) {
                    wx.showToast({
                        title: '文件下载失败',
                        icon: 'error'
                    })
                    return
                }
                
                // 增加文件类型判断
                const fileType = 'pdf'
                
                wx.openDocument({
                    filePath: res.tempFilePath,
                    fileType: fileType,
                    showMenu: true,
                    success: function (res) {
                        wx.showToast({
                            title: '打开文档成功',
                            icon: 'none'
                        })
                    },
                    fail: function (error) {
                        console.error('打开文档失败:', error)
                        // 针对安卓的特殊处理
                        if (wx.getSystemInfoSync().platform === 'android') {
                            wx.showModal({
                                title: '提示',
                                content: '打开文档失败，请确保已安装PDF查看器',
                                showCancel: false
                            })
                        } else {
                            wx.showToast({
                                title: '打开文档失败',
                                icon: 'error'
                            })
                        }
                    },
                })
            },
            fail: function(error) {
                wx.hideLoading()
                console.error('下载失败:', error)
                wx.showToast({
                    title: '文件下载失败',
                    icon: 'error'
                })
            }
        })
    }).catch(error => {
        wx.hideLoading()
        console.error('生成PDF失败:', error)
        wx.showToast({
            title: '生成文件失败',
            icon: 'error'
        })
    })
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log(options);
    this.setData({
      type:options.type,
      timeType:options.timeType
    })
    this.getMemberTrainList()
  },
   //去付款
   payOrder(e) {
    var id = e.currentTarget.dataset.id
    if (!id) {
      wx.showToast({
        title: '订单异常',
        icon: 'none'
      })
      return
    }
    var openid = wx.getStorageSync("USER_OPENID")
    if (!openid) {
      wx.showToast({
        title: '用户信息异常',
        icon: 'none'
      })
      return
    }
    wx.showToast({
      title: '提交中',
      icon: 'loading',
      mask: true
    })

    let payData = {}
    payData.memberOrderID = id
    payData.openid = openid
    payData.payWayID = app.paywayID
    payData.body = '购买商品'
    ports.ModuleAll.getWeixinPayInfo(payData).then(res => {
      if (res.data.header.code == 0) {
        var data = res.data.body;
        wx.requestPayment({
          timeStamp: data.timeStamp,
          nonceStr: data.nonceStr,
          package: data.package,
          signType: data.signType,
          paySign: data.paySign,
          success(res) {
            wx.showToast({
              title: '支付成功',
              icon: 'none',
              mask: true,
              duration: 1000
            })
          },
          fail() {
            wx.showToast({
              title: '取消支付',
              icon: 'none',
              mask: true,
              duration: 1000
            })
          }
        })
      } else {
        wx.showToast({
          title: res.data.header.msg,
          icon: 'none'
        })
      }
    })

  },
  getMemberTrainList(){
    var that = this
    let params = {
      sessionID:wx.getStorageSync('USER_SESSIONID'),
      joinMemberID:wx.getStorageSync('USER_MEMBERID'),
      sortTypeTime:1
    }
    ports.ModuleAll.getMemberTrainList(params).then(res=>{
      // console.log(res,'++++');
      let list = res.data.body.data.rows || []
      list.forEach(item=>{
        item.statusText = that.filtersList(item.status,statusList)
        item.payStatusText = that.filtersList(item.payStatus,payStatusList)
      })
      this.setData({
        memberTrainList:list
      })
      console.log(this.data.memberTrainList,'memberTrainList');
    })
  },
  href(e){
    let item = e.currentTarget.dataset.item
    let url = `/packageA/pages/memberTrainDetail/memberTrainDetail?memberTrainID=${item.memberTrainID}`
    console.log(url);
    wx.navigateTo({
      url,
    })
  },
  filtersList(value, arr) {
    if (!value) return '无'
    for (let i = 0; i < arr.length; i++) {
      if (arr[i].value == value) {
        return arr[i].name; // 找到之后就使用return返回找到的项，跳出循环
      }
      if (i == arr.length - 1 && arr[i].value != value) return '无'
    }
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})