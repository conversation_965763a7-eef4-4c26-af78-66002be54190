// components/trainBusinessBox/trainBusinessBox.js
const ports = require("../../utils/ports");
var app = getApp()
Component({

  /**
   * 组件的属性列表
   */
  properties: {
    title: String,
    navigatorID: String,
  },

  /**
   * 组件的初始数据
   */
  data: {
    logoImg: app.logoimg,
    trainBusinessList: []
  },

  /**
   * 组件的方法列表
   */
  methods: {
    init() {
      this.getTrainBusinessList();
    },
    getTrainBusinessList(){
      let params = {
        navigatorID: this.properties.navigatorID,
        sortTypeOrder: 1,
        pageNumber: 6,
        getDataType: 3
      }
      ports.ModuleAll.getStoryRecommendList(params).then(res => {
        const rows = res.data.body.data.rows || [];
        // console.log(rows,'rw');
        // const processedRows = rows.map(item => {
        //   return {
        //     ...item,
        //     formattedTime: this.formatTrainTime(item.trainBusinessBeginDateStr, item.trainBusinessEndDateStr)
        //   };
        // });
  
        this.setData({
          trainBusinessList: rows
        });
      })
    },
    toAll() {
      let url = `/packageA/pages/trainBusinessList/trainBusinessList`
      wx.navigateTo({
        url
      })
    },
    toDetail(e) {
      // console.log(e.currentTarget.dataset.id);
      let url = `/packageA/pages/trainBusinessDetail/trainBusinessDetail?id=${e.currentTarget.dataset.id}`
      wx.navigateTo({
        url
      })
    },
  }
})