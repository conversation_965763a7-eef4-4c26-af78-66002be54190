<!--pages/sharePageForInvite/sharePageForInvite.wxml-->
<!-- <loginModal isLoginVisible="{{isLoginVisible}}"></loginModal> -->
<view class="sharePageForInvite">
  <!-- 正常邀请状态 -->
  <view wx:if="{{canInvite==1}}" class="page-box">
    <view class="check-item" >
    <image src="https://3onepicture.3onedataqz.com/5d35236596cb4af88853c30ef832f4fe.jpg" mode="widthFix" style="width: 230rpx; height: 230rpx;padding-top: 240rpx;padding-bottom: 110rpx;"/>
      <!-- <view class="hello">您好：</view> -->
      <view>
      <view style="display: flex;">
        <image src="{{memberAvatar}}" mode="widthFix" style="border-radius: 50%;width: 88rpx;height: 88rpx;"/>
        <text class="bloder-text" style="margin-left: 32rpx;">{{applyMemberName}} </text>
      </view>
        <view style="font-size: 32rpx;font-weight: bold;margin: 20rpx 0;">发来一个组队邀请</view>
      </view>
      <view style="text-align: center;padding-top: 120rpx;">
        <view class="small-text">邀请您一起参加</view>
        <view style="margin: 20rpx 0;">
          <text style="font-size: 32rpx;font-weight: bold;">{{objectName}}</text>
          <text class="small-text"> 的 </text>
          <text style="font-size: 32rpx;font-weight: bold;">{{workType==2?'学习':workType==3?'实训':'考试'}}</text>
        </view>
        <view style="margin: 20rpx 0;font-size: 32rpx;">预约时段{{nextTime}}</view>
        

      </view>
      <view wx:if="{{resourcesObjectName}}">
        <text class="small-text">{{workType==2?'学习':workType==3?'实训':'考试'}}科目为：</text>
        <text class="bloder-text">{{resourcesObjectName}}</text>
      </view>
    </view>
    <button style="width: 80%;" class="btn-yesclick" wx:if="{{!isLoginVisible&&btnType==1}}" bind:tap="createOneAppointment">接受</button>
    <!-- <button style="width: 80%;" class="btn-yesclick" wx:if="{{isLoginVisible}}">登录后操作</button> -->
    <button style="width: 80%;" class="btn-yesclick" wx:if="{{isLoginVisible}}" catchtap="showRegister">登录并加入组队</button>
    <button style="width: 80%;" class="btn-yesclick" wx:if="{{!isLoginVisible&&btnType==2}}" catchtap="toRegister">我要报名</button>
  </view>

  <!-- 异常状态的统一处理 -->
  <view wx:if="{{canInvite!=1}}" class="page-box status-page">
    <view class="status-container">
      <!-- 状态图标 -->
      <view class="status-icon">
        <view wx:if="{{canInvite==2}}" class="icon-text warning">⚠️</view>
        <view wx:if="{{canInvite==3}}" class="icon-text full">🚫</view>
        <view wx:if="{{canInvite==4}}" class="icon-text success">✅</view>
      </view>

      <!-- 状态标题 -->
      <view class="status-title">
        <text wx:if="{{canInvite==2}}">无法接受邀请</text>
        <text wx:if="{{canInvite==3}}">预约已满</text>
        <text wx:if="{{canInvite==4}}">已接受邀请</text>
      </view>

      <!-- 状态描述 -->
      <view class="status-desc">
        <text wx:if="{{canInvite==2}}">您无法接受自己发出的邀请</text>
        <text wx:if="{{canInvite==3}}">当前时间段预约已满，请关注其他时间段</text>
        <text wx:if="{{canInvite==4}}">您已经同意过这个邀请了</text>
      </view>

      <!-- 邀请信息展示 -->
      <view class="invite-info">
        <view class="invite-from">
          <image src="{{memberAvatar}}" mode="widthFix" class="avatar"/>
          <text class="name">{{applyMemberName}}</text>
        </view>
        <view class="invite-detail">
          <text class="detail-text">{{objectName}} - {{workType==2?'学习':workType==3?'实训':'考试'}}</text>
          <text wx:if="{{resourcesObjectName}}" class="subject-text">科目：{{resourcesObjectName}}</text>
        </view>
        <view class="invite-detail">
          <text class="detail-text">{{nextTime||''}}</text>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="status-actions">
      <button wx:if="{{canInvite==2||canInvite==3}}" class="btn-back" catchtap="goIndex"> 返回 </button>
      <button wx:if="{{canInvite==4}}" class="btn-view" catchtap="goIndex">进入首页</button>
    </view>
  </view>

</view>