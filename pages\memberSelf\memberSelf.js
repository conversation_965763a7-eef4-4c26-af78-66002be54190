const ports = require("../../utils/ports");
const utils = require("../../utils/util")
var app = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    appointments: [
      { status: '未开始', count: 0 },
      { status: '进行中', count: 0 },
      { status: '已完成', count: 0 },
      { status: '全部', count: 0 }
    ],
    customStyle: `--mainColor:${app.mainColor}`,
    cust_bar_height: app.cust_bar_height,
    memberInfo: {},
    top: app.globalData.menuButtonTop + app.globalData.menuButtonHeight / 2,
    reLoadComponent:false  ,  //功能菜单组件刚进入页面时不显示
    areaFlag: false,
    doorFlag: false,
    assistFlag: false,
    clearFlag: true,
  },


  getAppointments(){
    if(!wx.getStorageSync('USER_SESSIONID') || !wx.getStorageSync('USER_MEMBERID')) return;
    ports.ModuleAll.getMyAppointmentStatistics({memberID:wx.getStorageSync('USER_MEMBERID')}).then(res=>{
      let data = res.data.body
      let code = res.data.header.code
      let allTotal = (data.newTotal || 0) + (data.runningTotal || 0) + (data.leftTotal || 0);
      
      let appointments=[
        { status: '未开始', count: data.newTotal, type: 'new' },
        { status: '进行中', count: data.runningTotal, type: 'running' },
        { status: '已完成', count: data.leftTotal, type: 'left' },
        { status: '全部', count: allTotal, type: 'all' }
      ]
      this.setData({
        appointments:appointments,
        code
      })
    })
  },
  
  goMyCertificate() {
    console.log(222);
    wx.navigateTo({
      url: "/packageA/pages/myCertificate/myCertificate",
    })
  },


  // 跳转到预约列表
  toAppointmentList(e) {
    const type = e.currentTarget.dataset.type;
    wx.navigateTo({
      url: `/packageA/pages/myAppointment/myAppointment?type=${type}`
    })
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    console.log(app.cust_bar_height, "cust_bar_heightcust_bar_height");
    console.log(options)
    // 首次进入我的页面的时候不能弹出登录页面
    // utils.isLoginTimeOut()
    
    this.getMemberMajorList()
    this.setData({reLoadComponent:true})
    if (options.scene) wx.setStorageSync('recommendCode', options.scene)
    if (options.memberID) wx.setStorageSync('recommendMemberID', options.memberID)
    let components = this.selectAllComponents(".child")
    for (var i = 0; i < components.length; i++) {
      components[i].init();
      if(i===components.length -1){
        setTimeout(()=>{
          wx.hideLoading();
          wx.hideNavigationBarLoading();
          //停止下拉刷新
          wx.stopPullDownRefresh();
        },1000)
      }
    }
  },

  async toMineInfo() {
    if (!wx.getStorageSync('deviceID')) this.getDevice();
    if (!wx.getStorageSync('USER_MEMBER')) {
      wx.showLoading({
        title: '加载中',
      })
      utils.LOGIN(0).then(res => {
        this.onShow()
      })
      this.setData({
        memberInfo: wx.getStorageSync('USER_MEMBER')
      })
    } else {
      wx.navigateTo({
        url: '/packageA/pages/mineInfo/memberInfo',
      })
    }
  },
  async getDevice() {
    let res = await ports.ModuleAll.getSystemInfo();
    let ADCID = new Date().getTime().toString(36) + Math.random().toString(36).substr(2, 9);
    let params = {
      applicationID: app.applicationID,
      siteID: app.siteID,
      company: res.brand, //制造商名称
      name: res.model, //设备型号
      shortName: res.brand, //设备型号
      category: 3, //设备类型
      OSname: res.system, //操作系统
      model: res.model, //品牌型号
      // udid: openId, //一个字符串
      // imei: openId, //手机特有的1个串
      mac: ADCID, //设备的mac地址
    }
    let resDevice = await ports.ModuleAll.submitDeviceInfo(params)
    wx.setStorageSync('deviceID', resDevice.data.body.deviceID)
    this.setData({
      clearFlag: true
    })
  },
  changeView(e) {
    console.log(e.detail,"更新视图")
  },
  loginOut() {
    let params = {
      sessionID:wx.getStorageSync('USER_SESSIONID'),
    }
    ports.ModuleAll.loginOut(params).then(res => {
      if (res.data.header.code === 0) {
        
      }else{
        wx.showToast({
          title: res.data.body.msg,
          icon: 'none'
        })
      }
    })
  },
    //清除缓存
    Clearcache() {
      this.loginOut()
      wx.clearStorage({
        success(res) {
          if (res.errMsg == 'clearStorage:ok') {
            wx.showToast({
              title: '清除成功！',
              icon: 'none'
            })
          }
        },
        fail() {
          wx.showToast({
            title: '清除失败！',
            icon: 'none'
          })
        }
      })
    },
  getMemberMajorList() {
    var that = this
    let params = {
      sessionID: wx.getStorageSync('USER_SESSIONID'),
    }
    ports.ModuleAll.getMyMemberMajorList(params).then(res => {
      let list = res.data.body.data.rows;
      wx.setStorageSync('majorOperating', 0)
      that.setData({
        doorFlag: false,
      })
      if (list.length>0) {
        that.setData({
          areaFlag: true
        })
      } else {
        that.setData({
          areaFlag: false
        })
        return;
      }
      for (let i = 0;i < list.length;i ++) {
        if (app.supportMajorID === list[i].majorID) {
          that.setData({
            doorFlag: true
          })
          wx.setStorageSync('majorOperating', 1)
          break;
        }
      }
    })
  },
  openTheDoor() {
    let url = `/packageA/pages/operatingManage/operatingManage?openType=${this.data.doorFlag?2:1}`
    wx.navigateTo({
      url,
    })
  },
  openTheTA() {
    let url = `/packageA/pages/teachingAssist/teachingAssist`
    wx.navigateTo({
      url,
    })
  },
  getMemberInfo() {
    if (!wx.getStorageSync('USER_SESSIONID') || !wx.getStorageSync('USER_MEMBERID')) {
      return this.setData({
        memberInfo: {}
      })
    }
    ports.ModuleAll.getOneMemberDetail({
      sessionID: wx.getStorageSync('USER_SESSIONID'),
      memberID: wx.getStorageSync('USER_MEMBERID')
    }).then(res => {
      console.log(res);
      if (res.data.header.code !== 0) {
        return this.setData({
          memberInfo: {}
        })
      }
      this.setData({
        memberInfo: res.data.body
      })
      wx.setStorageSync('people', res.data.body.peopleID)
      wx.setStorageSync("USER_MEMBER", res.data.body)
      wx.stopPullDownRefresh();
    })
    // ports.ModuleAll.queryMemberRealNameStatus({
    //   sessionID: wx.getStorageSync('USER_SESSIONID'),
    //   memberID: wx.getStorageSync('USER_MEMBERID')
    // }).then(res => {
    //   let data = res.data.body.data
    //   if (data.approveStatus == 1) {
    //     wx.setStorageSync("isRealName", true)
    //   }else{
    //     wx.setStorageSync("isRealName", false)
    //   }

    // })
  },
  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.setData({
      memberInfo: wx.getStorageSync('USER_MEMBER')
    })
    
    if (!wx.getStorageSync('deviceID')) {
      this.setData({
        clearFlag: false
      })
    } else {
      this.setData({
        clearFlag: true
      })
    }
    this.getAppointments();
    let components = this.selectAllComponents(".child")
    for (var i = 0; i < components.length; i++) {
      components[i].init();
      if (i === components.length - 1) {
        setTimeout(() => {
          wx.hideLoading();
          wx.hideNavigationBarLoading();
          //停止下拉刷新
          wx.stopPullDownRefresh();
        }, 1000)
      }
    }

    if (!wx.getStorageSync('USER_STUDENT')) return
      if (wx.getStorageSync('USER_STUDENT')) {
        this.setData({
          banded: true,
          student: wx.getStorageSync('USER_STUDENT')
        })
        return
      }
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {
    utils.isLoginTimeOut()
    if (wx.getStorageSync('USER_SESSIONID')) {
      this.getMemberInfo();
      this.getMemberMajorList();
      this.setData({reLoadComponent:false})
      setTimeout(()=>{this.setData({reLoadComponent:true})},100)
    } else {
      wx.stopPullDownRefresh();
    }
  },
  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {
    let path = utils.sharUrl("/pages/memberSelf/memberSelf")
    return {
      title: app.weixinAppTitle,
      path,
      imageUrl: '/assets/images/logo.png'
    }
  },
  toSettings(){
    wx.navigateTo({
      url: '/packageA/pages/memberSettings/memberSettings',
    })
  }
})
