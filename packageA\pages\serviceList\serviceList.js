// packageA/pages/serviceList/serviceList.js
var ports = require("../../../utils/ports")
var app = getApp();
Page({

  /**
   * 页面的初始数据
   */
  data: {
    logoImg: app.logoimg,
    categoryID:'',
    categoryList: [],
    servicerList:[]
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.setData({
      logoImg: app.logoimg,
      categoryID:options.categoryID
    })
    // console.log(this.data.logoImg);
    // this.init();
    this.newInit();
    this.flag();
  },
  async init() {
    let params = {
      sessionID: wx.getStorageSync('USER_SESSIONID'),
      categoryID: app.servicerCategoryID,
      depth: 1
    }
    let res = await ports.ModuleAll.getCategoryList(params)
    let categoryList = res.data.body.data.rows || []
    const length = categoryList.length
    Promise.all(
      categoryList.map((item, index) => {
        return new Promise(resolve => {
          let paramsAD = {
            sessionID: wx.getStorageSync('USER_SESSIONID'),
            // shopID: app.shopID,
            categoryID: item.categoryID,
            sortOrderSeq: 1
          }
          // getSiteServicerList
          ports.ModuleAll.getSiteServicerList(paramsAD).then(resAD => {
            // console.log(resAD,'resAD');
            let sonList = resAD.data.body.data.rows || []
            categoryList[index].sonList = sonList
            resolve(sonList)
          })
        })
      })
    ).then(allRes => {
      // console.log(allRes);
      this.setData({
        categoryList
      })
      // console.log(this.data.categoryList);
    })
  },
  newInit(){
    // 获取 sessionID，如果没有则不传该参数
    const sessionID = wx.getStorageSync('USER_SESSIONID');
    
    let paramsAD = {
      shopID: app.shopID,
      categoryID: app.siteServiceCategoryID,
      sortOrderSeq: 1
    }
    
    // 只有当 sessionID 存在时才添加到参数中
    if (sessionID) {
      paramsAD.sessionID = sessionID;
    }
    
    ports.ModuleAll.getSiteServicerList(paramsAD).then(resAD => {
      // 检查响应中的错误
      if (resAD.data && resAD.data.header && resAD.data.header.code !== 0) {
        // 接口返回了错误
        if (resAD.data.header.code === 10160) {
          wx.showToast({
            title: '此功能需要先登录才能使用',
            icon: 'none',
            duration: 2000
          });
        } else {
          wx.showToast({
            title: resAD.data.header.msg || '获取服务列表失败',
            icon: 'none',
            duration: 2000
          });
        }
        return;
      }
      
      this.setData({
        servicerList:resAD.data.body.data.rows || []
      })
    }).catch(error => {
      // 处理网络错误或其他异常
      console.error('getSiteServicerList error:', error);
      
      // 尝试从错误对象中获取错误信息
      if (error && error.data && error.data.header) {
        if (error.data.header.code === 10160) {
          wx.showToast({
            title: '此功能需要先登录才能使用',
            icon: 'none',
            duration: 2000
          });
        } else {
          wx.showToast({
            title: error.data.header.msg || '获取服务列表失败',
            icon: 'none',
            duration: 2000
          });
        }
      } else {
        wx.showToast({
          title: '获取服务列表失败',
          icon: 'none',
          duration: 2000
        });
      }
    })
  },
  flag(){
    // 只有在有 sessionID 时才检查手机号
    const sessionID = wx.getStorageSync('USER_SESSIONID');
    if (!sessionID) {
      return; // 没有登录，不检查手机号
    }
    
    let memberInfo = wx.getStorageSync('USER_MEMBER')
    if(!memberInfo.phone){
      wx.showModal({
        title: '提示',
        content: '您还没有设置手机号',
        complete: (res) => {
          wx.reLaunch({
            url: '/pages/memberSelf/memberSelf',
          })
        }
      })
    }
  },
  toOnline(e) {
    const memberID=e.currentTarget.dataset.memberid;

    let url = `/packageA/pages/onLine/onLine?memberID=${memberID}` 
    wx.navigateTo({
      url,
    })

    return
    let params = {
      sessionID: wx.getStorageSync('USER_SESSIONID'),
      shopID: app.shopID,
    }
    // 群组
    ports.ModuleAll.applyOneShopSiteService(params).then(res => {
      let serviceEventID = res.data.body.serviceEventID
      let memberGroupID = res.data.body.memberGroupID
      let url = `/packageA/pages/onLine/onLine?serviceEventID=${serviceEventID}&memberGroupID=${memberGroupID}&memberID=${memberID}` 
      wx.navigateTo({
        url,
      })
    })

  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})