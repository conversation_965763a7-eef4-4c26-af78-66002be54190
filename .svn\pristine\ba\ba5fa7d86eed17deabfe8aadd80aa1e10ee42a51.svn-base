<view class="wrap">
  <scroll-view id="section" class="page-bgcolor" scroll-into-view="{{scrollBottom}}" scroll-y="true">
    <view class="time" wx:if='{{allMessageObj[0].sendTimeString}}'>
      {{allMessageObj[0].sendTimeString}}
    </view>
    <view class="msg-list">
      <view class="one-msg flex-column" wx:for='{{allMessageObj}}' wx:key='id' id="item{{index}}">
        <view class="time" wx:if='{{item.sendTime-allMessageObj[index-1].sendTime>=5*60*1000}}'>
          {{item.sendTimeString}}
        </view>
        <view class="flex-row align-center" style="{{item.memberID==memberID?'justify-content:flex-end':''}}">
          <!-- 头像 -->
          <view class="avatar" wx:if='{{item.memberID!=memberID}}'>
            <image src="{{item.memberAvatar || logoImg}}" class="image"></image>
          </view>
          <view class="flex-column">
            <text wx:if="{{item.memberID!=memberID}}" style="font-size: 26rpx;">{{item.memberName}}</text>
            <!-- 文字 -->
            <view class="{{item.memberID==memberID?'content-text':'content-text2'}}" wx:if="{{!item.imageURL}}">{{item.name}}</view>
          </view>
          <!-- 图片 -->
          <view class="{{item.memberID==memberID?'content-text1':'content-text2'}} imageView" wx:if="{{item.imageURL}}">
            <view class="siengs" bindtap="getViewPicture" data-src="{{item.imageURL}}">
              <image mode="aspectFill" src="{{item.imageURL}}"></image>
            </view>
          </view>
          <!-- 头像 -->
          <view class="avatar" wx:if='{{item.memberID==memberID}}'>
            <image src="{{item.memberAvatar}}" class="image"></image>
          </view>
        </view>
      </view>
    </view>
  </scroll-view>
  <view class="footer">
    <view class="btn-list flex-row" wx:if='{{isSellorOrBuyer==0}}'>
      <view class="col-3" bindtap='sendQuest1' wx:for="{{btns}}" wx:key="1" data-index="{{index}}">
        <view class="btn one-btn-text ">{{item.title}}</view>
      </view>
    </view>
    <view class="form flex-row justify-between">
      <view class="Rlier btn-yesclick" bindtap="getPinjia" wx:if="{{showScoreBtn}}">评价</view>
      <view class="control-form2" bindtap="getimage">
        <view class='send2img'>
          <image src="/assets/images/add.png" />
        </view>
      </view>
      <label class="input {{imageArr.length>0?'inputBoder':''}}">
        <input wx:if="{{imageArr.length === 0}}" bindinput='inputValue' cursor-spacing="10" placeholder="输入消息..." value="{{message}}"></input>
      </label>
      <view class="control-form">
        <view>
          <view class='send2 btn-yesclick' bindtap='sendMessage'>发送</view>
        </view>
      </view>
    </view>
    <view wx:if="{{imageArr.length>0}}">
      <view class="dieng">
        <view class="imger" wx:for="{{imageArr}}" wx:key="index">
          <image bindload="imgHeight" data-id="{{index}}" style='height: {{item.imgheight}}rpx;' src="{{item.url}}">
            <view class="rins" data-index="{{index}}" bindtap="getdeleteimg">删除</view>
          </image>
        </view>
      </view>
    </view>
  </view>
</view>
<!-- 弹出框 -->
<block wx:if="{{isShowConfirm}}">
  <view class='toast-box'>
    <view class='toastbg'></view>
    <view class='showToast'>
      <view class='toast-title'>
        <text>请对本次服务作出评价</text>
      </view>
      <view class='toast-main'>
        <view class='toast-input'>
          <view>
            <view style="font-size:32px;color:#f63;text-align:center;margin-top:5px;">{{star}}</view>
              <!-- 加减分按钮（可选） -->
         
         
            <starsScore score="{{star}}" star="{{5}}" size="21px" starWidth="38rpx" starHeight="56rpx" bind:change="onScoreChange" />
            <!-- <view style="display:flex; justify-content:center; margin-top:10px;">
              <button bindtap="decreaseScore">-</button>
              <button bindtap="increaseScore">+</button>
            </view> -->
            <!-- <view style="width:105px;margin:0 auto">
              <view style="display:flex;">
                <view class="star" wx:for="{{yellow_star}}" bindtap="Btnstar2" data-index="{{index}}">
                  <view class="icon icon_yellow"></view>
                </view>
            
                <view wx:if="{{star_per>0}}" class="star" style="position:relative;">
                  　　　　　　　　　　　　<view class="icon icon_gray"></view>
                  　　　　　　　　　　　　<view class="icon icon_yellow " style="width:{{star_per}};overflow:hidden;position:absolute;left:0;top:0;"></view>
                  　　　　　　　　　 </view>
                <view class="star" wx:for="{{gray_star}}" bindtap="Btnstar" data-index="{{index}}">
                  <view class="icon icon_gray"></view>
                </view>
              </view>
            </view> -->
          </view>
          <!-- <textarea class="Psindf" bindinput="bindTextAreaBlur" placeholder="添加备注" /> -->
        </view>
      </view>
      <view class='toast-button'>
        <view class='button1'>
          <button catchtap='cancel'>取消</button>
        </view>
        <view class='button2'>
          <button catchtap='confil'>确定</button>
        </view>
      </view>
    </view>
  </view>
</block>