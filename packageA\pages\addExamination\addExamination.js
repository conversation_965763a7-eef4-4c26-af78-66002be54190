let app = getApp()
var ports = require("../../../utils/ports.js");


const statusList = [{
        value: 1,
        name: '新'
    },
    {
        value: 2,
        name: '已经取消'
    },
    {
        value: 3,
        name: '报名审核通过'
    },
    {
        value: 4,
        name: '报名审核拒绝'
    },
    {
        value: 5,
        name: '取消审核通过'
    },
    {
        value: 6,
        name: '取消审核拒绝'
    },
    {
        value: 10,
        name: '已经付款'
    },
    {
        value: 11,
        name: '已经退款'
    },
    {
        value: 15,
        name: '报名成功'
    },
    {
        value: 20,
        name: '签到完成'
    },
    {
        value: 21,
        name: '验票完成'
    },
    {
        value: 24,
        name: '考试完成'
    },
    {
        value: 25,
        name: '开票完成'
    },
    {
        value: 30,
        name: '完成'
    },
]

const payStatusList = [{
        value: 0,
        name: '未付款'
    },
    {
        value: 1,
        name: '已取消'
    },
    {
        value: 2,
        name: '已付款'
    },
    {
        value: 3,
        name: '付款过期'
    },
]

// packageA/pages/addExamination/addExamination.js
Page({

    /**
     * 页面的初始数据
     */
    data: {
        DateShow: false,
        minDate: new Date().getTime(),
        maxDate: new Date().getTime() + 2592000000,
        examPlanDate: '',
        form: {
            name: ''
        },
        trainCompanyID: '',
        trainCompany: '',
        showTrainCompany: false,
        memberTrainList: [],
        memberInfo:[],
    },

    //获取用户信息
  async getOneMemberDetail(){
    let params = {
      sessionID: wx.getStorageSync('USER_SESSIONID'),
      memberID: wx.getStorageSync('USER_MEMBERID')
    }
    let res = await ports.ModuleAll.getOneMemberDetail(params)
    console.log(res,'用户信息');
    const data = res.data.body
    this.setData({
      memberInfo:data
    })
    if (data.idStatus != 2) {
        wx.showModal({
          title: '提示',
          content: '使用钱包功能，您需要完成实名认证',
          confirmText: '前去认证',
          success(res) {
            if (res.confirm) {
              wx.navigateTo({
                url:'/packageA/pages/realName/realName'
              })
            }
          }
        })
    }
  },

    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {
        this.getMemberTrainList()
        this.getOneMemberDetail()
    },

    /**
     * 生命周期函数--监听页面初次渲染完成
     */
    onReady() {

    },

    /**
     * 生命周期函数--监听页面显示
     */
    onShow() {

    },

    /**
     * 生命周期函数--监听页面隐藏
     */
    onHide() {

    },

    /**
     * 生命周期函数--监听页面卸载
     */
    onUnload() {

    },

    /**
     * 页面相关事件处理函数--监听用户下拉动作
     */
    onPullDownRefresh() {

    },

    /**
     * 页面上拉触底事件的处理函数
     */
    onReachBottom() {

    },

    /**
     * 用户点击右上角分享
     */
    onShareAppMessage() {

    },

    showDate(e) {
        const currentDatePicker = e.currentTarget.dataset.datePicker
        console.log(11);
        this.setData({
            currentDatePicker,
            DateShow: true
        })
    },

    cancel() {
        this.setData({
            DateShow: false
        })
    },
    onChange(e) {
        console.log(e);
    },
    // onClose(e) {
    //     console.log(e);
    //     this.setData({
    //         showTrainCompany: false
    //     })
    // },
    onCancel() {
        this.setData({
            showTrainCompany: false
        })
    },
    confirmDate({
        detail
    }) {
        console.log(detail);
        // if (this.data.currentDatePicker === 'release') {
        this.setData({
            examPlanDate: this.formatDate(detail),
            DateShow: false
        })
        // }
        // if (this.data.currentDatePicker === 'start') {
        //     console.log(detail);
        //     // const endDate = this.data.form.endDate;
        //     // if (endDate && detail > endDate) {
        //     //     wx.showToast({
        //     //         title: '开始时间不能晚于结束时间',
        //     //         icon: 'none',
        //     //         mask: true
        //     //     })
        //     //     return
        //     // }
        //     this.setData({
        //         showStartDate: this.formatDate(detail),
        //         DateShow: false
        //     })
        // }
        // if (this.data.currentDatePicker === 'end') {
        //     // const startDate = this.data.form.startDate;
        //     // if (startDate && detail < startDate) {
        //     //     wx.showToast({
        //     //         title: '结束时间不能早于开始时间',
        //     //         icon: 'none',
        //     //         mask: true
        //     //     })
        //     //     return
        //     // }
        //     this.setData({
        //         showEndDate: this.formatDate(detail),
        //         DateShow: false
        //     })
        // }
    },
    changeExaminationName(e) {
        let value = e.detail.value;
        this.setData({
            'form.name': value
        })
    },
    getMemberTrainList() {
        var that = this
        let params = {
            sessionID: wx.getStorageSync('USER_SESSIONID'),
            joinMemberID: wx.getStorageSync('USER_MEMBERID'),
            sortTypeTime: 1,
            pageNumber: 999
        }
        ports.ModuleAll.getMemberTrainList(params).then(res => {
          const rows = res.data.body.data.rows || [];
          const filtered = rows.filter(item => !item.governmentCompanyID);
          const list = filtered.map(item => {
            item.statusText    = this.filtersList(item.status,    statusList);
            item.payStatusText = this.filtersList(item.payStatus, payStatusList);
            return {
              text:  item.name,
              value: item.trainCompanyID
            };
          });
          this.setData({
            memberTrainList: list
          });
          console.log(this.data.memberTrainList, 'memberTrainList');
        })
    },
    filtersList(value, arr) {
        if (!value) return '无'
        for (let i = 0; i < arr.length; i++) {
            if (arr[i].value == value) {
                return arr[i].name; // 找到之后就使用return返回找到的项，跳出循环
            }
            if (i == arr.length - 1 && arr[i].value != value) return '无'
        }
    },

    onConfirm(e) {
        console.log(e);
        let {
            text,
            value
        } = e.detail.value;
        console.log(value, text, "ssdsdsd");
        this.setData({
            trainCompanyID: value,
            trainCompany: text,
            showTrainCompany: false
        })

    },
    showTrainCompany() {
        this.setData({
            showTrainCompany: true
        })
    },

    async toAddCertificate() {
        console.log(this.data.form);
        if (this.data.memberInfo.idStatus != 2) {
          wx.showModal({
            title: '提示',
            content: '使用钱包功能，您需要完成实名认证',
            confirmText: '前去认证',
            success(res) {
              if (res.confirm) {
                wx.navigateTo({
                  url:'/packageA/pages/realName/realName'
                })
              }
            }
          })
          return
        }
        let res = await ports.ModuleAll.createOneExamApply({
            memberID: wx.getStorageSync('USER_MEMBERID'),
            name: this.data.form.name,
            examPlanDate: this.data.examPlanDate + ' 00:00:00',
            trainCompanyID: this.data.trainCompanyID,
            examName:this.data.memberInfo.name,
            examTel:this.data.memberInfo.phone,
            examIDCard:this.data.memberInfo.idNumber,
        })
            console.log(res, "nnfffff");
            if (res.data.header.code === 0) {
                wx.showToast({
                    icon: 'success',
                    title: '创建成功',
                })
                let pages = getCurrentPages();
                let beforePage = pages[pages.length - 2];
                beforePage.onLoad()
                setTimeout(() => {
                    wx.navigateBack()
                }, 2000)
            } else {
                wx.showToast({
                    icon: 'none',
                    title: res.data.body.msg,
                })
            }
    },


    formatDate(timestamp) {
        const date = new Date(timestamp);
        return `${date.getFullYear()}-${new String(date.getMonth() + 1).padStart(2,0)}-${new String(date.getDate()).padStart(2,0)}`
    }

})