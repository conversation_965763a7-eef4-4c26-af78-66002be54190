var ports = require("../../../utils/ports")
const utils = require("../../../utils/util")
var longitude = require('@/utils/longitude.js');
const app = getApp();


var redis =require('@/utils/redis')
const defaultCheckLearnType = [
  {value: '1', name: '实训/学习',checked:true, workType : '3'},
  {value: '2', name: '考试',checked:false, workType : '10'},
];
let noEditData= {}; // 临时储存的未被修改的data数据
let noEdit_checkedTime= {}; // 临时储存的未被修改的本地保存checktime
let noEdit_checkedWorkType={}; // 临时储存的未被修改的本地保存checkedWorkType
let noEdit_checkedMachine={}; // 临时储存的未被修改的本地保存checkedMachine
// packageA/pages/appointmentConfirm/appointmentConfirm.js
Page({
  /**
   * 页面的初始数据
   */
  data: {
    checkedOrInvite:1,
    friendList:[],
    friendShowModal:false,
    checkType:'',
    isCustomShare:false,
    orderSeqForShare:'',
    friend1MemberID:'',
    friend1Name:'',
    friend2MemberID:'',
    friend2Name:'',
    btn1:3,
    btn2:3,
    btn3:3,
    btn4:3,
    useE:{},
    logoImg: app.logoimg,
    showMemberGroup:false,
    newOrderSeq:'1',// 这个是他预约了哪个位置，在前一个页面进行选择
    enterStep:0,//这个表示他是预约完了直接进入还是说在这个页面点击进入按钮进入 的，需要确定他进入的是哪个页面
    bluetoothDeviceName:'',
    tabIndex:'',
    orderSeq:'',
    selectItem:{},
    appointmentID:"",
    sensorList:[],
    getLocationFlag:false,
    latitude:'',
    longitude:'',
    trainBusinessInfo:{},
    trainBusinessInfoLoading: true, // 添加培训业务信息加载状态
    isVisit:true,
    // 用appointmentSteps来判断用户是来预约的还是来学习的
    // 如果当前时段（用户选择的时段）用户没有预约字段值为0，那么显示预约按钮，如果当前时段用户预约了字段值为1，那么就可以去学习，同时，如果用户还没有购买这节课字段值为2，那么显示购买课程
    appointmentSteps:0,
    appointmentNumber:'',
    nextBeginTime:'',
    nextEndTime:'',
    info:{},
    checkLearnType: defaultCheckLearnType,
    checkedLearnTypeName: defaultCheckLearnType[0]['name'],
    checkedMachine:{
      id:'19d28aa3912b4e29989257c48594ebcb',
      machineName:'上海电信001',
      machineDistance:""
    },// 用户选择的机器,从缓存中获取
    learnTypes:[{name:"学习",id:1,checked:false},{name:"实训",id:2,checked:true},{name:"考试",id:3,checked:false}],
    learnTypes:[{name:"在线学习",id:1,checked:false},{name:"实训操作",id:2,checked:true},{name:"模批考试",id:3,checked:false}],
    learnType:2,
    trainBusinessID:"85c6a3482f72480eb2b7ac547e344dca",
    list1:[],
    list2:[],
    isLoginVisible:false,//false代表已经登陆
    loading:false,
    isDrawerOpen: false,
    isWorkTypeOpen:false,

    // 预约时间段页面迁移过来的data
    flag: false,
    beforeFlag: false,
    afterFlag: false,
    chnageFlag: true,
    timeList: [],
    memberGroupList: [],
    memberList: [],
    machineID: '',
    machineName: '',
    trainCompanyDetail: {},
    machineDetail: {},
    openDate:'',
    closeDate:'',
    selectedDate: '',
    current:0,
    show: true,
    disable: false,
    visible: true,
    workTypeList: [
      {code:0,name:"未选择"},
      {code:1,name:"不限制"},
      {code:2,name:"学习"},
      {code:3,name:"实训"},
      {code:10,name:"考试"},
    ],
    workType:'3',
    checkedMachine:{},
    nextBeginTime:'',
    nextEndTime:'',
    distance:'',
    fontSize:'',
    height: app.globalData.systemInfo.statusBarHeight,
    getCityshow:false,
    city:{},
    friend1:{},
    friend2:{},
    _discoveryStarted:'',
    isEdit:false, // 修改状态
    searchFlag: true,

  },
  /**
   * 创建一名会员培训记录
   * @param {Object} info 
   */
  async handleCreateOneMemberTrain(info) {
    if (!wx.getStorageSync('deviceID')) this.getDevice();
    if (!wx.getStorageSync('USER_MEMBER')) await utils.LOGIN(0);
    const resTwo = await ports.ModuleAll.getTrainBusinessDetail({
      trainBusinessID: info.trainBusinessID
    })
    if (!resTwo.data?.body?.trainBusiness?.myMemberTrainID) {
      let params = {
        siteID: app.siteID,
        shopID: app.shopID,
        buyType: 1, //类型为培训业务
        trainBusinessID: info.trainBusinessID,
        joinMemberID: wx.getStorageSync('USER_MEMBERID') || '',
        joinStudentID: wx.getStorageSync('USER_STUDENT').studentID || '',
        customerCompanyID: wx.getStorageSync('USER_STUDENT').companyID || '',
        applyType: 1,
        status: 15,
        bizType: info.bizType,
      }
      await ports.ModuleAll.createOneMemberTrain(params)
    }

  },
  // 我要报名/立即预约
  async initPage(){
    let p = {
      machineID:this.data.checkedMachine.id
    }
    if(!p.machineID) return
   const resOne = await ports.ModuleAll.getMachineDetail(p)
   let info = resOne.data?.body?.data || {}
   this.setData({
     info:info,
     // loading: true
     trainBusinessID:info.trainBusinessID,
   })
    //根据 machine的 bizType=1
    // 自动 给 trainBusinessID创建1个 memberTrain 记录 。注意 报名类型=赠送，状态=报名成功
    if(this.data.checkedMachine.bizType==1){
      await this.handleCreateOneMemberTrain(info)
    }
   const resTwo = await ports.ModuleAll.getTrainBusinessDetail({
     trainBusinessID: info.trainBusinessID
   })
   this.setData({
    trainBusinessInfo: resTwo.data?.body?.trainBusiness || {},
    trainBusinessInfoLoading: false // 数据加载完成
  })
  console.log('trainBusinessID',info.trainBusinessID,'trainBusinessInfo.myMemberTrainID',resTwo.data?.body?.trainBusiness?.myMemberTrainID)
  // this.getTrainSection()
   if (!wx.getStorageSync('USER_SESSIONID')) {
     this.setData({isLoginVisible : true})
     return
   }
   return info
  },
  toasttext(){
    wx.showToast({
      title: '报名未付款,不能预约。',
      icon:'none'
    })
  },
  getLocation(){
    wx.getLocation({
      type: 'gcj02', // 默认为 wgs84 返回 gps 坐标，gcj02 返回可用于 wx.openLocation 的坐标
      altitude: true, // true 会返回高度信息
      isHighAccuracy: true, //开启高精度定位
      highAccuracyExpireTime: 3100, //高精度定位超时时间(ms)
      success: (res)=> {
        console.log(this.data.distance, 'data.distance-');
        let distance = longitude.calculateDistance(this.data.info.mapY,this.data.info.mapX,res.latitude,res.longitude)
        console.log(res, '获取位置成功');
        console.log({mapY:this.data.info.mapY,mapX:this.data.info.mapX,latitude:res.latitude,longitude:res.longitude})

        this.setData({
          latitude:res.latitude,
          longitude:res.longitude,
          getLocationFlag:true,
          distance
        })


        wx.showToast({
          title: '获取距离成功',
        })
        wx.stopPullDownRefresh();
        // this.checkCanIn()
      },
      fail: (err) => {
        console.error(err)
        console.log('获取距离失败');
        this.setData({
          getLocationFlag:false
        })
        wx.showToast({
          title: '获取距离失败',
          icon:'error'
        })
        //停止下拉刷新
        wx.stopPullDownRefresh();
      }
    })
  },
  hideModal(){
    this.setData({
      showMemberGroup:false
    })
  },
  chooseTime(){
    this.setData({
      isDrawerOpen: !this.data.isDrawerOpen,
    })
    const child = this.selectComponent('#appointmentBox');
    if (child) {
      child.loadForTimes(); // 调用子组件的方法
    }
  },
  getTimes(){
    let checkedTime = wx.getStorageSync('checkedTime')
    const {beginTime,endTime} = checkedTime.showTime ? this.formatShowTime(checkedTime.showTime,checkedTime.openDate) :{}
    this.setData({
      nextBeginTime:beginTime,
      nextEndTime:endTime,
      appointmentNumber:checkedTime.checkedTime
    })
    checkedTime.nextBeginTime = beginTime
    checkedTime.nextEndTime = endTime
    wx.setStorageSync('checkedTime', checkedTime)    
    if(checkedTime.myAppointmentID){
      this.setData({
        appointmentSteps:1
      })
      console.log(this.appointmentSteps);
    }else{
      this.setData({
        appointmentSteps:0
      })
      this.setData({
        // checkLearnType: defaultCheckLearnType,
        checkedOrInvite:1
      })
      console.log(this.appointmentSteps);
    }
  },

  // 如果没登陆就设置为true
  toLogin(){
    this.setData({
      isLoginVisible : true
    })
  },
  // 如果登录了，需要把这个值变回来
  hasLogin(){
    this.setData({
      isLoginVisible : false
    })
  },
  goLogin(){
    wx.navigateTo({
      url: '../../../packageA/pages/login/login',
    })
  },
   formatDistace(distanceInMeters) {
    if (distanceInMeters >= 1000) {
      // 将距离从米转换为千米，并保留两位小数
      const distanceInKilometers = (distanceInMeters / 1000).toFixed(2);
      return `${distanceInKilometers} 千米`;
    } else {
      return `${distanceInMeters} 米`;
    }
  },
  closeDrawer(){
    this.setData({
      isDrawerOpen: !this.data.isDrawerOpen,
    })
    let checkedTime = wx.getStorageSync('checkedTime')
    const {beginTime,endTime} = this.formatShowTime(checkedTime.showTime,checkedTime.openDate)
    this.setData({
      nextBeginTime:beginTime,
      nextEndTime:endTime,
      appointmentNumber:checkedTime.checkedTime
    })
    checkedTime.nextBeginTime = beginTime
    checkedTime.nextEndTime = endTime
    wx.setStorageSync('checkedTime', checkedTime)    
    if(checkedTime.myAppointmentID){
      this.setData({
        appointmentSteps:1
      })
      console.log(this.appointmentSteps);
    }else{
      this.setData({
        appointmentSteps:0
      })
      console.log(this.appointmentSteps);
    }
  },
  formatShowTime(showTime,openDate){
    let times = showTime.split('-')
    let months = openDate.split('-')
    const today = new Date();
    const currentDate =  (today.getMonth() + 1) + '月' + today.getDate() + '日'
    // 构造 beginTime 字符串
    const beginTime = `${months[1]}月${months[2]}日 ${times[0]}`;
    // 对于 endTime，只保留时间部分
    const endTimeFormatted = `${times[1].split(':')[0]}:${times[1].split(':')[1]}`;
    return {
      beginTime,
      endTime: endTimeFormatted
    };
  },
  toRegister(e) {
    let previousUrl = `/pages/index/index`
    let url = `/packageA/pages/trainBusinessDetail/trainBusinessDetail?id=${this.data.trainBusinessID}&mid=${this.data.info.machineID}&url=${previousUrl}`
    console.log('trainBusinessInfo.myMemberTrainID 跳转的trainBusinessID', this.data.trainBusinessID)
    wx.navigateTo({
      url
    })
  },
  /**
   * 生命周期函数--监听页面加载
   * nextBeginTime=${this.data.nextBeginTime}
   * &nextEndTime=${this.data.nextEndTime}
   * &orderSeq=1
   * &friend1MemberID=${this.data.friend1.toMemberID?this.data.friend1.toMemberID:0}
   * &friend1Name=${this.data.friend1.toMemberName?this.data.friend1.toMemberName:0}
   * &friend2MemberID=${this.data.friend2.toMemberID?this.data.friend2.toMemberID:0}
   * &friend2Name=${this.data.friend2.toMemberName?this.data.friend2.toMemberName:0}
   * &workType=${this.data.checkedLearnType==1?3:10}`
   */
  onLoad(options) {
    console.warn('onLoad-路由参数',options)
    let {nextBeginTime,nextEndTime,workType,city,checkedMachine,distance,friend1,friend2}  = decodeObjectValues(options)
    console.log('city---',city)
    console.log('checkedMachine---',checkedMachine)
    
    const checkedWorkType = wx.getStorageSync('checkedWorkType')
    const checkedLearnType = checkedWorkType.type;
    const checkedLearnTypeName = checkedWorkType.workTypeName;
    const checkLearnType =this.data.checkLearnType.map(item=>{
      return {
        ...item,
        checked:item.workType== checkedWorkType.workType
      }
    })
    console.log('checkLearnType',checkLearnType)
    
    console.log('this.data.info.distance',distance,distance && !isNaN(Number(distance)) )
    // if(distance && distance != 9999 && !isNaN(Number(distance)) ){
    //   distance = this.formatDistace(Number(distance))
    // }
    let _distance = isNaN(Number(distance)) ? 9999 : Number(distance);
    const friend1MemberID = friend1.toMemberID;
    const friend1Name = friend1.toMemberName;
    const friend2MemberID = friend2.toMemberID;
    const friend2Name = friend2.toMemberName;

    console.log('checkedLearnTypeName',checkedLearnTypeName)
    
    noEdit_checkedTime = wx.getStorageSync('checkedTime');
    noEdit_checkedWorkType = wx.getStorageSync('checkedWorkType');
    noEdit_checkedMachine = wx.getStorageSync('checkedMachine');
    noEditData = JSON.parse(JSON.stringify({
      nextBeginTime,
      nextEndTime,
      workType,
      city,
      checkedMachine,
      distance: _distance,
      checkLearnType,
      checkedLearnType,
      checkedLearnTypeName,
      friend1,
      friend2,
      friend1MemberID,
      friend1Name,
      friend2MemberID,
      friend2Name,
    }))
    this.showNoEditPage(noEditData)
    
    // console.log('adjustFontSize(this.checkedMachine.machineName)',adjustFontSize(this.data.checkedMachine.machineName))
   
    
  },





  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },
  // 未被修改的页面
  showNoEditPage(noEditData){
    console.log(111,'noEditData',noEditData)
    wx.setStorageSync('checkedTime',noEdit_checkedTime);
    wx.setStorageSync('checkedWorkType',noEdit_checkedWorkType);
    wx.setStorageSync('checkedMachine',noEdit_checkedMachine);

    this.setData({
      ...noEditData,
      trainBusinessInfoLoading: true // 确保加载状态为true
    },async()=>{
      let info = {}
      if(noEditData.checkedMachine.id) {
        info = await this.initPage();
      } else {
        // 如果没有机器ID，直接设置加载完成
        this.setData({
          trainBusinessInfoLoading: false
        });
      }
      if(noEditData.distance == 9999) info?.mapX && info?.mapY && this.getLocation()
    })
  },
  goApp(e){
    // 如果他有学习小组 并且他选择的学习位置是实训位置或者学考位1
    // 不要这个了
    // if(this.data.memberGroupList.length>0&&(this.data.newOrderSeq==1||this.data.newOrderSeq==4)){
    //   this.setData({
    //     showMemberGroup:true,
    //     useE:e
    //   })
    // }else{
    //   this.goAppLast(e)
    // }
    this.goAppLast(e)
  },
  checkMyGroup(e){
    wx.showModal({
      title: '提示',
      content: '是否为'+e.currentTarget.dataset.item.name+'小组成员进行预约？',
      complete: (res) => {
        if (res.cancel) {
          console.log('取消')
        }
    
        if (res.confirm) {
          this.checkMyGroupNew(e)
        }
      }
    })
  },
  checkMyGroupNew(e){
    let memberGroup = e.currentTarget.dataset.item
    // 初始有三个可预约的，但是需要判断目前可预约的有几个
    let canUseApp = 3
    // 这里的btn123其实就是在首页传过来的三个btn代表三个学考位的状态，learn4指的是实训位
    let learn1=this.data.btn1
    let learn2=this.data.btn2
    let learn3=this.data.btn3
    let learn4=this.data.btn4
    // 由于只能预约三个人，所以需要判断还能预约几个
    // 传过来的状态如果是3代表这个不能再预约了
    // learn1(学考位1)和learn4(实训位1)他们的空间是绑定的，所以只需要判断一个就可以了
    if(learn1==3 || learn4==3){
      canUseApp = canUseApp-1
    }
    if(learn2==3){
      canUseApp = canUseApp-1
    }
    if(learn3==3){
      canUseApp = canUseApp-1
    }
    // sonlist应该是包含群主在内的
    if(memberGroup.sonList.length>canUseApp){
      wx.showModal({
        title: '提示',
        content: '您选择的学习小组（包括您在内）总人数大于可预约数：'+canUseApp+'，无法预约，请选择其他的学习小组',
        complete: (res) => {
          if (res.cancel) {
            
          }
      
          if (res.confirm) {
            
          }
        }
      })
      return
    }
    let checkedItem = this.data.useE.currentTarget.dataset.item
    let item = wx.getStorageSync('checkedTime')
    let workType = this.data.learnType == 1 ? 2 : this.data.learnType == 2 ?3 : 10
    let machineDetail = this.data.info
    // 在创建预约的时候，给群主预约他选择的位置，其他人预约剩余可选的位置
    let obj = {
      name:item.timeIntervalDefineName,
      sessionID: wx.getStorageSync('USER_SESSIONID'),
      companyID: app.companyID,
      objectDefineID: app.machineObjectDefineID,
      objectID: machineDetail.machineID,
      objectName: machineDetail.name,
      customerCompanyID: machineDetail.customerCompanyID,
      playCompanyID: machineDetail.playCompanyID,
      distributorCompanyID: machineDetail.distributorCompanyID,
      applyMemberID: wx.getStorageSync('USER_MEMBERID'),
      openDate: utils.formatDate(new Date().getTime(), 'yyyy-MM-dd'),
      timeIntervalInstanceID:item.timeIntervalInstanceID,
      timeIntervalName:item.timeIntervalDefineName,
      workType:workType,
      memberID : wx.getStorageSync('USER_MEMBERID'),
      orderSeq : this.data.newOrderSeq,
      resourcesObjectDefineID : '8a2f462a5c820afb015c8237567e1540',
      resourcesObjectID : checkedItem.sectionID,
      resourcesObjectName : checkedItem.name
    }
    // 先给我（群主）预约
    ports.ModuleAll.createOneAppointment(obj).then(res=>{
      if(res.data.header.code == 0){
        // 预约成功 接着给组员预约
        let sonList = memberGroup.sonList
        Promise.all(
          sonList.forEach(member=>{
            // 这个时候，最多只能预约两个人了：学考位2和学考位3`
            // 只看组员的不看群主的
            if(member.memberID!=wx.getStorageSync('USER_MEMBERID')){
              console.log(member);
              if(this.data.btn2!=3){
                obj.memberID = member.memberID
                // orderSeq就是预约的哪个位置，如果是2号位，那么orderSeq就是2
                obj.orderSeq = 2
                return new Promise((resolve,reject)=>{
                  ports.ModuleAll.createOneAppointment(obj).then(res=>{
                    if(res.data.header.code == 0){
                      // 预约成功之后把btn2状态改为3
                      this.setData({
                        btn2:3
                      })
                      resolve(res)
                    }
                  })
                })
              }else if(this.data.btn3!=3){
                obj.memberID = member.memberID
                // orderSeq就是预约的哪个位置，如果是3号位，那么orderSeq就是3
                obj.orderSeq = 3
                return new Promise((resolve,reject)=>{
                  ports.ModuleAll.createOneAppointment(obj).then(res=>{
                    if(res.data.header.code == 0){
                      // 预约成功之后把btn2状态改为3
                      this.setData({
                        btn3:3
                      })
                      resolve(res)
                    }
                  })
                })
              }
            }
          })
        ).then(newRes => {
          console.log(newRes,'批量预约');
          wx.showToast({
            title: '预约成功',
            icon:'success'
          })
        })
        this.setData({
          showMemberGroup:false
        })
        let appointmentID = res.data.body.appointmentID
        this.setData({
          appointmentID:appointmentID,
          enterStep:1
        })
        // 返回前设置标志
        wx.setStorageSync('refreshData', true);
        let checkedTime = wx.getStorageSync('checkedTime')
        checkedTime.myAppointmentID=res.data.body.appointmentID
        wx.setStorageSync('checkedTime', checkedTime)
        this.getTimes()
      }else{
        wx.showToast({
          title: '预约失败',
          icon:'error'
        })
      }
    })
  },
  appForMe(e){
    this.setData({
      showMemberGroup:false,
    })
    this.goAppLast(this.data.useE)
  },
   goAppLast(e){
    let that = this
    const checkedWorkType = wx.getStorageSync('checkedWorkType')?.workType;
    // if(checkedWorkType == 10 && this.data.learnType!=3){
    //   wx.showModal({
    //     title: '请注意',
    //     content: '该场次为考试专场，无法预约别的学习项目，请重新选择',
    //     complete: (res) => {
    //       if (res.cancel) {
    //         console.log('取消');
    //       }
    //       if (res.confirm) {
    //         console.log('确定');
    //       }
    //     }
    //   })
    //   return
    // }
    let text = ''
    // switch(this.data.newOrderSeq){
    //   case '1':
    //     text = "学考位1+实训位1"
    //     break
    //   case '2':
    //     text = '学考位2'
    //     break
    //   case '3':
    //     text = '学考位3'
    //     break
    //   case '4':
    //     text = '实训位1,注意：预约实训位1后其他人将无法预约其他学考位'
    //     break
    // }
    let friend1=false
    let friend2=false
    // if(this.data.friend1MemberID=='0'&&this.data.friend2MemberID=='0'){
    if(!this.data.friend1MemberID&&!this.data.friend2MemberID){
      text='是否为您自己预约？'
    }else if(this.data.friend1MemberID&&!this.data.friend2MemberID){
      text='是否为您和'+this.data.friend1Name+'(2号学习位)一起预约？'
      friend1=true
    }else if(!this.data.friend1MemberID&&this.data.friend2MemberID){
      text='是否为您和'+this.data.friend2Name+'(2号学习位)一起预约？'
      friend2=true
    }else if(this.data.friend1MemberID&&this.data.friend2MemberID){
      text='是否为您和'+this.data.friend1Name+'(2号学习位)以及'+this.data.friend2Name+'(3号学习位)一起预约？'
      friend1=true
      friend2=true
    }
    console.log("text:",text);
    wx.showModal({
      title: '提示',
      content: text,
      complete: async (res) => {
        if (res.cancel) {
          console.log('用户点击取消')
        }
    
        // 预约
        if (res.confirm) {
          
          const memberTrainDetail= await this.getMemberTrain(wx.getStorageSync('USER_MEMBERID'))
          
          console.log("memberTrainDetail:",memberTrainDetail);

            // let checkedItem = e.currentTarget.dataset.item
          let item = wx.getStorageSync('checkedTime')
          // let workType = this.data.learnType == 1 ? 2 : this.data.learnType == 2 ?3 : 10
          let workType = wx.getStorageSync('checkedWorkType')?.workType
          // let workType = this.data.workType
          let machineDetail = that.data.info
          const applyMemberName=wx.getStorageSync('USER_MEMBER').name;
          let obj = {
            name:item.timeIntervalDefineName,
            sessionID: wx.getStorageSync('USER_SESSIONID'),
            // applicationID: app.applicationID,
            companyID: app.companyID,
            objectDefineID: app.machineObjectDefineID,
            objectID: machineDetail.machineID,
            objectName: machineDetail.name,
            // resourcesObjectDefineID: app.trainBusinessObjectDefineID,
            // resourcesObjectID: this.data.trainBusinessID,
            customerCompanyID: machineDetail.customerCompanyID,
            playCompanyID: machineDetail.playCompanyID,
            distributorCompanyID: machineDetail.distributorCompanyID,
            applyMemberID: wx.getStorageSync('USER_MEMBERID'),
            // governmentCompanyID: trainCompanyDetail.toCompanyID,
            openDate: utils.formatDate(item.openDate, 'yyyy-MM-dd'),
            timeIntervalInstanceID:item.timeIntervalInstanceID,
            timeIntervalName:item.timeIntervalDefineName,
            workType:workType, 
            memberID : wx.getStorageSync('USER_MEMBERID'),
            orderSeq : 1,
            resourcesObjectDefineID : '8a2f462a5c820afb015c8237567e1540',
            // 不再显示预约的课程，预约到时间和空间
            // resourcesObjectID : checkedItem.sectionID,
            // resourcesObjectName : checkedItem.name
            studentID:memberTrainDetail.joinStudentID||'',
            governmentCompanyID:memberTrainDetail.governmentCompanyID||'',
            isLimit:1,
          }
          console.log("obj:",obj);
          ports.ModuleAll.createOneAppointment(obj).then(async res => {
            if (res.data.header.code == 0) {
              wx.showToast({
                title: '预约成功'
              })
              let appointmentID = res.data.body.appointmentID
              this.setData({
                appointmentID:appointmentID,
                enterStep:1
              })
              // 返回前设置标志
              wx.setStorageSync('refreshData', true);
              let checkedTime = wx.getStorageSync('checkedTime')
              checkedTime.myAppointmentID=res.data.body.appointmentID
              wx.setStorageSync('checkedTime', checkedTime)
              //预约成功了帮好友预约
              if (friend1) {
                //好友1memberTrain
                const memberTrainDetail1 = await this.getMemberTrain(this.data.friend1MemberID)
                obj.orderSeq = 2
                obj.memberID = this.data.friend1MemberID
                obj.studentID = memberTrainDetail1.joinStudentID || ''
                obj.governmentCompanyID = memberTrainDetail1.memberTrainDetail || ''
                ports.ModuleAll.createOneAppointment(obj)
                let params = {
                  senderMemberID: wx.getStorageSync('USER_MEMBERID'),
                  name: `你的好友${applyMemberName}帮你预约${obj.objectName}:${obj.name}的${obj.orderSeq}学考位`,
                  receiverType: 2,
                  code: 351,
                  receiverMemberID: obj.memberID

                }
                ports.ModuleAll.sendOneMessage(params)
              }
              if (friend2) {
                const memberTrainDetail2 = await this.getMemberTrain(this.data.friend2MemberID)
                obj.orderSeq = 3
                obj.memberID = this.data.friend2MemberID
                obj.studentID = memberTrainDetail2.joinStudentID || ''
                obj.governmentCompanyID = memberTrainDetail2.memberTrainDetail || ''
                ports.ModuleAll.createOneAppointment(obj)
                let params = {
                  senderMemberID: wx.getStorageSync('USER_MEMBERID'),
                  name: `你的好友${applyMemberName}帮你预约${obj.objectName}:${obj.name}的${obj.orderSeq}学考位`,
                  receiverType: 2,
                  code: 351,
                  receiverMemberID: obj.memberID
                }
                ports.ModuleAll.sendOneMessage(params)
              }
              // this.getTimes()
              this.enterDoor()
            } else {
              wx.showToast({
                title: res.data.header.msg || '预约失败',
                icon: 'none'
              })
            }
          })
          
          
          

      
        }
      }
    })
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    console.warn('onShow')
    let that = this
    let checkedMachine = wx.getStorageSync('checkedMachine')
    let checkedTime = wx.getStorageSync('checkedTime')
    this.setData({
      checkedMachine:checkedMachine
    })
    // 如果这里不为null，则不显示预约按钮。而是直接就是学习、实训、考试
    if(checkedTime.myAppointmentID){
      this.setData({
        appointmentSteps:1
      })
    }else{
      this.setData({
        appointmentSteps:0
      })
    }
    let p = {
      machineID:checkedMachine.id
    }
    ports.ModuleAll.getMachineDetail(p).then(async(resOne) => {
      let info = resOne.data.body.data
      this.setData({
        info:info,
        trainBusinessID:info.trainBusinessID,
        // loading: true
      })
      const resTwo = await ports.ModuleAll.getTrainBusinessDetail({
        trainBusinessID: info.trainBusinessID
      })
      this.setData({
       trainBusinessInfo: resTwo.data?.body?.trainBusiness || {}
     })
     console.log('trainBusinessID',info.trainBusinessID,'trainBusinessInfo.myMemberTrainID',resTwo.data?.body?.trainBusiness?.myMemberTrainID)

      if (!wx.getStorageSync('USER_SESSIONID')) {
        this.showLogin()
      }
      this.loadForTimes()
    })
  },
  async showLogin(){
    this.setData({
      isLoginVisible : true
    })
  },
  async notShowLogin(){
    this.setData({
      isLoginVisible : false
    })
  },

 async getMemberTrain(joinMemberID){
    let params = {
      applicationID: app.applicationID,
      trainBusinessID: this.data.trainBusinessID,
      joinMemberID,
      sortTypeTime:1,
      currentPage:1,
      pageSize:1
    }
    const res =await ports.ModuleAll.getMemberTrainList(params)
    if(res.data.header.code!=0){
      throw new Error(res.data.header.msg)
    }
    return res.data.body?.data?.rows?.[0]  || ''
  },

  // -->>下面的时从appointment.js迁移过来的方法，可能有改动
    //点击切换样式

    loadForTimes(){
      let nowTime = utils.formatDate(new Date().getTime(), 'yyyy-MM-dd')
      let futureTime = utils.formatDate(new Date().getTime()+1296000000, 'yyyy-MM-dd')
      // TODO:?? trainBusinessID 这里写死跳转到下一页页面传参不对
      // this.setData({
      //   // machineID: options.machineID,
      //   // machineName: options.machineName,
      //   // trainBusinessID: options.trainBusinessID,
      //   machineID:"19d28aa3912b4e29989257c48594ebcb",
      //   machineName:"上海电信001",
      //   trainBusinessID:"85c6a3482f72480eb2b7ac547e344dca",
      //   openDate: nowTime,
      //   selectedDate: nowTime,
      //   closeDate: futureTime,
      // })

      // this.before1day(this.data.selectedDate,this.data.openDate)
      // this.after1day(this.data.selectedDate,this.data.closeDate)
      this.setData({
        openDate: nowTime,
        selectedDate: nowTime,
        closeDate: futureTime,
      })
      let selectedDate = this.data.selectedDate || nowTime;
      let openDate = this.data.openDate || nowTime;
      let closeDate = this.data.closeDate || futureTime;
      this.before1day(selectedDate,openDate)
      this.after1day(selectedDate,closeDate)

      this.getDetail()
      // this.getMemberTrain()
      this.getTimeIntervalInstanceList()
      this.getMemberGroupList()
    },

    wordShow(e) {
      // console.log(e.currentTarget.dataset.index)
      let idx = e.currentTarget.dataset.index;
      let arr = [...this.data.timelist];
      let obj = {};
      arr.forEach((item, index) => {
        if (idx === index) {
          obj.word = item.word;
          obj.show = !item.show;
          arr.splice(index, 1, obj);
          console.log(obj);
          obj = {};
        }
      });
      this.setData({
        arr
      })
    },
    getDetail() {
      // debugger
      let paramsAD = {
        sessionID: wx.getStorageSync('USER_SESSIONID'),
        id: this.data.checkedMachine.machineID,
      }
      ports.ModuleAll.getOneMachineDetail(paramsAD).then(resAD => {
        // console.log(resAD);
        this.setData({
          machineDetail: resAD.data.body.data
        })
      })
    },
    getMemberTrainList() {
      let params = {
        applicationID: app.applicationID,
        sessionID: wx.getStorageSync('USER_SESSIONID'),
        trainBusinessID: this.data.info.trainBusinessID,
        joinMemberID: wx.getStorageSync('USER_MEMBERID')
      }
      ports.ModuleAll.getMemberTrainList(params).then(res => {
        
        let list = res.data.body.data.rows || []
        this.setData({
          flag: list.length === 0 ? false : true
        })
        // this.setData({
        //   flag: true
        // })
      })
    },
    getMemberGroupList() {
      let params = {
        applicationID: app.applicationID,
        sessionID: wx.getStorageSync('USER_SESSIONID'),
        headMemberID: wx.getStorageSync('USER_MEMBERID'),
        pageNumber: 99,
        sortTypeTime: 2
      }
      params.headMemberID = wx.getStorageSync('USER_MEMBERID')
      ports.ModuleAll.getMemberGroupList(params).then(res => {
        if (res.data.header.code == 0) {
        let memberGroupList = res.data.body.data.rows || []

        Promise.all(
          memberGroupList.map((item, index) => {
            return new Promise((resolve, reject) => {
              // 获取课程列表
              ports.ModuleAll.getMemberGroupJoinList({
                sessionID: wx.getStorageSync('USER_SESSIONID'),
                memberGroupID: item.memberGroupID,
                // memberGroupID: '29827e7aedb043f5a67fd9a701d9bc1a',
                pageNumber: 99,
                sortTypeTime: 2
              }).then(res => {
                if (res.data.header.code == 0) {
                  let list = res.data.body.data.rows || []
                  memberGroupList[index].sonList = list
                  resolve(memberGroupList[index].sonList)
                }
              })
            })
          })).then(newRes => {
          this.setData({
            memberGroupList,
            chooseStatus:true
          })
        })
      }else{
        console.log('getMemberGroupList-',res)
      }

      })
    },
    getTimeIntervalInstanceList(){
      var that = this
      this.setData({
        timeList: []
      })
      let params = {
        applicationID: app.applicationID,
        sessionID: wx.getStorageSync('USER_SESSIONID'),
        applicationID:app.applicationID,
        companyID:app.companyID,
        memberID:wx.getStorageSync('USER_MEMBERID'),
        pageNumber:24,
        objectID: this.data.checkedMachine.machineID,
        opendateBeginTime: this.data.selectedDate,
        opendateEndTime: utils.formatDate(new Date(this.data.selectedDate).getTime()+86400000, 'yyyy-MM-dd'),
        // sortTypeopenDate: 1,
        // objectDefineID:app.machineObjectDefineID,
        // objectID:this.data.checkedMachine.machineID,
        // openDate:this.data.openDate
      }
      wx.showLoading({
        title: '',
        mask: true,
      })
      ports.ModuleAll.getTimeIntervalInstanceList(params).then(res=>{
        wx.hideLoading()
        console.log(res,'时间段');
        let timeList = res.data.body.data.rows || []
        timeList.sort((a, b) => a.code - b.code)
        timeList.forEach(item=>{
          item.showTime = item.beginHour +'-'+ item.endHour
          item.check = item.myAppointmentID?true:false
          item.disable = item.myAppointmentID?true:false
          item.visible = (new Date(`${item.openDate} ${item.beginHour}`).getTime()>new Date().getTime())
          let r = that.data.workTypeList.find((obj)=>obj.code===item.workType) 
          // console.log(item.workType,"r")
          // console.log(r,"r")
          if (r) {         
            item.workTypeStr = r.name
          }
        })
        this.setData({
          timeList: timeList
        })
        // this.setData({
        //   disable: item.myAppointmentID?true:false
        // })
      })
    },
  bindEmployeeChange(e) {
    // console.log(e);
    this.setData({
      current: e.detail.value,
    })
  },
  before1day(time1,time2) {
    if(new Date(time1).getTime()>new Date(time2).getTime()) {
      this.setData({
        beforeFlag : false
      })
    } else {
      this.setData({
        beforeFlag : true
      })
    }

  },
  after1day(time1,time2) {
    if(new Date(time1).getTime()<new Date(time2).getTime()) {
      this.setData({
        afterFlag : false
      })
    } else {
      this.setData({
        afterFlag : true
      })
    }

  },
  add1day(){
    let timeStamp = new Date(this.data.selectedDate).getTime()+86400000
    this.setData({      
      selectedDate:utils.formatDate(timeStamp, 'yyyy-MM-dd')
    })
    this.before1day(this.data.selectedDate,this.data.openDate)
    this.after1day(this.data.selectedDate,this.data.closeDate)
    this.getTimeIntervalInstanceList()
  },
  minus1day(){
    let timeStamp = new Date(this.data.selectedDate).getTime()-86400000
    this.setData({      
      selectedDate:utils.formatDate(timeStamp, 'yyyy-MM-dd')
    })
    this.before1day(this.data.selectedDate,this.data.openDate)
    this.after1day(this.data.selectedDate,this.data.closeDate)
    this.getTimeIntervalInstanceList()
  },

  // 时间段选择  
  bindDateChange(e) {
    let that = this;
    console.log(e.detail.value)
    that.setData({
      selectedDate: e.detail.value,
    })
    that.before1day(that.data.selectedDate,that.data.openDate)
    that.after1day(that.data.selectedDate,that.data.closeDate)
    this.getTimeIntervalInstanceList()
  },
  // <<--到这里

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    console.log('触发');
    this.getLocation()
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },



  isWithinTimeRange(hour){
    // 创建一个新的 Date 对象表示当前时间
    const now = new Date();
    // 获取当前时间的小时和分钟
    const currentHour = now.getHours();
    const currentMinute = now.getMinutes();
    // 构建目标时间范围的开始和结束时间
    const targetStart = new Date(now);
    targetStart.setHours(hour - 1, 30, 0, 0); 
    const targetEnd = new Date(now);
    targetEnd.setHours(hour, 50, 0, 0); 
    // 比较当前时间是否在目标时间范围内
    return now >= targetStart && now <= targetEnd;
  },

   //点击事件创建设备连接
   createBLEConnection(id, name,e) {
    let deviceId = id
    wx.setStorageSync('blueDeviceID', id)
    wx.setStorageSync('blueDeviceName', name)
    //调用API连接设备
    wx.createBLEConnection({
      //连接的设备id
      deviceId,
      //连接成功
      success: (res) => {
        console.log('连接成功', res);
        //获得service
        wx.getBLEDeviceServices({
          deviceId, // 搜索到设备的 deviceId
          success: (res) => {
            let serviceIDs = []
            for (let i = 0; i < res.services.length; i++) {
              if (res.services[i].isPrimary) {
                serviceIDs.push(res.services[i].uuid)
                // 可根据具体业务需要，选择一个主服务进行通信
              }
              //保存serviceID们到本地
              wx.setStorageSync('blueServiceIDs', serviceIDs)
            }
          },
          fail: (res) => {
            console.log(res, 999);
          }
        })
        //读写特征值
        wx.getBLEDeviceCharacteristics({
          deviceId, // 搜索到设备的 deviceId
          serviceId: wx.getStorageSync('blueServiceIDs'), // 上一步中找到的某个服务
          success: (res) => {
            for (let i = 0; i < res.characteristics.length; i++) {
              let item = res.characteristics[i]
              if (item.properties.write) {
                let buffer = new ArrayBuffer(1)
                let dataView = new DataView(buffer)
                dataView.setUint8(0, 0)
                wx.writeBLECharacteristicValue({
                  deviceId,
                  serviceId,
                  characteristicId: item.uuid,
                  value: buffer,
                })
              }
              if (item.properties.read) { // 该特征值可读
                wx.readBLECharacteristicValue({
                  deviceId,
                  serviceId,
                  characteristicId: item.uuid,
                })
              }
              if (item.properties.notify || item.properties.indicate) {
                wx.notifyBLECharacteristicValueChange({
                  deviceId,
                  serviceId,
                  characteristicId: item.uuid,
                  state: true,
                })
              }
            }
          },
          fail: (res) => {
            console.log(res, 999);
          }
        })
        wx.stopBluetoothDevicesDiscovery({
          success: function (res) {
            console.log('停止查找蓝牙设备', res)
          }
        })
        this.door()
      },
      fail: (err) => {
        wx.hideLoading();
        wx.showToast({
          title: '连接失败，请重试',
          icon: 'none'
        })
        console.log(err, '连接失败');
      }
    })
  },
  door() {
    wx.hideLoading();
    this.action('door', 'unlock')
  },
   //获取蓝牙命令Json
   getCommand(sensorCode, commandValue) {
    return new Promise(resolve => {
      let appointment = this.data.selectItem
      //先获取实训仓的 所有传感器列表
      ports.ModuleMachine.getSensorList({
        machineID: appointment.objectID,
        pageNumber: 999
      }).then(res => {
        if (res.data.header.code == 0) {
          this.setData({
            sensorList:res?.data?.body?.data?.rows || []
          })
          let sensorID = ''
          //筛选出 传入名称 的传感器
          res.data.body.data.rows.forEach(row => {
            if (row.code == sensorCode)
              sensorID = row.sensorID
          });
          //获取该传感器的操作命令，先 写死 unlock 开门
          ports.ModuleMachine.getOneMachineCommandForSanOne({
            machineID: appointment.objectID,
            // sensorID,
            code:'door',
            commandValue,
          }).then(res2 => {
            if (res2.data.header.code == 0) {
              let c = "SE" + wx.getStorageSync('orderSeq') + "_computer"
              console.log("c",c);
              console.log("sensorList"+this.data.sensorList);
              let o=this.data.sensorList.find(sensor => sensor.code === c)
              console.log("company",o);
              //请先关门电脑自动打开
              this.sleep(5000).then(() => {
                this.querySensorStatus(sensorID,o)
              })
                resolve(res2.data.body.data)       
            } else {
              return resolve('fail')
            }
          })
        } else return resolve('fail')
      })
    })
  },
  querySensorStatus(sensorID,o,attempt = 1, maxAttempts = 99){
    console.log("查询门是否关闭");
    let appointmentList = wx.getStorageSync('appointmentList')
    //睡眠
    ports.ModuleMachine.getOneSensorDetail({
      sensorID,
    }).then(res9 => {
      console.log("openStatus"+res9.data.body.data.openStatus);
      if (res9.data.header.code == 0&&res9.data.body.data.openStatus==0) {
        ports.ModuleMachine.getOneMachineCommandForSanOne({
          machineID: appointmentList[0].objectID,
          // sensorID: o.sensorID,
          code:'door',
          commandValue: "on",
        }).then(res3 => {
          if (res3.data.header.code == 0) {
            console.log("开启电脑")
          } else {
            return resolve('fail')
          }
        })
      }else{
        if (attempt < maxAttempts) {
          wx.showToast({
            title: '请关闭门锁',
            icon: 'none',
            duration: 1000
          })
          setTimeout(() => {
            this.querySensorStatus(sensorID,o, attempt + 1, maxAttempts);
          }, 2000);
        } else {
          // 达到最大尝试次数，处理失败情况
          console.error('达到最大尝试次数，无法开启电脑');
        }
      }
    })
  },
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  },
  //按下按钮对应的操作
  action(sensor, act) {
    let that = this
    //调用写特征值（开门命令
    this.getCommand(sensor, act).then(jsonData => {
      if (jsonData == 'fail')
        return alert('获取通讯key失败')
      wx.closeBLEConnection({
        deviceId: wx.getStorageSync('blueDeviceID'), // 蓝牙设备ID
        success: (res) => {
          wx.removeStorageSync('blueDeviceID')
          console.log('断开蓝牙连接成功', res);
        },
        fail: (err) => {
          console.log('断开蓝牙连接失败', err);
        }
      });
      let params = {
        sessionID: wx.getStorageSync('USER_SESSIONID'),
        machineID: that.data.info.machineID,
        operateType: 13,
        operateName: '蓝牙连接',
        lastScanMac: that.data.info.macAddress,
      }
      ports.ModuleAll.operateOneMachine(params).then(res => {
        if (res.data.header.code === 0) {
          console.log("开始一次机器使用记录")
        }
      })
      wx.showToast({
        title: '请在听到门锁啪嗒一声后，再推门进入',
        icon: 'none',
        duration: 1000
      })
      let selectItem = this.data.selectItem
      if(this.data.enterStep==1){
        let reqURL = `getSectionDetail?sectionID=${selectItem.resourcesObjectID}&sessionID=${wx.getStorageSync('USER_SESSIONID')}`
        ports.ModuleAll.getSectionDetail(reqURL).then(respo => {
          let sectionDetail =respo.data.body.apiSectionDetailDto
          setTimeout(() => {
            wx.setStorageSync('enterFlag', true)
            this.setData({
              enterFlag: true
            })
            console.log(this.data.enterFlag, this.data.ornot)
            let machineID = that.data.selectItem.objectID
            let appointmentID = that.data.appointmentID;
            let trainBusinessID = that.data.info.trainBusinessID;
            let shopID = that.data.info.shopID;
            let mapAddress = that.data.info.address;
            let mapX = that.data.info.mapX;
            let mapY = that.data.info.mapY;
            let tabIndex = wx.getStorageSync('tabIndex');
            let orderSeq = wx.getStorageSync('orderSeq');
            let isVisit = false
            
            let url1 = `/packageA/pages/learnSteps/learnSteps?trainMinutes=${sectionDetail.trainMinutes}&sectionId=${sectionDetail.sectionID}&name=${sectionDetail.name}&shopID=${shopID}&appointmentID=${appointmentID}&mapAddress=${mapAddress}&mapX=${mapX}&mapY=${mapY}&machineID=${machineID}&orderSeq=${orderSeq}`
            let url2 = `/packageA/pages/trainSteps/trainSteps?sectionID=${sectionDetail.sectionID}&shopID=${shopID}&appointmentID=${appointmentID}&mapAddress=${mapAddress}&mapX=${mapX}&mapY=${mapY}&machineID=${machineID}&orderSeq=${orderSeq}`
            let url3 = `/packageA/pages/examSteps/examSteps?sectionID=${sectionDetail.sectionID}&shopID=${shopID}&appointmentID=${appointmentID}&mapAddress=${mapAddress}&mapX=${mapX}&mapY=${mapY}&machineID=${machineID}&orderSeq=${orderSeq}`
            let url = selectItem.workType==2?url1:selectItem.workType==3?url2:url3
            // wx.setStorageSync('urlForEnterFlag', url)
            // 更新于谷总提出新要求后，这里预约完了就不进入了，开个门就行250108
            // wx.navigateTo({
            //   url:url
            // })
          }, 1000)
          //调用写特征值（开门命令）
          this.writeBLECharacteristicValue(jsonData)
        })
      }else if(this.data.enterStep==2){
        let sectionDetail = this.data.sectionDetail
        setTimeout(() => {
          wx.setStorageSync('enterFlag', true)
          this.setData({
            enterFlag: true
          })
          console.log(this.data.enterFlag, this.data.ornot)
          let machineID = that.data.selectItem.objectID
          let appointmentID = that.data.appointmentID;
          let trainBusinessID = that.data.info.trainBusinessID;
          let shopID = that.data.info.shopID;
          let mapAddress = that.data.info.address;
          let mapX = that.data.info.mapX;
          let mapY = that.data.info.mapY;
          let tabIndex = wx.getStorageSync('tabIndex');
          let orderSeq = wx.getStorageSync('orderSeq');
          let isVisit = false
          
          let url1 = `/packageA/pages/learnSteps/learnSteps?trainMinutes=${sectionDetail.trainMinutes}&sectionId=${sectionDetail.sectionID}&name=${sectionDetail.name}&shopID=${shopID}&appointmentID=${appointmentID}&mapAddress=${mapAddress}&mapX=${mapX}&mapY=${mapY}&machineID=${machineID}&orderSeq=${orderSeq}`
          let url2 = `/packageA/pages/trainSteps/trainSteps?sectionID=${sectionDetail.sectionID}&shopID=${shopID}&appointmentID=${appointmentID}&mapAddress=${mapAddress}&mapX=${mapX}&mapY=${mapY}&machineID=${machineID}&orderSeq=${orderSeq}`
          let url3 = `/packageA/pages/examSteps/examSteps?sectionID=${sectionDetail.sectionID}&shopID=${shopID}&appointmentID=${appointmentID}&mapAddress=${mapAddress}&mapX=${mapX}&mapY=${mapY}&machineID=${machineID}&orderSeq=${orderSeq}`
          let url = this.data.learnType==1?url1:this.data.learnType==2?url2:url3
          wx.setStorageSync('urlForEnterFlag', url)
          wx.navigateTo({
            url:url
          })
        }, 1000)
        //调用写特征值（开门命令）
        this.writeBLECharacteristicValue(jsonData)
      }
    })
  },
  getSensor(machineID) {
    var that = this;
    ports.ModuleMachine.getSensorList({
      machineID: machineID,
      pageNumber: 99,
    }).then(res => {
      if (res.data.header.code == 0) {
        that.setData({
          sensorList: res.data.body.data.rows
        })

      } else return 
    })
  },

  radioChange_n(e){
    const {type,name} = e.currentTarget.dataset;
    // 预约内容的类型，目前实训学习合为一类，只有两类
    wx.setStorageSync('checkedWorkType', { workType: type, workTypeName:name })

    const checkLearnType = this.data.checkLearnType.map(item=>{
      return {
        ...item,
        checked: item.workType === type
      }
    })

    this.setData({
      checkLearnType,
      checkedLearnType:type,
      checkedLearnTypeName:name,
      friend1:{},
      friend2:{}
    })
  },
  // 切换toggleWorkType
  chooseWorkType(){
    this.setData({
      isWorkTypeOpen: true
    })
    // this.disablePageScroll()
  },
  hideWorkType(){
    this.setData({
      isWorkTypeOpen: false
    })
    // this.enablePageScroll()

  },
  // 修改实训仓
  toHref(){
    wx.navigateTo({
      url: '/packageA/pages/machineListNew/machineListNew',
    })
  },
  // 有弹窗的时候,都应该禁止滚动
  disablePageScroll() {
    wx.createSelectorQuery()
      .select('.scroll-page')
      .node()
      .exec((res) => {
        const page = res[0]?.node;
        if(page && page.style)page.style.overflow = 'hidden';
      });
  },
  enablePageScroll() {
    wx.createSelectorQuery()
      .select('.scroll-page')
      .node()
      .exec((res) => {
        const page = res[0]?.node;
        if(page && page.style)page.style.overflow = 'auto';;
      });
  },
  toHelp(){
    wx.switchTab({
      url: '/pages/help/help',
    })
  },
    getCityshowFn() {
      let that = this
      that.setData({
        getCityshow: false,
      })
    },
     // 点击选择城市
     selectedCity(e) {
      const data = e.currentTarget.dataset.item;
      let that = this
      that.setData({
        city: {
          name: data.name,
          cityID: data.cityID,
        }
      })
      console.log(that.data.city,'------------')
      const recentCities = wx.getStorageSync('recentCities') || [];
      const index = recentCities.findIndex(item => item.cityID === data.cityID);
      if (index === -1) {
        console.log('没有');
        recentCities.unshift(data);
        const newArray = recentCities.slice(0, 8);
        wx.setStorageSync('recentCities', newArray);
      } else {
        console.log('存在');
        const existingCity = recentCities[index];
        recentCities.splice(index, 1); // 删除原来的位置
        recentCities.unshift(existingCity); // 将数据放到数组的开头
        wx.setStorageSync('recentCities', recentCities);
      }
      that.setData({
        getCityshow: false
      });
    },
  
    // 打开城市列表抽屉
    Cityshow() {
      let that = this
      const recentCities = wx.getStorageSync('recentCities') || [];
      that.setData({
        getCityshow: true,
        recentCities: recentCities
      })
      if (that.data.getCityshow && that.data.cityList.length === 0) {
        // 这里先注释掉，先用写死的城市列表
        // this.getCity();
      }
    },
    // 获取城市列表
    getCity() {
      var arr = [];
      console.log(arr);
      let that = this;
      let data = {
        sessionID:wx.getStorageSync('USER_SESSIONID'),
        applicationID:app.applicationID,
        isValid:1,
        pageNumber:999
      }
      ports.ModuleAll.getApplicationCityList(data, that, res => {
        let rows = res.data.body.data.rows
        that.setData({
          cityList: rows,
        })
        // that
        console.log(res,'ressss')
      })
    },
    handleShare(e){
      console.log('handleShare',e)
      this.setData({
        isCustomShare:true,
        orderSeqForShare:e.currentTarget.dataset.item
      })
      return
      let appointmentDetail = this.data.shareAppointmentDetail
      let machineDetail = this.data.info
      let item = wx.getStorageSync('checkedTime')
      const USER_MEMBERID = wx.getStorageSync('USER_MEMBERID');
      const urlParams = {
        applyMemberID: USER_MEMBERID,
        machineID: machineDetail.machineID,
        objectID: machineDetail.machineID, // 如果需要，可以检查是否重复
        objectName: machineDetail.name,
        customerCompanyID: machineDetail.customerCompanyID,
        playCompanyID: machineDetail.playCompanyID,
        distributorCompanyID: machineDetail.distributorCompanyID,
        timeIntervalInstanceID: appointmentDetail.timeIntervalInstanceID,
        timeIntervalName: appointmentDetail.timeIntervalName,
        workType: appointmentDetail.workType,
        orderSeq: this.data.orderSeqForShare,
        resourcesObjectDefineID: '8a2f462a5c820afb015c8237567e1540',
        resourcesObjectID: appointmentDetail.resourcesObjectID,
        resourcesObjectName: appointmentDetail.resourcesObjectName,
        name: item.timeIntervalDefineName,
        companyID: app.companyID,
        objectDefineID: app.machineObjectDefineID,
        openDate: utils.formatDate(new Date().getTime(), 'yyyy-MM-dd')
      };
      let url = buildUrl('/pages/sharePageForInvite/sharePageForInvite', urlParams);
      console.log("url:",url);
      wx.navigateTo({
        url: url,
      })
    },
    inviteFriend2(){
      ports.ModuleAll.getFriendList({memberID:wx.getStorageSync('USER_MEMBERID'),isAgree:1}).then(res=>{
        this.setData({
          friendList:res.data.body.data?.rows || [],
          friendShowModal:true,
          checkType:1
        },()=>{
          console.log(this.data.friendList,'getFriendList1');
        })
      })
    },
    inviteFriend3(){
      ports.ModuleAll.getFriendList({memberID:wx.getStorageSync('USER_MEMBERID'),isAgree:1}).then(res=>{
        this.setData({
          friendList:res.data.body.data?.rows || [],
          friendShowModal:true,
          checkType:2
        },()=>{
          console.log(this.data.friendList,'getFriendList1');
        })
      })
    },
    checkFriend(e){
      let friend = e.currentTarget.dataset.item
      console.log('friend',friend);
      if(this.data.friend1.toMemberID==friend.toMemberID||this.data.friend2.toMemberID==friend.toMemberID){
        wx.showToast({
          title: '已选择此用户',
          icon:'none'
        })
        return
      }
      if(this.data.checkType==1){
        this.setData({
          friend1:friend,
          friend1MemberID:friend.toMemberID,
          friend1Name:friend.toMemberName,
          friendShowModal:false
        })
      }else if(this.data.checkType==2){
        this.setData({
          friend2:friend,
          friend2MemberID:friend.toMemberID,
          friend2Name:friend.toMemberName,
          friendShowModal:false
        })
      }
    },
    // 去开门
    enterDoor(){
      let thatNew = this
      if(this.data.distance > 100){
        wx.showModal({
          title: '提示',
          content: '您当前距离实训舱过远，无法开门,是否查看学习内容',
          complete: (res) => {
            if (res.cancel) {
              console.log('取消');
            }
            if (res.confirm) {
              console.log('确定');
              this.toNewIndex1()
            }
          }
        })
        return
      }
      let checkedTime = wx.getStorageSync('checkedTime')
      const appHour = checkedTime.name.match(/\d+/)
      const appDate = new Date(checkedTime.openDateStr)
      const currentDate = new Date()
      if(!(this.isWithinTimeRange(appHour)&&appDate.toDateString==currentDate.toDateString)){
        console.log("时间不对");
        wx.showModal({
          title: '提示',
          content: '当前未到进入时间，请检查选择的学习时段以及实训舱,是否查看学习内容',
          complete: (res) => {
            if (res.cancel) {
              console.log('取消');
            }
        
            if (res.confirm) {
              console.log('确定');
              this.toNewIndex1()
            }
          }
        })
        return
      }
      
      let pa = {
        machineID: this.data.info.machineID
      }
      console.log("machineID",this.data.info.machineID);
      wx.showLoading({
        title: '正在连接......',
        mask: true,
      })
      let info={};
      ports.ModuleAll.getMachineDetail(pa).then(async(resp) => {
        info=resp.data.body.data
        this.setData({
          info: info,
          trainBusinessID:info.trainBusinessID,
        })
        const resTwo = await ports.ModuleAll.getTrainBusinessDetail({
          trainBusinessID: info.trainBusinessID
        })
        this.setData({
         trainBusinessInfo: resTwo.data?.body?.trainBusiness || {}
       })
       console.log('trainBusinessID',info.trainBusinessID,'trainBusinessInfo.myMemberTrainID',resTwo.data?.body?.trainBusiness?.myMemberTrainID)

        let machineDetail = resp.data.body.data
        ports.ModuleAll.getOneAppointmentDetail({
          appointmentID: checkedTime.myAppointmentID
        }).then(respT =>{
          let appointment = respT.data.body.data
          console.log("appointment19",appointment)
          wx.setStorageSync('appointment', appointment)
          thatNew.setData({
            bluetoothDeviceName: this.data.info.name, //传入设备名
            appointmentID: appointment.appointmentID,
            tabIndex: appointment.workType === 3 ? 1 : 0,
            orderSeq: appointment.orderSeq,
            selectItem: appointment
          })
          ports.ModuleAll.isCanInOneMachineByAppointment({
            sessionID: wx.getStorageSync('USER_SESSIONID'),
            machineID: machineDetail.machineID
          }).then(respO => {
            let obj = respO.data.body.data.rows[0]
            let orderSeq = obj.appointmentNumber + 1;
            wx.setStorageSync('orderSeq', orderSeq)
            this.getSensor(checkedTime.objectID);
            this.manualOpenBluetoothAdapter();
          })
        })
      })
    },
      //手动搜索设备
  //第一步
  manualOpenBluetoothAdapter() {
    wx.setStorageSync('blueDeviceID', '')
    this.closeBluetoothAdapter()
    this.openBluetoothAdapter()
    // clearInterval(date)
  },
  //移除蓝牙
  closeBluetoothAdapter() {
    wx.closeBluetoothAdapter()
    this.setData({
      _discoveryStarted: false
    })
  },
    //开始扫描
  //第二步
  openBluetoothAdapter() {
    var that = this
    //初始化蓝牙模块所有接口只能初始化后才能调佣
    wx.openBluetoothAdapter({
      //蓝牙初始化成功
      success: (res) => {
        console.log('openBluetoothAdapter success', res)
        this.startBluetoothDevicesDiscovery() //开始搜寻附近的蓝牙外围设备
      },
      //蓝牙初始化失败
      fail: (res) => {
        //手机蓝牙未打开或不支持使用蓝牙返回10001
        if (res.errCode === 10001) {
          //监听用户的蓝牙是否打开（监听蓝牙的状态的改变）也可以调用蓝牙模块的所有API。开发者在开发中应该考虑兼容用户在使用小程序过程中打开/关闭蓝牙开关的情况，并给出必要的提示
          // wx.showLoading({
          //   title: '请打开蓝牙',
          // })
          wx.hideLoading()
          wx.showToast({
            title: '请打开蓝牙',
            icon: 'none',
          })
          wx.onBluetoothAdapterStateChange(function (res) {
            wx.hideLoading()
            if (res.available) {
              // wx.showToast({
              //   title: '搜索设备中...',
              //   icon: 'none'
              // }, 1000)
              that.startBluetoothDevicesDiscovery()
            }
          })
        } else {
          wx.hideLoading()
          wx.showToast({
            title: '连接失败，请重试',
            icon: 'none',
          })
          //测试使用，上线注释
          // this.toNewIndex1(true)
        }
      }
    })
  },
    //扫描并发现外围设备
  //第三步
  startBluetoothDevicesDiscovery() {
    var that = this
    if (this.data._discoveryStarted) return
    this.setData({
      _discoveryStarted: true
    })
    //调用API扫描发现外围设备
    wx.startBluetoothDevicesDiscovery({
      // services:["6E400001-B5A3-F393-E0A9-E50E24DCCA9E"],
      // allowDuplicatesKey: true,
      interval: 1000,
      success: (res) => {
        console.log('startBluetoothDevicesDiscovery success', res)
        // 设置定时器，30秒后停止查找
        setTimeout(function () {
          if (that.data.searchFlag) {
            wx.hideLoading();
            wx.showToast({
              title: '未搜索到设备',
              icon: 'none',
            })
            wx.stopBluetoothDevicesDiscovery({
              success: function (res) {
                console.log('停止查找蓝牙设备', res)
              }
            })
          }
        }, 10000)
        //监听蓝牙发现的设备
        this.onBluetoothDeviceFound()
      },
    })
  },
  //监听蓝牙搜索设备
  //第四步
  onBluetoothDeviceFound() {
    var deviceData = []
    var that = this
    //返回扫描到的设备
    wx.onBluetoothDeviceFound((res) => {
      console.log('返回扫描到的设备', res);
      res.devices.forEach(device => {
        if ((!device.name && !device.localName) || !device.connectable) {
          return
        }
        if (device.localName === this.data.info.iccid || device.name === this.data.info.iccid) {
          that.createBLEConnection(device.deviceId, device.name)
          that.data.searchFlag = false
          return
        }
      })
    })
  },
   //点击事件创建设备连接
   createBLEConnection(id, name,e) {
    let deviceId = id
    wx.setStorageSync('blueDeviceID', id)
    wx.setStorageSync('blueDeviceName', name)
    //调用API连接设备
    wx.createBLEConnection({
      //连接的设备id
      deviceId,
      //连接成功
      success: (res) => {
        console.log('连接成功', res);
        //获得service
        wx.getBLEDeviceServices({
          deviceId, // 搜索到设备的 deviceId
          success: (res) => {
            let serviceIDs = []
            for (let i = 0; i < res.services.length; i++) {
              if (res.services[i].isPrimary) {
                serviceIDs.push(res.services[i].uuid)
                // 可根据具体业务需要，选择一个主服务进行通信
              }
              //保存serviceID们到本地
              wx.setStorageSync('blueServiceIDs', serviceIDs)
            }
          },
          fail: (res) => {
            console.log(res, 999);
          }
        })
        //读写特征值
        wx.getBLEDeviceCharacteristics({
          deviceId, // 搜索到设备的 deviceId
          serviceId: wx.getStorageSync('blueServiceIDs'), // 上一步中找到的某个服务
          success: (res) => {
            for (let i = 0; i < res.characteristics.length; i++) {
              let item = res.characteristics[i]
              if (item.properties.write) {
                let buffer = new ArrayBuffer(1)
                let dataView = new DataView(buffer)
                dataView.setUint8(0, 0)
                wx.writeBLECharacteristicValue({
                  deviceId,
                  serviceId,
                  characteristicId: item.uuid,
                  value: buffer,
                })
              }
              if (item.properties.read) { // 该特征值可读
                wx.readBLECharacteristicValue({
                  deviceId,
                  serviceId,
                  characteristicId: item.uuid,
                })
              }
              if (item.properties.notify || item.properties.indicate) {
                wx.notifyBLECharacteristicValueChange({
                  deviceId,
                  serviceId,
                  characteristicId: item.uuid,
                  state: true,
                })
              }
            }
          },
          fail: (res) => {
            console.log(res, 999);
          }
        })
        wx.stopBluetoothDevicesDiscovery({
          success: function (res) {
            console.log('停止查找蓝牙设备', res)
          }
        })
        this.door()
      },
      fail: (err) => {
        wx.hideLoading();
        wx.showToast({
          title: '连接失败，请重试',
          icon: 'none'
        })
        console.log(err, '连接失败');
      }
    })
  },
  door() {
    wx.hideLoading();
    this.action('door', 'unlock')
  },
  //获取蓝牙命令Json
  getCommand(sensorCode, commandValue) {
    return new Promise(resolve => {
      let appointment = this.data.selectItem
      //先获取实训仓的 所有传感器列表
      ports.ModuleMachine.getSensorList({
        machineID: appointment.objectID,
        pageNumber: 999
      }).then(res => {
        if (res.data.header.code == 0) {
          this.setData({
            sensorList:res?.data?.body?.data?.rows || []
          })
          let sensorID = ''
          //筛选出 传入名称 的传感器
          res.data.body.data.rows.forEach(row => {
            if (row.code == sensorCode)
              sensorID = row.sensorID
          });
          //获取该传感器的操作命令，先 写死 unlock 开门
          ports.ModuleMachine.getOneMachineCommandForSanOne({
            machineID: appointment.objectID,
            // sensorID,
            code:'door',
            commandValue,
          }).then(res2 => {
            if (res2.data.header.code == 0) {
              let c = "SE" + wx.getStorageSync('orderSeq') + "_computer"
              console.log("c",c);
              console.log("sensorList"+this.data.sensorList);
              let o=this.data.sensorList.find(sensor => sensor.code === c)
              console.log("company",o);
              //请先关门电脑自动打开
              this.sleep(10000).then(() => {
                this.querySensorStatus(sensorID,o)
              })
                resolve(res2.data.body.data)       
            } else {
              return resolve('fail')
            }
          })
        } else return resolve('fail')
      })
    })
  },
  querySensorStatus(sensorID,o,attempt = 1, maxAttempts = 99){
    console.log("查询门是否关闭");
    let appointmentList = wx.getStorageSync('appointmentList')
    //睡眠
    ports.ModuleMachine.getOneSensorDetail({
      sensorID,
    }).then(res9 => {
      console.log("openStatus"+res9.data.body.data.openStatus);
      if (res9.data.header.code == 0&&res9.data.body.data.openStatus==0) {
        ports.ModuleMachine.getOneMachineCommandForSanOne({
          machineID: appointmentList[0].objectID,
          // sensorID: o.sensorID,
          code:'door',
          commandValue: "on",
        }).then(res3 => {
          if (res3.data.header.code == 0) {
            console.log("开启电脑")
          } else {
            return resolve('fail')
          }
        })
      }else if (attempt < maxAttempts) {
        wx.showToast({
          title: '请关闭门锁',
          icon: 'none',
          duration: 1000
        })
        setTimeout(() => {
          this.querySensorStatus(sensorID,o, attempt + 1, maxAttempts);
        }, 2000);
      }else {
        // 达到最大尝试次数，处理失败情况
        console.error('达到最大尝试次数，无法开启电脑');
      }
    })
  },
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  },
  //按下按钮对应的操作
  action(sensor, act) {
    let that = this
    //调用写特征值（开门命令
    this.getCommand(sensor, act).then(jsonData => {
      if (jsonData == 'fail')
        return alert('获取通讯key失败')
      wx.closeBLEConnection({
        deviceId: wx.getStorageSync('blueDeviceID'), // 蓝牙设备ID
        success: (res) => {
          wx.removeStorageSync('blueDeviceID')
          console.log('断开蓝牙连接成功', res);
        },
        fail: (err) => {
          console.log('断开蓝牙连接失败', err);
        }
      });
      let params = {
        sessionID: wx.getStorageSync('USER_SESSIONID'),
        machineID: that.data.info.machineID,
        operateType: 13,
        operateName: '蓝牙连接',
        lastScanMac: that.data.info.macAddress,
      }
      ports.ModuleAll.operateOneMachine(params).then(res => {
        if (res.data.header.code === 0) {
          console.log("开始一次机器使用记录")
        }
      })
      utils.gosign(this.data.appointmentID)
      wx.showToast({
        title: '请在听到门锁啪嗒一声后，再推门进入',
        icon: 'none',
        duration: 1000
      })
      // 这里直接进新页面，对于时间和地点已经判断过了，直接进就可以了
      this.toNewIndex1(true)
      return
      let selectItem = this.data.selectItem
      let reqURL = `getSectionDetail?sectionID=${selectItem.resourcesObjectID}&sessionID=${wx.getStorageSync('USER_SESSIONID')}`
      ports.ModuleAll.getSectionDetail(reqURL).then(respo => {
        let sectionDetail =respo.data.body.apiSectionDetailDto
        console.log("sectionDetail19",sectionDetail);
        console.log("sectionDetail1922",this.data.selectItem);
        console.log("sectionDetail1911",respo);
        setTimeout(() => {
          wx.setStorageSync('enterFlag', true)
          this.setData({
            enterFlag: true
          })
          console.log(this.data.enterFlag, this.data.ornot)
          let machineID = that.data.selectItem.objectID
          let appointmentID = that.data.appointmentID;
          let trainBusinessID = that.data.info.trainBusinessID;
          let shopID = that.data.info.shopID;
          let mapAddress = that.data.info.address;
          let mapX = that.data.info.mapX;
          let mapY = that.data.info.mapY;
          let tabIndex = wx.getStorageSync('tabIndex');
          let orderSeq = wx.getStorageSync('orderSeq');
          let isVisit = false
          
          let url1 = `/packageA/pages/learnSteps/learnSteps?trainMinutes=${sectionDetail.trainMinutes}&sectionId=${sectionDetail.sectionID}&name=${sectionDetail.name}&shopID=${shopID}&appointmentID=${appointmentID}&mapAddress=${mapAddress}&mapX=${mapX}&mapY=${mapY}&machineID=${machineID}&orderSeq=${orderSeq}`
          let url2 = `/packageA/pages/trainSteps/trainSteps?sectionID=${sectionDetail.sectionID}&shopID=${shopID}&appointmentID=${appointmentID}&mapAddress=${mapAddress}&mapX=${mapX}&mapY=${mapY}&machineID=${machineID}&orderSeq=${orderSeq}`
          let url3 = `/packageA/pages/examSteps/examSteps?sectionID=${sectionDetail.sectionID}&shopID=${shopID}&appointmentID=${appointmentID}&mapAddress=${mapAddress}&mapX=${mapX}&mapY=${mapY}&machineID=${machineID}&orderSeq=${orderSeq}`
          if(selectItem.workType==3){
            this.toNewIndex1()
          }else{
            let url = selectItem.workType==2?url1:selectItem.workType==3?url2:url3
            wx.setStorageSync('urlForEnterFlag', url)
            wx.navigateTo({
              url:url
            })
          }
        }, 1000)
        //调用写特征值（开门命令）
        this.writeBLECharacteristicValue(jsonData)
      })
    })
  },
   //写BEI特征值
   writeBLECharacteristicValue(jsonData) {
    console.log(jsonData, "jsondata")
    // 向蓝牙设备发送数据
    //let uuid = wx.getStorageSync('uuid');
    let that = this
    const serviceId = '0000FFF0-0000-1000-8000-00805F9B34FB' //写死 每个蓝牙都一样
    const characteristicId = "0000FFF2-0000-1000-8000-00805F9B34FB" //写死 每个蓝牙都一样
    let asciiArray = jsonToAscii(jsonData)
    let buffer = new ArrayBuffer(asciiArray.length);
    let dataView = new DataView(buffer)
    for (let i = 0; i < asciiArray.length; i++) {
      dataView.setUint8(i, asciiArray[i]);
    }
    wx.writeBLECharacteristicValue({
      deviceId: wx.getStorageSync('blueDeviceID'),
      serviceId,
      characteristicId,
      value: buffer,
      success(res) {
        console.log("消息发送成功")
      },
      fail(e) {
        console.log("发送消息失败: " + e.errMsg, );
      },
    })
    wx.onBLECharacteristicValueChange((characteristic) => {
      console.log(characteristic, "characteristic")
    })
  },
  getSensor(machineID) {
    var that = this;
    ports.ModuleMachine.getSensorList({
      machineID: machineID,
      pageNumber: 99,
    }).then(res => {
      if (res.data.header.code == 0) {
        that.setData({
          sensorList: res.data.body.data.rows
        })

      } else return 
    })
  },
    // TODO: 都预约成功
    toNewIndex1(openDoorSuccess){
    const data = {
      isVisit:false,
      // newOrderSeq:options.orderSeq,
      orderSeq:this.data.orderSeq,
      friend1MemberID:this.data.friend1MemberID || '0',
      friend1Name:this.data.friend1Name,
      friend2MemberID:this.data.friend2MemberID || '0',
      friend2Name:this.data.friend2Name,
      workType: this.data.checkLearnType.find(item=>item.checked).workType,
      openDoorSuccess:!!openDoorSuccess,
     }
      let url = buildUrl_n('/packageA/pages/newIndex1/newIndex1',data)
      wx.redirectTo({
        url: url,
      })
    },
    // 点击修改预约信息按钮
    handleEditClick(){
      this.setData({isEdit:true})
    },
    deleteFriend1(){
      this.setData({friend1:{}})
    },
    deleteFriend2(){
      this.setData({friend2:{}})
    },
    // 确认修改
    handleEditYes(){
      this.setData({isEdit:false})
    },
    // 取消修改
    handleEditNo(){
      this.setData({isEdit:false},()=>{
        this.showNoEditPage(noEditData)
      })
    },
    

});
function jsonToAscii(jsonObj) {
  // 将JSON对象转换为字符串
  const jsonString = JSON.stringify(jsonObj);
  // 将字符串转换为ASCII码数组
  const asciiArray = jsonString.split('').map(char => char.charCodeAt(0));
  // 返回ASCII码数组
  return asciiArray;
}


function decodeObjectValues(options) {
  const decodedParams = {};
  // 遍历 options 对象，对每个参数进行解码和解析
  for (const key in options) {
      if (options.hasOwnProperty(key)) {
          const encodedValue = options[key];
          const decodedValue = decodeURIComponent(encodedValue);
          try {
              // 尝试将解码后的值解析为对象
              decodedParams[key] = JSON.parse(decodedValue);
          } catch (error) {
              // 如果解析失败，说明该值是普通字符串，直接赋值
              decodedParams[key] = decodedValue;
          }
      }
  }
  return decodedParams;
}

function adjustFontSize(text) {
  const maxLength = 10; // 假设超过 20 个字开始缩小字体
  const baseFontSize = 32; // 基础字体大小
  const minFontSize = 18; // 最小字体大小
  let fontSize = baseFontSize;
  if (text.length > maxLength) {
    // 按比例缩小字体
    fontSize = Math.max(minFontSize, baseFontSize * (maxLength / text.length));
  }
  return fontSize
}

function buildUrl_n(base, params) {
  const encodedParams = Object.entries(params)
    .map(([key, value]) => `${encodeURIComponent(key)}=${ encodeURIComponent(typeof value === 'string' ? value : JSON.stringify(value)) }`)
    .join('&');
  return `${base}?${encodedParams}`;
}

