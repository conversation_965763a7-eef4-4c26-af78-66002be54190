// packageA/pages/machineDetail/machineDetail.js
var ports = require("../../../utils/ports")
var app = getApp()
var longitude = require('@/utils/longitude.js')
var utils = require('../../../utils/util')
Page({

  /**
   * 页面的初始数据
   */
  data: {
    backHome: false,
    quickEnterBtnFlag: false,
    onLearningFlag: false,
    content: "",
    showModal: false,
    appointmentID: '',
    timeIntervalInstanceID: '',
    id: '',
    flag: '',
    info: {},
    trainBusinessInfo: {},
    trainCompanyInfo: {},
    appointmentList: [],
    showList: [],
    deviceID: '',
    deviceName: '',
    serviceIDs: [],
    service: {
      serviceID: '',
      CharacteristicIDs: []
    },
    canWrite: false,
    serviceIDSel: '',
    charas: [],
    charValue: '',
    userInput: '',
    distance: 9999, //用经纬度计算距离
    bluetoothDeviceName: 'trainMachine',
    _discoveryStarted: false,
    statusList: [{
        num: 1,
        name: "新"
      },
      {
        num: 2,
        name: "申请"
      },
      {
        num: 3,
        name: "取消"
      },
      {
        num: 4,
        name: "预约中"
      },
      {
        num: 5,
        name: "撤销"
      },
      {
        num: 6,
        name: "签到"
      },
      {
        num: 10,
        name: "验票"
      },
      {
        num: 11,
        name: "离开"
      },
      {
        num: 20,
        name: "失效"
      },
    ],
    myX: 31.230416,
    myY: 121.473701,
    statusStr: '',
    timeFlag: true,
    connectStr: "进入",
    appointmentIndex: -1,
    ornot: 0, //今天是否签到
    btnShow: false,
    workTypeList: [{
        code: 0,
        name: "未选择"
      },
      {
        code: 1,
        name: "不限制"
      },
      {
        code: 2,
        name: "学习"
      },
      {
        code: 3,
        name: "实训"
      },
      {
        code: 10,
        name: "考试"
      },
    ],
    searchFlag: true,
    assistFlag: false,
    assistFlag: false,
    sensorList: [],
    //分页数据
    pageNumber: 0,
    pagetotal: 0,
    page: 1,
    myMemberID: "",
    myMemberTrainID: null,
    orderSeq: undefined,
    enterFlag: false,
    hasMore: false, // 是否还有更多数据
  },

  /**
   * 生命周期函数--监听页面加载
   */
  async onLoad(options) {


    if (wx.getStorageSync('loginTime')) {
      utils.isLoginTimeOut()
    }

    if (wx.getStorageSync('appointmentList')) {
      wx.removeStorageSync('appointmentList')
    }
    // if (!wx.getStorageSync('USER_SESSIONID')) {
    //   wx.showToast({
    //     icon:'none',
    //     title: '请先登录',
    //   })
    //   setTimeout(()=>{
    //     let url='/pages/memberSelf/memberSelf';
    //     wx.navigateTo({
    //       url: url,
    //     },1000)
    //   })

    // }
    this.setData({
      id: options.id,
      myMemberID: wx.getStorageSync('USER_MEMBERID'),
      fromInvite: options.fromInvite === 'true' // 记录是否来自邀请页面
    })

    let params = {
      applicationID: app.applicationID,
      trainBusinessID: options.trainid,
      sortTypeTime:1,
    }
    const res = await ports.ModuleAll.getMemberTrainList(params)
    if(res.data.body.data.rows){
      let governmentCompanyID = res.data.body.data.rows[0].governmentCompanyID
      if(governmentCompanyID){
        this.setData({
          governmentCompanyID: res.data.body.data.rows[0].governmentCompanyID,
          governmentCompanyName: res.data.body.data.rows[0].governmentCompanyName,
        })
      }else{
        this.setData({
          governmentCompanyID: '',
          governmentCompanyName: '',
          
        })
      }
    }

    //scene是扫码进来的
    if (options.scene) {
      this.setData({
        backHome: true,
        id: options.scene,
      })
      let params = {
        machineID: this.data.id,
        operateType: 6,
        operateName: "扫描二维码"
      }
      ports.ModuleAll.operateOneMachine(params).then(res => {
        if (res.data.header.code === 0) {
          console.log("开始一次机器使用记录")
        }
      })
    }
    if (!wx.getStorageSync('deviceID')) this.getDevice();
    // 这里先不需要注册
    if (false && !wx.getStorageSync('USER_MEMBER')) {
      wx.showLoading({
        title: '加载中',
      })
      wx.setStorageSync('previousUrl', `/packageA/pages/machineDetail/machineDetail?id=${options.id}`)
      await utils.LOGIN(0);
      this.setData({
        memberInfo: wx.getStorageSync('USER_MEMBER')
      })
      this.init()
      if (!wx.getStorageSync('USER_MEMBER')) {
        return
      }

    }
    if (wx.getStorageSync('enterFlag') && wx.getStorageSync('openTime')) {
      let nowTimeTimestamp = new Date().getTime();
      let openTime = wx.getStorageSync('openTime');
      if (nowTimeTimestamp > (openTime + 3600000)) {
        wx.removeStorageSync('enterFlag');
        wx.removeStorageSync('openTime');
        this.setData({
          enterFlag: false
        })
      }
    }

    console.log(wx.getStorageSync('previousUrl'))

    this.init();
  },
  async getDevice() {
    let res = await ports.ModuleAll.getSystemInfo();
    let ADCID = new Date().getTime().toString(36) + Math.random().toString(36).substr(2, 9);
    let params = {
      applicationID: app.applicationID,
      siteID: app.siteID,
      company: res.brand, //制造商名称
      name: res.model, //设备型号
      shortName: res.brand, //设备型号
      category: 3, //设备类型
      OSname: res.system, //操作系统
      model: res.model, //品牌型号
      // udid: openId, //一个字符串
      // imei: openId, //手机特有的1个串
      mac: ADCID, //设备的mac地址
    }
    let resDevice = await ports.ModuleAll.submitDeviceInfo(params)
    wx.setStorageSync('deviceID', resDevice.data.body.deviceID)
    this.setData({
      clearFlag: true
    })
  },
  async getMemberInfo() {
    if (!wx.getStorageSync('deviceID')) this.getDevice();
    // 这里先不需要注册
    if (false && !wx.getStorageSync('USER_MEMBER')) {
      wx.showLoading({
        title: '加载中',
      })
      wx.setStorageSync('previousUrl', `/packageA/pages/machineDetail/machineDetail`)
      await utils.LOGIN(0);
      this.setData({
        memberInfo: wx.getStorageSync('USER_MEMBER')
      })
      if (!wx.getStorageSync('USER_MEMBER')) {
        return
      }
    }
  },
  loadMore: function () {
    if (!this.data.hasMore) return; // 如果没有更多数据，则不再请求
    wx.showLoading({
      title: '加载中',
    })
    this.data.page++;
    this.loadData().then((newData) => {
      if (newData.length === 0) {
        this.setData({
          hasMore: false
        });
      } else {
        this.setData({
          showList: this.data.showList.concat(newData),
        });
      }
    });
  },

  // 示例：模拟异步加载更多数据
  loadData: function () {
    function getStringAfterFirstOccurrence(str, char) {
      var parts = str.split(char);
      if (parts.length > 1) {
        return parts.slice(1).join(char); // 返回第一个指定字符后的内容
      }
      return ''; // 如果指定字符不存在，返回空字符串
    }
    // 使用正则表达式
    function cutStringBeforeCharWithRegex(str, charToFind) {
      var regex = new RegExp("(.*)" + charToFind);
      if (regex.test(str)) {
        return RegExp.$1.trim();
      }
      return '';
    }
    let nowTime = new Date()
    var that = this
    return new Promise((resolve) => {
      setTimeout(() => {
        ports.ModuleAll.getAppointmentList({
          applicationID: app.applicationID,
          sessionID: wx.getStorageSync('USER_SESSIONID'),
          memberID: that.data.assistFlag ? null : wx.getStorageSync('USER_MEMBERID'),
          isBlank: 1,
          objectID: this.data.id,
          sortTypeOpen: 1,
          sortTypeName: 1,
          pageNumber: 10,
          currentPage: this.data.page,
          tabIndex: 0,
          // currentPage: this.data.pageNumber,
          beginTime: utils.formatDate(nowTime, 'yyyy-MM-dd'),
          // beginTime: f,
          // endTime: l,
          // sortTypeOpen: 2,
          //sortTypeName: 2
          // keyWords: info.name,
        }).then(resFour => {
          wx.hideLoading()
          let arr = resFour.data.body.data.rows;
          let newArr = []
          that.setData({
            appointmentList: arr,
            pageNumber: 0,
            pagetotal: 0,
            page: resFour.data.body.data.currentPage,
            hasMore: resFour.data.body.data.totalPage > 1 ? true : false
          })
          for (let i = 0; i < that.data.appointmentList.length; i++) {
            let tempStr = "appointmentList[" + i + "].dateStr";
            that.setData({
              [tempStr]: getStringAfterFirstOccurrence(that.data.appointmentList[i].openDateStr, "-")
            })
            let c = that.data.workTypeList.find((obj) => obj.code === that.data.appointmentList[i].workType)
            if (c) {
              let str1 = "appointmentList[" + i + "].workTypeStr";
              that.setData({
                [str1]: c.name
              })
            }
            let r = that.data.statusList.find((obj) => obj.num === that.data.appointmentList[i].status)
            if (r) {
              let str2 = "appointmentList[" + i + "].statusStr";
              that.setData({
                [str2]: r.name
              })
            }
            let openStr = that.data.appointmentList[i].openDateStr + " " + cutStringBeforeCharWithRegex(that.data.appointmentList[i].timeIntervalName, "点") + ":00:00";
            let nowTimeTimestamp = new Date().getTime();
            let openTime = new Date(openStr).getTime();
            wx.setStorageSync('openTime', openTime)
            let beginTime = new Date(openStr).getTime() - 1800000;
            let endTime = new Date(openStr).getTime() + 1800000;
            let key = "appointmentList[" + i + "].timeFlag";

            if (nowTimeTimestamp >= beginTime && nowTimeTimestamp <= endTime) {
              that.setData({
                [key]: true
              })

            } else {
              that.setData({
                [key]: false
              })
            }
            if ((nowTimeTimestamp - beginTime) < 3600000) {
              newArr.push(that.data.appointmentList[i])
            }

          }
          that.setData({
            appointmentList: newArr,
          })
          // 真实场景中这里应调用API获取下一页数据
          const newData = newArr;
          resolve(newData);

        })

      }, 1000);
    });
  },
  clickArticle() {
    let url = '/packageA/pages/agreement/agreement?articleID=' + app.articleID;
    wx.navigateTo({
      url: url,
    })
  },
  getFirstAndLastDayOfMonth(date) {
    let firstDay = new Date(date);
    let lastDay = new Date(date.getFullYear() + 1, date.getMonth(), 0);
    // let f = utils.formatDate(firstDay, 'yyyy-MM');
    // let l = utils.formatDate(lastDay, 'yyyy-MM');
    return {
      firstDay: firstDay,
      lastDay: lastDay
    };
  },
  async init() {
    if (!wx.getStorageSync('USER_SESSIONID')) {
      utils.LOGIN(1)
      if(!wx.getStorageSync('hasShowModal')){
        this.fetchContent();
      }
    }

    function getStringAfterFirstOccurrence(str, char) {
      var parts = str.split(char);
      if (parts.length > 1) {
        return parts.slice(1).join(char); // 返回第一个指定字符后的内容
      }
      return ''; // 如果指定字符不存在，返回空字符串
    }

    var that = this
    let p = {
      machineID: this.data.id,
    }
    let nowTime = new Date()
    let {
      firstDay,
      lastDay
    } = this.getFirstAndLastDayOfMonth(nowTime);
    let f = utils.formatDate(firstDay, 'yyyy-MM-dd');
    let l = utils.formatDate(lastDay, 'yyyy-MM-dd');
    ports.ModuleAll.getMachineDetail(p).then(resOne => {
      console.log(resOne);
      let info = resOne.data.body.data
      this.setData({
        info: resOne.data.body.data
      })
      if (wx.getStorageSync('USER_SESSIONID')) {
        this.getDistance();
      }

      // 使用正则表达式
      function cutStringBeforeCharWithRegex(str, charToFind) {
        var regex = new RegExp("(.*)" + charToFind);
        if (regex.test(str)) {
          return RegExp.$1;
        }
        return str;
      }

      ports.ModuleAll.getMyMemberMajorList({
        sessionID: wx.getStorageSync('USER_SESSIONID')
      }).then(res => {
        if (res.data.header.code === 0) {
          let list = res.data.body.data.rows;
          wx.setStorageSync('majorAssist', 0)
          that.setData({
            assistFlag: false,
          })
          for (let i = 0; i < list.length; i++) {
            if (app.assistMajorID === list[i].majorID) {
              that.setData({
                assistFlag: true
              })
              wx.setStorageSync('majorAssist', 0)
              break;
            }
          }
          ports.ModuleAll.getAppointmentList({
            applicationID: app.applicationID,
            sessionID: wx.getStorageSync('USER_SESSIONID'),
            memberID: that.data.assistFlag ? null : wx.getStorageSync('USER_MEMBERID'),
            isBlank: 1,
            objectID: this.data.id,
            sortTypeOpen: 1,
            sortTypeName: 1,
            pageNumber: 10,
            currentPage: 1,
            tabIndex: 0,
            // currentPage: this.data.pageNumber,
            beginTime: utils.formatDate(nowTime, 'yyyy-MM-dd'),
            // beginTime: f,
            // endTime: l,
            // sortTypeOpen: 2,
            //sortTypeName: 2
            // keyWords: info.name,
          }).then(resFour => {
            console.log("resFour", resFour);
            //初始化页码数据
            that.setData({
              pageNumber: 0,
              pagetotal: 0,
              page: 1,
              hasMore: resFour.data.body.data.totalPage > 1 ? true : false
            })
            //wx.hideLoading()
            wx.stopPullDownRefresh()
            let arr = resFour.data.body.data.rows;
            let totalPage = resFour.data.body.data.totalPage;
            let newArr = []
            that.setData({
              appointmentList: arr,
            })

            console.log("appointmentList" + that.data.appointmentList);
            for (let i = 0; i < that.data.appointmentList.length; i++) {
              let tempStr = "appointmentList[" + i + "].dateStr";
              that.setData({
                [tempStr]: getStringAfterFirstOccurrence(that.data.appointmentList[i].openDateStr, "-")
              })
              let c = that.data.workTypeList.find((obj) => obj.code === that.data.appointmentList[i].workType)
              if (c) {
                let str1 = "appointmentList[" + i + "].workTypeStr";
                that.setData({
                  [str1]: c.name
                })
              }
              let r = that.data.statusList.find((obj) => obj.num === that.data.appointmentList[i].status)
              if (r) {
                let str2 = "appointmentList[" + i + "].statusStr";
                that.setData({
                  [str2]: r.name
                })
              }
              let openStr = that.data.appointmentList[i].openDateStr + " " + cutStringBeforeCharWithRegex(that.data.appointmentList[i].timeIntervalName, "点") + ":00:00";
              let nowTimeTimestamp = new Date().getTime();
              let openTime = new Date(openStr).getTime();
              wx.setStorageSync('openTime', openTime)
              let beginTime = new Date(openStr).getTime() - 1800000;
              let endTime = new Date(openStr).getTime() + 1800000;
              let key = "appointmentList[" + i + "].timeFlag";

              if (nowTimeTimestamp >= beginTime && nowTimeTimestamp <= endTime) {
                that.setData({
                  [key]: true
                })

              } else {
                that.setData({
                  [key]: false
                })
              }
              if ((nowTimeTimestamp - beginTime) < 3600000) {
                if (that.data.appointmentList[i].status === 1) {
                  newArr.push(that.data.appointmentList[i])
                }
              }
              //保留两小时内状态为离开或者签到的预约
              let beginTime1 = new Date(openStr).getTime();
              const timeDifference = nowTimeTimestamp - beginTime1;
              // 检查时间差是否在过去2小时内，并且状态为离开(11)或签到(6)
              if (timeDifference <= 7200000 && (that.data.appointmentList[i].status === 6 || that.data.appointmentList[i].status === 11)) {

                // 如果条件满足，将该预约添加到新数组
                newArr.push(that.data.appointmentList[i]);
              }

            }
            that.setData({
              appointmentList: newArr,
            })

            let thisPages = that.data.pageNumber;
            let rows = 10;
            let prizeListItem = that.data.appointmentList;
            let pageData = that.data.showList;
            let pages = that.data.totalPage;
            pageData = prizeListItem.filter(function (item, index, prizeListItem) {
              //元素值，元素的索引，原数组。
              return index >= rows * thisPages && index <= rows * (thisPages + 1) - 1; //初始为0，0 < index < 6-1
            });
            thisPages = thisPages + 1;

            let x = 0;
            let y = prizeListItem.length;
            if (y % 10 !== 0) {
              x = 1
            };
            pages = parseInt(y / 10) + x;
            that.setData({
              pageNumber: thisPages,
              showList: pageData,
              pagetotal: pages,
            })
            wx.setStorageSync('appointmentList', that.data.appointmentList)
          })
        }
      })


      // ports.ModuleAll.getTimeIntervalInstanceList({
      //   applicationID: app.applicationID,
      //   sessionID: wx.getStorageSync('USER_SESSIONID'),
      //   memberID: wx.getStorageSync('USER_MEMBERID'),
      //   isBlank: 1
      // }).then(resFour=>{
      //   console.log(resFour.data.body.data.rows)
      //   this.setData({
      //     appointmentList: resFour.data.body.data.rows
      //   })
      // })
      //获得课程
      // if (!info.trainBusinessID) return    
      ports.ModuleAll.getTrainBusinessDetail({
        trainBusinessID: info.trainBusinessID
      }).then(resTwo => {
        this.setData({
          trainBusinessInfo: resTwo.data.body.trainBusiness,
          flag: resTwo.data.body.trainBusiness.myMemberTrainStatus,
          myMemberTrainID: resTwo.data.body.trainBusiness.myMemberTrainID,
          flag: resTwo.data.body.trainBusiness.myMemberTrainStatus
        })
      })

      ports.ModuleAll.getTrainCompanyList({
        trainBusinessID: info.trainBusinessID,
        status:2
      }).then(resThree => {
        this.setData({
          trainCompanyInfo: resThree.data.body.data.rows || {}
        })
        console.log(this.data.trainCompanyInfo);
      })
    })

  },
  quickEnter() {
    this.setData({
      quickEnterBtnFlag: true
    })
    this.isCanInOneMachineByAppointment()
  },
  isCanInOneMachineByAppointment() {
    let that = this
    ports.ModuleAll.isCanInOneMachineByAppointment({
      sessionID: wx.getStorageSync('USER_SESSIONID'),
      machineID: this.data.id
    }).then(res => {
      if (res.data.header.code === 0) {
        let obj = res.data.body.data.rows[0];

        //比对appointmentList是否存在上个时间的预约
        let appointmentList = wx.getStorageSync('appointmentList')
        const matchedAppointment = appointmentList.find(function (appointment) {
          return appointment.timeIntervalInstanceID === obj.timeIntervalInstanceID;
        });
        if (matchedAppointment) {
          // 如果找到了匹配的 timeIntervalInstanceID，执行逻辑
          let appointmentID = matchedAppointment.appointmentID;
          this.setData({
            appointmentID: appointmentID
          })
          ports.ModuleAll.getOneAppointmentDetail({
            appointmentID: appointmentID
          }).then(resp => {
            let appointment = resp.data.body.data
            wx.setStorageSync('appointment', appointment)
          })
          let that = this;
          wx.showModal({
            title: '提示',
            content: '请等待五秒后拉门进入！',
            success(res) {
              if (res.confirm) {
                //开启电脑需要的参数
                let orderSeq = obj.appointmentNumber;
                wx.setStorageSync('orderSeq', orderSeq)
                
                that.getCommand('door', 'unlock').then(jsonData => {
                  if (jsonData == 'fail')
                    return alert('获取通讯key失败')
                  // if (!wx.getStorageSync('blueDeviceID'))
                  //   return alert('请连接蓝牙设备') 
                  let params = {
                    sessionID: wx.getStorageSync('USER_SESSIONID'),
                    machineID: that.data.id,
                    // operateType: 13,
                    // operateName: '蓝牙连接',
                    lastScanMac: that.data.info.macAddress,
                  }
                  ports.ModuleAll.operateOneMachine(params).then(res => {
                    if (res.data.header.code === 0) {
                      console.log("开始一次机器使用记录")
                    }
                  })
                  wx.showToast({
                    title: '请在听到门锁啪嗒一声后，再推门进入',
                    icon: 'none',
                    duration: 1000
                  })
                  that.init()
                  setTimeout(() => {
                    wx.setStorageSync('enterFlag', true)
                    that.setData({
                      enterFlag: true
                    })
                    let machineID = that.data.id
                    let trainBusinessID = that.data.info.trainBusinessID
                    let shopID = that.data.info.shopID;
                    let mapAddress = that.data.info.address;
                    let mapX = that.data.info.mapX;
                    let mapY = that.data.info.mapY;
                    let tabIndex = obj.workType === 3 ? 1 : 0;
                    let orderSeq = obj.appointmentNumber+1;
                    wx.setStorageSync('orderSeq', orderSeq)
                    wx.setStorageSync('tabIndex', tabIndex)
                    wx.navigateTo({
                      url: `/packageA/pages/learningOperation/learningOperation?appointmentID=${appointmentID}&machineID=${machineID}&shopID=${shopID}&mapAddress=${mapAddress}&mapX=${mapX}&mapY=${mapY}&trainBusinessID=${trainBusinessID}&tabIndex=${tabIndex}&seq=${orderSeq}`,
                    })
                  }, 1000)
                  //调用写特征值（开门命令
                  that.writeBLECharacteristicValue(jsonData)

                })
                return
                // 执行相关操作
              } else if (res.cancel) {
                return
              }

            }

          });
          return
        }
        if (obj.appointmentNumber < 3 && (this.data.showList.length === 0 || this.data.showList[0].timeIntervalInstanceID !== obj.timeIntervalInstanceID)) {
          let params = {
            sessionID: wx.getStorageSync('USER_SESSIONID'),
            applyMemberID: wx.getStorageSync('USER_MEMBERID'),
            companyID: app.companyID,
            name: obj.timeIntervalDefineName,
            objectDefineID: app.machineObjectDefineID,
            objectID: that.data.info.machineID,
            objectName: that.data.info.name,
            customerCompanyID: that.data.info.customerCompanyID,
            playCompanyID: that.data.info.playCompanyID,
            distributorCompanyID: that.data.info.distributorCompanyID,
            timeIntervalInstanceID: obj.timeIntervalInstanceID,
            timeIntervalName: obj.timeIntervalDefineName,
            openDate: utils.formatDate(new Date().getTime(), 'yyyy-MM-dd'),
            workType: obj.workType,

          }

          wx.showModal({
            title: '提示',
            content: '请等待五秒后拉门进入！',
            success(res) {
              if (res.confirm) {
                ports.ModuleAll.createOneAppointment(params).then(res => {
                  if (res.data.header.code == 0) {
                    let appointmentID = res.data.body.appointmentID
                    that.setData({
                      appointmentID: appointmentID
                    })
                    ports.ModuleAll.getOneAppointmentDetail({
                      appointmentID: appointmentID
                    }).then(resp => {
                      let appointment = resp.data.body.data
                      wx.setStorageSync('appointment', appointment)
                    })
                    //开启电脑需要的参数
                    let orderSeq = obj.appointmentNumber + 1;
                    wx.setStorageSync('orderSeq', orderSeq)
                    that.getCommand('door', 'unlock').then(jsonData => {
                      if (jsonData == 'fail')
                        return alert('获取通讯key失败')
                      // if (!wx.getStorageSync('blueDeviceID'))
                      //   return alert('请连接蓝牙设备') 
                      let params = {
                        sessionID: wx.getStorageSync('USER_SESSIONID'),
                        machineID: that.data.id,
                        // operateType: 13,
                        // operateName: '蓝牙连接',
                        lastScanMac: that.data.info.macAddress,
                      }
                      ports.ModuleAll.operateOneMachine(params).then(res => {
                        if (res.data.header.code === 0) {
                          console.log("开始一次机器使用记录")
                        }
                      })
                      wx.showToast({
                        title: '请在听到门锁啪嗒一声后，再推门进入',
                        icon: 'none',
                        duration: 1000
                      })
                      that.init()
                      setTimeout(() => {
                        wx.setStorageSync('enterFlag', true)
                        that.setData({
                          enterFlag: true
                        })
                        let machineID = that.data.id
                        let trainBusinessID = that.data.info.trainBusinessID
                        let shopID = that.data.info.shopID;
                        let mapAddress = that.data.info.address;
                        let mapX = that.data.info.mapX;
                        let mapY = that.data.info.mapY;
                        let tabIndex = obj.workType === 3 ? 1 : 0;
                        let orderSeq = obj.appointmentNumber + 1;
                        wx.setStorageSync('orderSeq', orderSeq)
                        wx.setStorageSync('tabIndex', tabIndex)
                        wx.navigateTo({
                          url: `/packageA/pages/learningOperation/learningOperation?appointmentID=${appointmentID}&machineID=${machineID}&shopID=${shopID}&mapAddress=${mapAddress}&mapX=${mapX}&mapY=${mapY}&trainBusinessID=${trainBusinessID}&tabIndex=${tabIndex}&seq=${orderSeq}`,
                        })
                      }, 1000)
                      //调用写特征值（开门命令
                      that.writeBLECharacteristicValue(jsonData)
                    })
                  } else {
                    wx.showToast({
                      icon: 'none',
                      title: '预约失败',
                    })
                  }
                })

              } else if (res.cancel) {
                return
              }
            }
          });


        } else {
          wx.showToast({
            icon: 'none',
            title: '当前不能进入',
          })
        }
        console.log(res.data.body.data.rows[0], "进入")
      } else {
        wx.showToast({
          icon: 'none',
          title: '当前不能进入',
        })
      }
    }).finally(() => {
      this.setData({
        quickEnterBtnFlag: false
      })
    })
  },
  //input输入双向绑定数据
  inputValue: function (e) {
    let name = e.currentTarget.dataset.name;
    let mapName = {};
    mapName[name] = e.detail && e.detail.value;
    // console.log(mapName);
    this.setData(mapName);
  },
  //上一页
  prevFn: function () {
    if (this.data.pageNumber <= 0) {
      wx.showToast({
        icon: 'none',
        title: '已经是最前一页',
      })
      return;
    }

    wx.showLoading({
      title: '加载中...',
    })
    let that = this;
    let thisPages = that.data.pageNumber;
    let prizeListItem = that.data.appointmentList;
    let pageData = that.data.showList;
    let rows = 10;
    pageData = prizeListItem.filter(function (item, index, prizeListItem) {
      //元素值，元素的索引，原数组。
      return index >= rows * (thisPages - 2) && index <= rows * (thisPages - 1) - 1;
    });
    thisPages = thisPages - 1;
    that.setData({
      pageNumber: thisPages,
      showList: pageData,
    })
    // this.setData({
    //   pageNumber:Number(this.data.pageNumber)-1
    // })
    this.init()
    setTimeout(function () {
      wx.hideLoading()
    }, 250)
  },
  //下一页
  nextFn: function () {
    if (this.data.pageNumber === this.data.pagetotal) {
      wx.showToast({
        icon: 'none',
        title: '已经是最后一页',
      })
      return;
    }
    wx.showLoading({
      title: '加载中...',
    })
    let that = this;
    let thisPages = that.data.pageNumber;
    let prizeListItem = that.data.appointmentList;
    let pageData = that.data.showList;
    let rows = 10;
    pageData = prizeListItem.filter(function (item, index, prizeListItem) {
      //元素值，元素的索引，原数组。
      return index >= rows * thisPages && index <= rows * (thisPages + 1) - 1;
    });
    thisPages = thisPages + 1;
    that.setData({
      pageNumber: thisPages,
      showList: pageData,
    })
    // this.setData({
    //   pageNumber:Number(this.data.pageNumber)+1
    // })
    // this.init()
    setTimeout(function () {
      wx.hideLoading()
    }, 250)
  },
  //去到某一页
  pageGo: function () {
    console.log(Number(this.data.pageNumber));
    if (Number(this.data.pageNumber) > this.data.pagetotal) {
      this.setData({
        pageNumber: this.data.pagetotal
      })
    } else if (Number(this.data.pageNumber) <= 0) {
      this.setData({
        pageNumber: 1
      })
    }
    wx.showLoading({
      title: '',
    })
    this.init()
    // setTimeout(function(){
    //   wx.hideLoading()
    // },1000)
  },
  //打卡签到
  gosign: function () {
    let that = this

    let params = {
      sessionID: wx.getStorageSync('USER_SESSIONID'),
      appointmentID: that.data.appointmentID,
      shopID: that.data.info.shopID ? that.data.info.shopID : "",
      mapAddress: that.data.info.address,
      mapX: that.data.info.mapX,
      mapY: that.data.info.mapY,
    }
    ports.ModuleAll.signinOneAppointment(params).then(res => {
      if (res.data.header.code == 0) {
        console.log(res, "开始学习")
        that.setData({
          ornot: 1
        })
      } else {
        console.log(res.data.header.msg)
      }
    })
  },
  goout: function () {
    let that = this

    let params = {
      sessionID: wx.getStorageSync('USER_SESSIONID'),
      appointmentID: that.data.appointmentID,
    }
    wx.showLoading()
    ports.ModuleAll.leftOneAppointment(params).then(res1 => {
      wx.hideLoading()
      if (res.data.header.code == 0) {
        console.log(res1, "结束学习")
        that.setData({
          ornot: -1
        })
        let orderSeq = wx.getStorageSync('orderSeq')
        let c = "SE" + orderSeq + "_computer"
        let o = this.data.sensorList.find((sensor) => c === sensor.code)
        ports.ModuleMachine.getOneMachineCommandForSanOne({
          machineID: this.data.id,
          // sensorID: o.sensorID,
          code:'door',
          commandValue: "off",
        }).then(res2 => {
          if (res2.data.header.code == 0) {
            console.log(res2, "关闭电脑")
          } else {
            console.log(res2.data.header.msg)
          }
        })
      } else {
        console.log(res1.data.header.msg)
      }
    })

  },
  clickAll() {
    let url = `/packageA/pages/myAppointment/myAppointment`;
    wx.navigateTo({
      url: url,
    })
  },
  toBluetooth(e) {
    let that = this;
    if (!wx.getStorageSync('USER_SESSIONID')) {
      return wx.showToast({
        title: '请先登录',
        icon: 'none'
      })
    }
    // setTimeout(()=>{
    //   let appointmentID = that.data.appointmentID;
    //   let trainBusinessID = that.data.info.trainBusinessID
    //   let shopID = that.data.info.shopID;
    //   let mapAddress = that.data.info.address;
    //   let mapX = that.data.info.mapX;
    //   let mapY = that.data.info.mapY;
    //   wx.navigateTo({
    //     url: `/packageA/pages/learningOperation/learningOperation?appointmentID=${appointmentID}&shopID=${shopID}&mapAddress=${mapAddress}&mapX=${mapX}&mapY=${mapY}&trainBusinessID=${trainBusinessID}`,
    //   })
    // },5000)

    wx.showLoading({
      title: '正在连接......',
      mask: true,
    })
    // this.setData({
    //   connectStr: '正在连接......'
    // })
    // wx.navigateTo({
    //   url: `/packageA/pages/bluetooth/bluetooth?machineName=${this.data.info.name}`,
    // })
    this.setData({
      bluetoothDeviceName: this.data.info.name, //传入设备名
      appointmentIndex: e.currentTarget.dataset.index,
      appointmentID: e.currentTarget.dataset.id,
      tabIndex: e.currentTarget.dataset.type === 3 ? 1 : 0,
      orderSeq: e.currentTarget.dataset.seq,
    })
    ports.ModuleAll.getOneAppointmentDetail({
      appointmentID: e.currentTarget.dataset.id
    }).then(resp => {
      let appointment = resp.data.body.data
      wx.setStorageSync('appointment', appointment)
      let orderSeq = appointment.appointmentNumber + 1;
      wx.setStorageSync('orderSeq', orderSeq)
    })
    this.getSensor();
    this.manualOpenBluetoothAdapter();
    // this.gosign(e);
  },
  deleteOneAppointment(e) {
    let appointmentID = e.currentTarget.dataset.id;
    let params = {
      sessionID: wx.getStorageSync('USER_SESSIONID'),
      appointmentID: appointmentID,
    }
    ports.ModuleAll.deleteOneAppointment(params).then(res => {
      wx.showToast({
        title: '删除成功',
        icon: 'none',
      })
      this.onPullDownRefresh()
    })
  },
  //手动搜索设备
  //第一步
  manualOpenBluetoothAdapter() {
    wx.setStorageSync('blueDeviceID', '')
    this.closeBluetoothAdapter()
    this.openBluetoothAdapter()
    // clearInterval(date)
  },
  //移除蓝牙
  closeBluetoothAdapter() {
    wx.closeBluetoothAdapter()
    this.setData({
      _discoveryStarted: false
    })
  },
  //开始扫描
  //第二步
  openBluetoothAdapter() {
    var that = this
    //初始化蓝牙模块所有接口只能初始化后才能调佣
    wx.openBluetoothAdapter({
      //蓝牙初始化成功
      success: (res) => {
        console.log('openBluetoothAdapter success', res)
        this.startBluetoothDevicesDiscovery() //开始搜寻附近的蓝牙外围设备
      },
      //蓝牙初始化失败
      fail: (res) => {
        //手机蓝牙未打开或不支持使用蓝牙返回10001
        if (res.errCode === 10001) {
          //监听用户的蓝牙是否打开（监听蓝牙的状态的改变）也可以调用蓝牙模块的所有API。开发者在开发中应该考虑兼容用户在使用小程序过程中打开/关闭蓝牙开关的情况，并给出必要的提示
          // wx.showLoading({
          //   title: '请打开蓝牙',
          // })
          wx.hideLoading()
          wx.showToast({
            title: '请打开蓝牙',
            icon: 'none',
          })
          wx.onBluetoothAdapterStateChange(function (res) {
            wx.hideLoading()
            if (res.available) {
              // wx.showToast({
              //   title: '搜索设备中...',
              //   icon: 'none'
              // }, 1000)
              that.startBluetoothDevicesDiscovery()
            }
          })
        } else {
          wx.hideLoading()
          wx.showToast({
            title: '连接失败，请重试',
            icon: 'none',
          })
        }
      }
    })
  },
  //扫描并发现外围设备
  //第三步
  startBluetoothDevicesDiscovery() {
    var that = this
    if (this.data._discoveryStarted) return
    this.setData({
      _discoveryStarted: true
    })
    //调用API扫描发现外围设备
    wx.startBluetoothDevicesDiscovery({
      // services:["6E400001-B5A3-F393-E0A9-E50E24DCCA9E"],
      // allowDuplicatesKey: true,
      interval: 1000,
      success: (res) => {
        console.log('startBluetoothDevicesDiscovery success', res)
        // 设置定时器，30秒后停止查找
        setTimeout(function () {
          if (that.data.searchFlag) {
            wx.hideLoading();
            wx.showToast({
              title: '未搜索到设备',
              icon: 'none',
            })
            wx.stopBluetoothDevicesDiscovery({
              success: function (res) {
                console.log('停止查找蓝牙设备', res)
              }
            })
          }
        }, 30000)
        // this.getBluetoothDevices()
        //监听蓝牙发现的设备
        this.onBluetoothDeviceFound()
      },
    })
  },
  //监听蓝牙搜索设备
  //第四步
  onBluetoothDeviceFound() {
    var deviceData = []
    var that = this
    //返回扫描到的设备
    wx.onBluetoothDeviceFound((res) => {
      console.log('返回扫描到的设备', res);
      res.devices.forEach(device => {
        if ((!device.name && !device.localName) || !device.connectable) {
          // wx.hideLoading();
          // wx.showToast({
          //   title: '未搜索到设备',
          //   icon: 'none',
          // }) 
          return
        }
        if (device.localName === this.data.info.iccid || device.name === this.data.info.iccid) {
          //deviceData.push(device)
          that.createBLEConnection(device.deviceId, device.name)
          //flag = false
          that.data.searchFlag = false
          return
        }
        // console.log(device, '可连接的设备');
        // that.data.bluetoothDeviceName == device.name
        // that.data.bluetoothDeviceName == device.localName
        // if (device.name.includes(that.data.bluetoothDeviceName) || device.localName.includes(that.data.bluetoothDeviceName)){
        // console.log('符合条件', device);
        //发现设备的id
        // let deviceList = that.data.devices
        // deviceList.some(function (item) {
        //   return item.deviceId == device.deviceId
        // })
        // console.log(findSome, '打印');
        // if (!findSome) {
        //   deviceList.push(device)
        //   that.setData({
        //     devices: deviceList
        //   })
        // }
        //}
      })
      // res.devices.forEach(device => {

      // })

    })
  },
  //点击事件创建设备连接
  createBLEConnection(id, name) {
    // console.log(e, '点击事件');
    // wx.showLoading({
    //   title: '连接中'
    // })
    //保存deviceId,name到本地
    let deviceId = id
    wx.setStorageSync('blueDeviceID', id)
    wx.setStorageSync('blueDeviceName', name)
    //this.updateMacAddress(deviceId)


    //调用API连接设备
    wx.createBLEConnection({
      //连接的设备id
      deviceId,
      //连接成功
      success: (res) => {

        // this.door()

        console.log('连接成功', res);

        //获得service
        wx.getBLEDeviceServices({
          deviceId, // 搜索到设备的 deviceId
          success: (res) => {

            let serviceIDs = []
            for (let i = 0; i < res.services.length; i++) {
              if (res.services[i].isPrimary) {
                serviceIDs.push(res.services[i].uuid)
                // 可根据具体业务需要，选择一个主服务进行通信
              }
              //保存serviceID们到本地
              wx.setStorageSync('blueServiceIDs', serviceIDs)

            }
          },
          fail: (res) => {
            console.log(res, 999);
          }
        })
        //读写特征值
        wx.getBLEDeviceCharacteristics({
          deviceId, // 搜索到设备的 deviceId
          serviceId: wx.getStorageSync('blueServiceIDs'), // 上一步中找到的某个服务
          success: (res) => {
            console.log(res, 123456)
            for (let i = 0; i < res.characteristics.length; i++) {
              let item = res.characteristics[i]

              if (item.properties.write) { // 该特征值可写
                // 本示例是向蓝牙设备发送一个 0x00 的 16 进制数据
                // 实际使用时，应根据具体设备协议发送数据
                let buffer = new ArrayBuffer(1)
                let dataView = new DataView(buffer)
                dataView.setUint8(0, 0)
                wx.writeBLECharacteristicValue({
                  deviceId,
                  serviceId,
                  characteristicId: item.uuid,
                  value: buffer,
                })
              }
              if (item.properties.read) { // 该特征值可读
                wx.readBLECharacteristicValue({
                  deviceId,
                  serviceId,
                  characteristicId: item.uuid,
                })
              }
              if (item.properties.notify || item.properties.indicate) {
                // 必须先启用 wx.notifyBLECharacteristicValueChange 才能监听到设备 onBLECharacteristicValueChange 事件
                wx.notifyBLECharacteristicValueChange({
                  deviceId,
                  serviceId,
                  characteristicId: item.uuid,
                  state: true,
                })
              }
            }
          },
          fail: (res) => {
            console.log(res, 999);
          }
        })
        /* wx.onBLECharacteristicValueChange(characteristic => {
            // TODO 收到的信息为ArrayBuffer类型，可根据自己的需要转换 可发送给父组件用来回显
            console.log("收到原始的数据", characteristic, characteristic.value);
            // 测试向设备发送数据
            // this.writeBLECharacteristicValue(JSON.stringify({"FAN":"OFF"}))
          });
          
          //向蓝牙设备写入二进制数据
          writeBLECharacteristicValue(jsonStr);
           
            let arrayBufferValue = str2ab(jsonStr);
            console.log("发送数据给蓝牙", "原始字符串", jsonStr, "转换arrayBuffer", arrayBufferValue);
  
            wx.writeBLECharacteristicValue({
              deviceId: this._deviceId,
              serviceId: this._serviceId, 
              characteristicId: this._characteristicId,
              value: arrayBufferValue, // 只能发送arrayBuffer类型数据
              
              success(res) {
                console.log("消息发送成功", res.errMsg);
                wx.showToast({ title: "消息发送成功", icon: "none" });
              },
              fail(e) {
                console.log("发送消息失败", e);
                wx.showToast({ title: "发送消息失败,错误信息: " + e.errMsg, icon: "none" });
              },
            });
  
   */

        //停止蓝牙搜索设备
        //this.stopBluetoothDevicesDiscovery()
        wx.stopBluetoothDevicesDiscovery({
          success: function (res) {
            console.log('停止查找蓝牙设备', res)
          }
        })

        let str1 = 'showList[' + this.data.appointmentIndex + '].disable'
        this.setData({
          [str1]: true
        })
        let str2 = 'showList[' + this.data.appointmentIndex + '].btnShow'
        this.setData({
          [str2]: true
        })
        this.door()
        // setTimeout(() => {
        //   wx.navigateBack({
        //     delta: 1
        //   })
        // }, 500)
      },
      fail: (err) => {
        wx.hideLoading();
        wx.showToast({
          title: '连接失败，请重试',
          icon: 'none'
        })
        console.log(err, '连接失败');
      }
    })
  },
  getBluetoothData() {
    if (wx.getStorageSync('blueDeviceID')) {
      this.setData({
        deviceID: wx.getStorageSync('blueDeviceID'),
        deviceName: wx.getStorageSync('blueDeviceName'),
        serviceIDs: wx.getStorageSync('blueServiceIDs'),
      })
      //再次调用getDetail，发送开门命令
      ports.ModuleMachine.getMachineDetail({
        machineID: this.data.id
      }).then(res => {})


    }
  },
  //获取BEL的特征
  getBLEDeviceCharacteristics(e) {
    //调用设备服务的API
    let deviceId = wx.getStorageSync('blueDeviceID')
    var serviceId = e.currentTarget.dataset.item;
    this.setData({
      charas: [],
      serviceIDSel: serviceId
    })
    wx.getBLEDeviceCharacteristics({
      deviceId,
      serviceId,
      success: (res) => {
        let charas = []
        for (let i = 0; i < res.characteristics.length; i++) {
          let chara = {}
          let that = this
          let item = res.characteristics[i]
          chara.id = item.uuid
          chara.readable = "indicate:" + item.properties.indicate + ",notify:" + item.properties.notify + ",read:" + item.properties.read + ",write:" + item.properties.write
          charas.push(chara)
          this.setData({
            charas
          })

          if (item.properties.notify || item.properties.indicate) {
            // 启用蓝牙低功耗设备特征值变化时的 notify 功能
            // 必须先启用 wx.notifyBLECharacteristicValueChange 才能监听到设备 onBLECharacteristicValueChange 事件
            // 启用特征值通知
            wx.notifyBLECharacteristicValueChange({
              deviceId, // 蓝牙设备ID
              serviceId, // 服务的UUID
              characteristicId: wx.getStorageSync('uuid'), // 特征值的UUID
              state: true, // true 表示启用通知
              success: function (res) {
                console.log('启用通知成功', res);
                console.log(item.properties.notify, 123456)
              },
              fail: function (err) {
                console.error('启用通知失败', err);
              },
              catch (error) {
                console.error("启用通知过程中发生错误", error);
              }
            });
            // 监听特征值变化
            wx.onBLECharacteristicValueChange(function (characteristic) {
              // 处理接收到的数据
              let dataView = new DataView(characteristic.value);
              let receivedData = '';
              for (let i = 0; i < dataView.byteLength; i++) {
                receivedData += String.fromCharCode(dataView.getUint8(i));
              }
              console.log('接收到的数据:', receivedData);
              // 根据业务逻辑处理接收到的数据
            });

          }

        }
      },
      //错误
      fail(res) {
        console.error('getBLEDeviceCharacteristics', res)
      }
    })
  },
  //可读特征值
  readBLECharacteristicValue(e) {
    console.log(e);
    let that = this
    let uuid = e.currentTarget.dataset.item;
    wx.setStorageSync('uuid', uuid)
    wx.readBLECharacteristicValue({
      deviceId: wx.getStorageSync('blueDeviceID'),
      serviceId: this.data.serviceIDSel,
      characteristicId: uuid,
      success: function (res) {},
      fail: function () {
        that.setData({
          charValue: '不可读'
        })
      }
    })
    // 监听特征值变化
    wx.onBLECharacteristicValueChange(function (characteristic) {
      let res = ab2hex(characteristic.value)
      that.setData({
        charValue: res
      })
      wx.setStorageSync('characteristic', characteristic)
      console.log(wx.getStorageSync('characteristic'))
      console.log("收到原始的数据", characteristic, characteristic.value);
      // 处理接收到的数据
      let dataView = new DataView(characteristic.value);
      let receivedData = '';
      // 假设设备发送的数据是UTF-8编码的字符串
      for (let i = 0; i < dataView.byteLength; i++) {
        receivedData += String.fromCharCode(dataView.getUint8(i));
      }

      console.log('接收到的数据:', receivedData);
      /* // 处理接收到的数据
        // 将 ArrayBuffer 转换为合适的格式，例如十六进制字符串
        let dataView = new DataView(characteristic.value);
        //转格式为十六进制字符串，暂时不用
        //let hexString = Array.from(dataView).map(byte => byte.toString(16).padStart(2, '0')).join(' '); 
        console.log('接收到的数据:', dataView);
      
        // 根据业务逻辑处理接收到的数据
        // 例如，可以在这里更新应用的状态或UI  */
    })
  },
  //写BEI特征值
  writeBLECharacteristicValue(jsonData) {
    console.log(jsonData, "jsondata")
    // 向蓝牙设备发送数据
    //let uuid = wx.getStorageSync('uuid');
    let that = this
    const serviceId = '0000FFF0-0000-1000-8000-00805F9B34FB' //写死 每个蓝牙都一样
    const characteristicId = "0000FFF2-0000-1000-8000-00805F9B34FB" //写死 每个蓝牙都一样
    let asciiArray = jsonToAscii(jsonData)
    let buffer = new ArrayBuffer(asciiArray.length);
    let dataView = new DataView(buffer)
    for (let i = 0; i < asciiArray.length; i++) {
      dataView.setUint8(i, asciiArray[i]);
    }
    wx.writeBLECharacteristicValue({
      deviceId: wx.getStorageSync('blueDeviceID'),
      serviceId,
      characteristicId,
      value: buffer,
      success(res) {
        console.log("消息发送成功")
        // let params = {
        //   sessionID: wx.getStorageSync('USER_SESSIONID'),
        //   machineID: that.data.id,
        // }
        // ports.ModuleAll.openOneMachineRecord(params).then(res => {
        //   if (res.data.header.code===0) {
        //     console.log("开始一次机器使用记录")
        //   }
        // })




      },
      fail(e) {
        console.log("发送消息失败: " + e.errMsg, );
      },
    })
    wx.onBLECharacteristicValueChange((characteristic) => {
      console.log(characteristic, "characteristic")
    })
  },

  light() {
    this.action('GD_1', 'on')
  },
  door() {
    wx.hideLoading();
    // wx.showToast({
    //   title: '连接成功',
    //   icon: 'none',
    //   duration:3000
    // })

    //let code = e.currentTarget.dataset.id
    //let command = ""
    //let r = this.data.sensorList.find((sensor)=>{sensor.code==="door"})
    this.action('door', 'unlock')
  },
  //按下按钮对应的操作
  action(sensor, act) {
    let that = this
    //调用写特征值（开门命令
    //this.writeBLECharacteristicValue(jsonData)
    this.getCommand(sensor, act).then(jsonData => {
      if (jsonData == 'fail')
        return alert('获取通讯key失败')
      wx.closeBLEConnection({
        deviceId: wx.getStorageSync('blueDeviceID'), // 蓝牙设备ID
        success: (res) => {
          wx.removeStorageSync('blueDeviceID')
          console.log('断开蓝牙连接成功', res);
        },
        fail: (err) => {
          console.log('断开蓝牙连接失败', err);
        }
      });
      // if (!wx.getStorageSync('blueDeviceID'))
      //   return alert('请连接蓝牙设备') 
      let params = {
        sessionID: wx.getStorageSync('USER_SESSIONID'),
        machineID: that.data.id,
        operateType: 13,
        operateName: '蓝牙连接',
        lastScanMac: that.data.info.macAddress,
      }
      ports.ModuleAll.operateOneMachine(params).then(res => {
        if (res.data.header.code === 0) {
          console.log("开始一次机器使用记录")
        }
      })
      wx.showToast({
        title: '请在听到门锁啪嗒一声后，再推门进入',
        icon: 'none',
        duration: 1000
      })
      setTimeout(() => {
        wx.setStorageSync('enterFlag', true)
        this.setData({
          enterFlag: true
        })
        console.log(this.data.enterFlag, this.data.ornot)
        let machineID = that.data.id
        let appointmentID = that.data.appointmentID;
        let trainBusinessID = that.data.info.trainBusinessID
        let shopID = that.data.info.shopID;
        let mapAddress = that.data.info.address;
        let mapX = that.data.info.mapX;
        let mapY = that.data.info.mapY;
        let tabIndex = wx.getStorageSync('tabIndex');
        let orderSeq = wx.getStorageSync('orderSeq');
        wx.navigateTo({
          url: `/packageA/pages/learningOperation/learningOperation?appointmentID=${appointmentID}&machineID=${machineID}&shopID=${shopID}&mapAddress=${mapAddress}&mapX=${mapX}&mapY=${mapY}&trainBusinessID=${trainBusinessID}&tabIndex=${tabIndex}&seq=${orderSeq}`,
        })
      }, 1000)
      //调用写特征值（开门命令
      this.writeBLECharacteristicValue(jsonData)
    })
  },
  //获取蓝牙命令Json
  getCommand(sensorCode, commandValue) {
    return new Promise(resolve => {
      //先获取实训仓的 所有传感器列表
      ports.ModuleMachine.getSensorList({
        machineID: this.data.id,
        pageNumber: 999
      }).then(res => {
        if (res.data.header.code == 0) {
          this.setData({
            sensorList:res?.data?.body?.data?.rows || []
          })
          let sensorID = ''
          //筛选出 传入名称 的传感器
          res.data.body.data.rows.forEach(row => {
            if (row.code == sensorCode)
              sensorID = row.sensorID
          });
          //获取该传感器的操作命令，先 写死 unlock 开门
          ports.ModuleMachine.getOneMachineCommandForSanOne({
            machineID: this.data.id,
            // sensorID,
            code:'door',
            commandValue,
          }).then(res2 => {
            if (res2.data.header.code == 0) {
              let c = "SE" + wx.getStorageSync('orderSeq') + "_computer"
              console.log("c",c);
              console.log("sensorList"+this.data.sensorList);
              let o=this.data.sensorList.find(sensor => sensor.code === c)
              console.log("company",o);
              //请先关门电脑自动打开
              this.sleep(5000).then(() => {
                this.querySensorStatus(sensorID,o)
              })
                resolve(res2.data.body.data)       
            } else {
              return resolve('fail')
            }
          })
        } else return resolve('fail')
      })
    })
  },
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  },
  querySensorStatus(sensorID,o,attempt = 1, maxAttempts = 99){
    console.log("查询门是否关闭");
    
    //睡眠
    
    ports.ModuleMachine.getOneSensorDetail({
      sensorID,
    }).then(res9 => {
      console.log("openStatus"+res9.data.body.data.openStatus);
      if (res9.data.header.code == 0&&res9.data.body.data.openStatus==0) {
        
        
        ports.ModuleMachine.getOneMachineCommandForSanOne({
          machineID: this.data.id,
          // sensorID: o.sensorID,
          code:'door',
          commandValue: "on",
        }).then(res3 => {
          if (res3.data.header.code == 0) {
            console.log("开启电脑")
            
          } else {
            return resolve('fail')
          }
        })
      }else{
        if (attempt < maxAttempts) {
          wx.showToast({
            title: '请关闭门锁',
            icon: 'none',
            duration: 1000
          })
          setTimeout(() => {
            this.querySensorStatus(sensorID,o, attempt + 1, maxAttempts);
          }, 2000);
        }else {
          // 达到最大尝试次数，处理失败情况
          console.error('达到最大尝试次数，无法开启电脑');
        }
      }
    })
  },
  getSensor() {
    var that = this;
    ports.ModuleMachine.getSensorList({
      machineID: this.data.id,
      pageNumber: 99,
    }).then(res => {
      if (res.data.header.code == 0) {
        that.setData({
          sensorList: res.data.body.data.rows
        })

      } else return alert('更新失败')
    })
  },
  getDistance() {
    function calculateDistance(lat1, lng1, lat2, lng2) {
      var that = this;
      let rad1 = lat1 * Math.PI / 180.0;
      let rad2 = lat2 * Math.PI / 180.0;
      let a = rad1 - rad2;
      let b = lng1 * Math.PI / 180.0 - lng2 * Math.PI / 180.0;
      let s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2) + Math.cos(rad1) * Math.cos(
        rad2) * Math.pow(
        Math.sin(b / 2), 2)));
      s = s * 6378.137;
      s = Math.round(s * 10000) / 10000;
      s = s.toString();
      s = s.substring(0, s.indexOf('.') + 2);
      console.log('距离：', s);
      return s; //返回距离
    }
    wx.getLocation({
      type: 'gcj02', // 默认为 wgs84 返回 gps 坐标，gcj02 返回可用于 wx.openLocation 的坐标
      altitude: true, // true 会返回高度信息
      isHighAccuracy: true, //开启高精度定位
      highAccuracyExpireTime: 3100, //高精度定位超时时间(ms)
      success: (res) => {
        console.log(res, '获取位置成功');
        console.log(this.info, '获取位置成功');
        // let distance = calculateDistance(this.data.info.mapX,this.data.info.mapY, res.latitude, res.longitude)
        let distance = longitude.calculateDistance(this.data.info.mapY, this.data.info.mapX, res.latitude, res.longitude)
        console.log(distance, "距离")
        // let distance = longitude.calculateDistance(this.info.mapX, this.info.mapY, res.latitude, res.longitude)
        this.setData({
          distance
        })
        this.setData({
          myX: res.latitude
        })
        this.setData({
          myY: res.longitude
        })
      }
    })

  },
  // getTrainBusinessDetail(id) {
  //   let params = {
  //     trainBusinessID: id
  //   }
  //   ports.ModuleAll.getTrainBusinessDetail(params).then(res => {
  //     let obj = res.data.body.data.rows || {}
  //     this.setData({
  //       flag: obj.myMemberOrderPayStatus === 4 ? true : false
  //     })
  //   })
  // },
  appointment() {
    let url = `/packageA/pages/appointment/appointment?machineName=${this.data.info.name}&machineID=${this.data.id}&trainBusinessID=${this.data.info.trainBusinessID}`
    console.log(url)
    wx.navigateTo({
      url,
    })
  },
  //去付款
  payOrder(e) {
    var id = e.currentTarget.dataset.id
    if (!id) {
      wx.showToast({
        title: '订单异常',
        icon: 'none'
      })
      return
    }
    var openid = wx.getStorageSync("USER_OPENID")
    if (!openid) {
      wx.showToast({
        title: '用户信息异常',
        icon: 'none'
      })
      return
    }
    wx.showToast({
      title: '提交中',
      icon: 'loading',
      mask: true
    })

    let payData = {}
    payData.memberOrderID = id
    payData.openid = openid
    payData.payWayID = app.paywayID
    payData.body = '购买商品'
    ports.ModuleAll.getWeixinPayInfo(payData).then(res => {
      if (res.data.header.code == 0) {
        var data = res.data.body;
        wx.requestPayment({
          timeStamp: data.timeStamp,
          nonceStr: data.nonceStr,
          package: data.package,
          signType: data.signType,
          paySign: data.paySign,
          success(res) {
            wx.showToast({
              title: '支付成功',
              icon: 'none',
              mask: true,
              duration: 1000
            })
          },
          fail() {
            wx.showToast({
              title: '取消支付',
              icon: 'none',
              mask: true,
              duration: 1000
            })
            // setTimeout(() => {
            //   wx.redirectTo({
            //     url: '/packageA/pages/goodsShop/orderList/orderList',
            //   })
            // }, 1000);
          }
        })
      } else {
        wx.showToast({
          title: res.data.header.msg,
          icon: 'none'
        })
      }
    })

  },


  toRegister(e) {

let nostu = e.currentTarget.dataset.nostu
    let previousUrl = `/packageA/pages/machineDetail/machineDetail?id=${this.data.id}`

    // 如果来自邀请页面，添加邀请标识
    let fromInviteParam = this.data.fromInvite ? '&fromInvite=true' : '';

    let url = `/packageA/pages/trainBusinessDetail/trainBusinessDetail?id=${
      e.currentTarget.dataset.id}&nostu=${nostu || ''}&trainCompanyID=${e.currentTarget.dataset.traincompanyid || '' }&toCompanyID=${e.currentTarget.dataset.tocompanyid || ''}&mid=${this.data.id}&price=${
        e.currentTarget.dataset.price || ''}&fromInvite=${fromInviteParam}&url=${previousUrl}`
        console.log(url);
    wx.navigateTo({
      url
    })
  },
  async studentRegistration(){
    
    const res = await this.handleCreateOneMemberTrain(this.data.info)
    if (res.data.header.code == 0) {
        wx.showToast({
          title: '报名成功',icon:'success',duration: 1500
        })
        setTimeout(() => {
          wx.navigateBack()
        }, 2000);
      }

    
  },
   /**
     * 创建一名会员培训记录
     * @param {Object} info 
     */
  async handleCreateOneMemberTrain(info) {
    const resTwo = await ports.ModuleAll.getTrainBusinessDetail({
      trainBusinessID: info.trainBusinessID
    })
    if (!resTwo.data?.body?.trainBusiness?.myMemberTrainID) {
      let params = {
        siteID: app.siteID,
        shopID: app.shopID,
        buyType: 1, //类型为培训业务
        trainBusinessID: info.trainBusinessID,
        joinMemberID: wx.getStorageSync('USER_MEMBERID') || '',
        joinStudentID: wx.getStorageSync('USER_STUDENT').studentID || '',
        customerCompanyID: wx.getStorageSync('USER_STUDENT').companyID || '',
        playCompanyID: this.info.playCompanyID || '',
        applyType: 1,
        status: 15,
        bizType: info.bizType,
      }

      return res = await ports.ModuleAll.createOneMemberTrain(params)
    }
  },
  /**
     * 获取会员培训记录
     * @param {String} joinMemberID 
     * @returns {Object} 会员培训记录
     */
    async getMemberTrain(joinMemberID){
        let params = {
          applicationID: app.applicationID,
          trainBusinessID: this.data.trainBusinessID,
          joinMemberID,
          sortTypeTime:1,
          currentPage:1,
          pageSize:1
        }
        const res =await ports.ModuleAll.getMemberTrainList(params)
        if(res.data.header.code!=0){
          throw new Error(res.data.header.msg)
        }
        return res.data.body?.data?.rows?.[0]  || ''
      },
  toSetting(e) {
    console.log(e)
    let url = `/packageA/pages/showChart/showChart?id=${this.data.id}&seq=${wx.getStorageSync('orderSeq')}`
    wx.navigateTo({
      url
    })
  },
  toLearning(e) {
    let that = this;
    console.log(this.data.enterFlag);
    if (this.data.enterFlag) {
      let machineID = that.data.id
      let appointmentID = that.data.appointmentID;
      let trainBusinessID = that.data.info.trainBusinessID
      let shopID = that.data.info.shopID;
      let mapAddress = that.data.info.address;
      let mapX = that.data.info.mapX;
      let mapY = that.data.info.mapY;
      let tabIndex = wx.getStorageSync('tabIndex');
      let orderSeq = wx.getStorageSync('orderSeq');
      wx.navigateTo({
        url: `/packageA/pages/learningOperation/learningOperation?appointmentID=${appointmentID}&machineID=${machineID}&shopID=${shopID}&mapAddress=${mapAddress}&mapX=${mapX}&mapY=${mapY}&trainBusinessID=${trainBusinessID}&tabIndex=${tabIndex}&seq=${orderSeq}`,
      })
    } else {
      let appointmentID = that.data.appointmentID;
      let isVisit = true;
      let trainBusinessID = that.data.info.trainBusinessID
      let shopID = that.data.info.shopID;
      let mapAddress = that.data.info.address;
      let mapX = that.data.info.mapX;
      let mapY = that.data.info.mapY;
      wx.navigateTo({
        url: `/packageA/pages/learningOperation/learningOperation?appointmentID=${appointmentID}&shopID=${shopID}&mapAddress=${mapAddress}&mapX=${mapX}&mapY=${mapY}&trainBusinessID=${trainBusinessID}&isVisit=${isVisit}&machineID=${that.data.id}`,
      })
    }
  },
  toSensorBox(e) {
    let that = this;

    let machineID = that.data.id
    wx.navigateTo({
      url: `/packageA/pages/sensorBox/sensorBox?machineID=${machineID}`,
    })

  },
  toVideo(e) {
    let url = `/packageA/pages/caseList/caseList?navigatorID=${app.videoID}`
    wx.navigateTo({
      url
    })
  },
  checkSession() {
    return new Promise((resolve) => {
      ports.ModuleAll.checkSessionIsOK({
        sessionID: wx.getStorageSync('USER_SESSIONID')
      }).then(res => {
        resolve(res.data.body.isTimeOut)
      })
    })
  },
  fetchContent: function () {
    let params = {
      articleID: app.aboutUsArticleID
    }
    ports.ModuleAll.getArticleMoreDetail(params).then(res => {
      if (res.data.header.code === 0) {
        this.setData({
          content: res.data.body.data.content,
          showModal: true
        })
        wx.setStorageSync('hasShowModal', true)
      }
    })
  },
  /**
   * 关闭模态框
   */
  handleCloseModal: function () {
    this.setData({
      showModal: false
    });
    this.getDistance();
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    let learningStepsStatus = wx.getStorageSync('learningStepsStatus')
    const enterFlag = wx.getStorageSync('enterFlag');
    if (learningStepsStatus&&enterFlag==true) {
      this.setData({
        onLearningFlag: true
      })
    } else {
      this.setData({
        onLearningFlag: false
      })
    }
    if (enterFlag!=true&&enterFlag!=false) {
      wx.setStorageSync('enterFlag', false)
      this.setData({
        enterFlag: false
      })
    } else {
      this.setData({
        enterFlag: wx.getStorageSync('enterFlag')
      })
    }
    const shouldRefresh = wx.getStorageSync('refreshData');
    if (shouldRefresh) {
      // 清除标志
      wx.removeStorageSync('refreshData');
      // 执行数据刷新操作

      this.init()
      // this.loadData();
    }else{
      this.init()
    }
    this.getBluetoothData()
  },
  
  backTrain() {
    wx.showModal({
      title: '返回实训',
      content: '您有进行中的实训，是否返回实训？',
      complete: (res) => {
        if (res.cancel) {
          console.log('取消返回实训')
        }

        if (res.confirm) {
          console.log("返回实训")
          let url = `/packageA/pages/learningSteps/learningSteps`
          wx.navigateTo({
            url: url,
          })
        }
      }
    })
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {
    let enterFlag = wx.getStorageSync('enterFlag')
    if(enterFlag==true){
      let that = this
      let machineID = that.data.id
      let appointmentID = that.data.appointmentID;
      let trainBusinessID = that.data.info.trainBusinessID
      let shopID = that.data.info.shopID;
      let mapAddress = that.data.info.address;
      let mapX = that.data.info.mapX;
      let mapY = that.data.info.mapY;
      let tabIndex = wx.getStorageSync('tabIndex');
      let orderSeq = wx.getStorageSync('orderSeq');
      
      let url= `/packageA/pages/learningOperation/learningOperation?appointmentID=${appointmentID}&machineID=${machineID}&shopID=${shopID}&mapAddress=${mapAddress}&mapX=${mapX}&mapY=${mapY}&trainBusinessID=${trainBusinessID}&tabIndex=${tabIndex}&seq=${orderSeq}`

      wx.setStorageSync('urlForEnterFlag', url)
    }else{
      wx.removeStorageSync('urlForEnterFlag')
    }
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

    utils.isLoginTimeOut()
    this.init()

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    this.loadMore()
    console.log("触发上拉触底事件")
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})
// ArrayBuffer转16进度字符串示例
function ab2hex(buffer) {
  var hexArr = Array.prototype.map.call(
    new Uint8Array(buffer),
    function (bit) {
      return ('00' + bit.toString(16)).slice(-2)
    }
  )
  return hexArr.join('');
}
// stringToASCII 字符串转ASCII编码
function stringToASCII(str) {
  let asciiArray = [];
  for (let i = 0; i < str.length; i++) {
    // 将每个字符转换为其ASCII码对应的数值
    let asciiValue = str.charCodeAt(i);
    asciiArray.push(asciiValue);
  }
  return asciiArray;
}

function jsonToAscii(jsonObj) {
  // 将JSON对象转换为字符串
  const jsonString = JSON.stringify(jsonObj);
  // 将字符串转换为ASCII码数组
  const asciiArray = jsonString.split('').map(char => char.charCodeAt(0));
  // 返回ASCII码数组
  return asciiArray;
}

function alert(text) {
  wx.showToast({
    title: text,
    icon: 'none'
  })
}