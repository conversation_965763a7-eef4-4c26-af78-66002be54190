var ports = require("../../../utils/ports");
var app = getApp()
var starIndex = 0 ////评判分数
var timer = null;

// packageA/pages/onLine/onLine.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    logoImg:app.logoimg,
    scrollBottom: '',
    memberID: '',
    serviceEventID: '',
    memberGroupID: '',
    message: '',
    imageArr: [],
    allMessageObj: [],
    allMessage: [],


    isShowConfirm: false,
    star: '60', //评价分数
    yellow_star: 3, //黄色五角星，默认一开始是黄色星星0分
    gray_star: 2, //灰色五角星 灰色星星是5颗 表示是5分
    star_per: 0, //自定义长度黄色五角星  一开始需要打的是0分
    customerServiceMemberID:'',//当前客服ID
    showScoreBtn:false, // 评价按钮
    star:100, //评分
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log('options',options);

    this.setData({
      serviceEventID: options.serviceEventID != "null" ? options.serviceEventID : '',
      memberGroupID: options.memberGroupID != "null" ? options.memberGroupID : '',
      customerServiceMemberID: options.memberID != "null" ? options.memberID : '',
      memberID: wx.getStorageSync('USER_MEMBERID'),
    },async()=>{
      console.log('this.data.customerServiceMemberID',this.data.customerServiceMemberID)
      wx.showLoading({
        title: '',
      })
      let serviceEventID = this.data.serviceEventID;
      let memberGroupID = this.data.memberGroupID;
      let showScoreBtn = this.data.showScoreBtn;
       // 群组
       if (!this.data.customerServiceMemberID) {
          const gropdata = await this.checkGroup()
          serviceEventID = gropdata.serviceEventID
          memberGroupID = gropdata.memberGroupID;
          if(!memberGroupID){
            wx.hideLoading()
            wx.showToast({
              title: '获取群组失败',
              icon: 'none'
            })
            return
          }
       } 
      //  customerServiceMemberID 一对一
       else{
        const data = await this.checkOne(this.data.customerServiceMemberID)
        console.log(data)
        serviceEventID = data?.serviceEventID;
        if(data?.endTime && !data.askScoreTime){
          showScoreBtn= true;
        }
      }
      wx.hideLoading()
      this.setData({showScoreBtn,serviceEventID,memberGroupID: memberGroupID || ''},()=>{
        console.log({memberGroupID:this.data.memberGroupID,serviceEventID:this.data.serviceEventID})

        this.getMessage();
        timer = setInterval(() => {
          this.getMessage();
        }, 5000)
      })

  
    })
 
  },
// 如果返回的 endTime为空，则表示 可继续聊天，如果 endTime不为空，但是askScoreTime 为空，则 显示按钮 我要评分，按了 让他输入 0-100的分数，调接口 askScoreServiceEvent
  checkOne(memberID){
    return new Promise((resolve,reject)=>{
      let params = {
        sessionID: wx.getStorageSync('USER_SESSIONID'),
        shopID: app.shopID,
        askMemberID: wx.getStorageSync('USER_MEMBERID'),
        memberID: memberID
      }
      ports.ModuleAll.applyOneOnlineSiteService(params).then(res => {
        const data = res?.data?.body?.data || {};
        console.log(res,data)
        resolve(data)
      }).catch(err=>{
        console.log(err)
        resolve({})
      })
    })

  },
  checkGroup(){
    return new Promise((resolve,reject)=>{
      let params = {
        sessionID: wx.getStorageSync('USER_SESSIONID'),
        shopID: app.shopID,
      }
      ports.ModuleAll.applyOneShopSiteService(params).then(res => {
        const data = res?.data?.body || {};
        console.log(res,data)
        resolve(data)
      }).catch(err=>{
        console.log(err)
        resolve({})
      })
    })
  },




  getMessage() {
    this.getToMe()
    this.getMeTo()
  },
  getToMe() {
    let params = {
      companyID: app.companyID,
      sessionID: wx.getStorageSync('USER_SESSIONID'),
      serviceEventID: this.data.serviceEventID,
      memberGroupID: this.data.memberGroupID,
      beginTime: '2024-01-18',
      memberID:this.data.customerServiceMemberID,
      pageNumber: 99
    }

    ports.ModuleAll.getToMeTalkingMessageList(params).then(res => {
      console.log(res.data?.header)
      let list = res.data?.body?.data?.rows || []
      this.allMessageInfo(list)
    })
  },
  getMeTo() {
    let params = {
      companyID: app.companyID,
      sessionID: wx.getStorageSync('USER_SESSIONID'),
      serviceEventID: this.data.serviceEventID,
      memberGroupID: this.data.memberGroupID,
      beginTime: '2024-01-18',
      pageNumber: 99
    }
    ports.ModuleAll.getMeToTalkingMessageList(params).then(res => {
      console.log(res.data?.header)
      let list = res.data?.body?.data?.rows || []
      this.allMessageInfo(list)
    })
  },

  allMessageInfo(messageObj) {
    let allMessage = this.data.allMessage;
    let allMessageObj = this.data.allMessageObj;
    let length = this.data.allMessageObj.length
    //循环遍历数组
    for (var j = 0; j <= messageObj.length; j++) {
      if (messageObj[j] == null) {
        continue;
      }
      let sendMemberMessage = messageObj[j].id;
      if (allMessage.length == 0) {
        allMessage[0] = sendMemberMessage;
        allMessageObj[0] = messageObj[j];
        continue;
      }
      for (var i = 0; i < allMessage.length; i++) {
        //判断addNum 是否与数组中数字重复
        if (allMessage[i] == sendMemberMessage) {
          // flag = true;
          //如有重复，跳出循环
          break;
        }
        //保证数组能够循环一遍后且不重复，再添加元素
        if (i == allMessage.length - 1) {
          //添加元素
          allMessage[i + 1] = sendMemberMessage;
          allMessageObj[allMessageObj.length] = messageObj[j];
          // data.scroll = allMessageObj.length * 100 + 100
        }
      }
    }
    allMessageObj = allMessageObj.sort(function (obj1, obj2) {
      return obj2.sendTimeString > obj1.sendTimeString ? -1 : 1;
    })
    this.setData({
      allMessageObj
    })
    if (length !== allMessageObj.length) {
      wx.nextTick(() => {
        this.setScroll()
      });
    }
    // console.log(this.data.allMessageObj);
  },
  sendOneTalkMemberMessage(type) {
    let params = {
      sessionID: wx.getStorageSync('USER_SESSIONID'),
      companyID: app.companyID,
      shopID: app.shopID,
      serviceEventID: this.data.serviceEventID,
      memberGroupID: this.data.memberGroupID,
      talkType: type,
      name: this.data.message,
      imageURL: this.data.imageArr[0]?.url?this.data.imageArr[0].url:''
    }
    if(this.data.customerServiceMemberID){
      params.sendToMemberID = this.data.customerServiceMemberID
    }

    ports.ModuleAll.sendOneTalkMemberMessage(params).then(res => {
      if (res.data.header.code == 0) {
        this.setData({
          message: '',
          imageArr: []
        })
        this.getMeTo()
      }else{
        console.log(res.data.header.msg)
        wx.showToast({
          title: '发送失败',
          icon:'none'
        })
      }
    })

  },
  sendMessage() {
    console.log(this.data.memberGroupID,this.data.serviceEventID)
    console.log(!this.data.message && this.data.imageArr.length === 0);
    if (!this.data.message && this.data.imageArr.length === 0) {
      return wx.showToast({
        title: '消息不能为空',
        icon: 'none'
      })
    }
    var that = this;
    if (that.data.message) {
      that.sendOneTalkMemberMessage(1);
    } else if (that.data.imageArr) {
      var message = that.data.message ? that.data.message : wx.getStorageSync('USER_MEMBER').name + '发送了一张图片'
      that.setData({
        message
      })
      that.sendOneTalkMemberMessage(2);
    }
  },
  inputValue(e) {
    var that = this;
    that.setData({
      message: e.detail.value
    })
  },
  getimage() {
    var that = this
    var imageArr = this.data.imageArr
    if(imageArr.length!==0)  {
      return wx.showToast({
        title: '一次只能选择一张图片',
        icon:'none'
      })
    }
    wx.chooseMedia({
      count: 1, // 最多可以选择的图片张数，默认9
      sizeType: ['original', 'compressed'], // original 原图，compressed 压缩图，默认二者都有
      sourceType: ['album', 'camera'], // album 从相册选图，camera 使用相机，默认二者都有
      success: function (res) {
        var tempFilePaths = res.tempFiles
        let formData = {
          fileBucketID: app.fileBucketID,
          sessionID: wx.getStorageSync('USER_SESSIONID')
        }
        tempFilePaths.forEach((item) => {
          console.log(item);
          //上传图片
          wx.uploadFile({
            url: app.baseUrl + 'uploadOneFileToQiniu.json',
            filePath: item.tempFilePath,
            name: "file",
            formData,
            success(res) {
              var data = JSON.parse(res.data);
              if (data.body.url) {
                var obj = {}
                obj.url = data.body.url
                imageArr.push(obj)
                that.setData({
                  imageArr
                })
                console.log(that.data.imageArr);
              }
            },
            fail(err) {
              console.log(err);
              wx.showToast({
                title: '图片上传失败',
                icon: 'none'
              })
            }
          })
        })
      }
    })
  },
  imgHeight(e) {
    var index = e.currentTarget.dataset.id
    var imgwidth = e.detail.width;
    imgheight = e.detail.height;
    var ratio = imgwidth / imgheight;
    var viewHeight = 750 / ratio
    var imgheight = viewHeight
    var imageArr = this.data.imageArr
    imageArr[index].imgheight = imgheight
    this.setData({
      imageArr
    })
  },
  getdeleteimg(e) {
    // let index = e.currentTarget.dataset.index
    // let imageArr = this.data.imageArr
    // imageArr.splice(index,1)
    this.setData({
      imageArr: []
    })
  },
  //评价
  getPinjia() {
    this.setData({
      isShowConfirm: true
    })
  },
  //取消评价
  cancel() {
    this.setData({
      isShowConfirm: false
    })
  },
  // 五星
  onScoreChange(e){
    console.log(e.detail)
    const {score} = e.detail;
    this.setData({star:score})
  },
  // 加分按钮
  increaseScore() {
        const {
          star,
        } = this.data;
        const scorePerStar = 100 / 5;
        const newScore = Math.min(100, star + scorePerStar);
        this.setData({
          star: newScore
        })
  },
  // 减分按钮
  decreaseScore() {
        const {
          star
        } = this.data;
        const scorePerStar = 100 / 5;
        const newScore = Math.max(0, star - scorePerStar);
        this.setData({
          star: newScore
        })
  },
  //评论事件,加分
  Btnstar(e) {
    var index = e.currentTarget.dataset.index;
    console.log(index)
    if (index > 0 && index < 5) {
      starIndex = 5
    } else {
      starIndex = starIndex + 1
    }
    console.log(index)
    if (starIndex <= 100) {
      var star = starIndex; //获取的评分
      var yellow_star = parseInt(star); //需要展示的整个黄色5角星，3.62分的时候需要展示3颗整个的黄色五角星。
      var star_per = parseFloat(star - yellow_star) * 100; //3.62颗评价分-3颗整个黄色星，是0.62的占比，先将它*100。这样赋值的时候比较方便。也就是一颗灰色的星星中展示出62%的黄色部位。
      var gray_star = parseInt(5 - star); //需要展示的灰色星星，正常是5-3.62=1.38颗，0.38颗已经是在百分比中了。所以此时最后需要展示的是1整个灰色五角星
      this.setData({
        star: star * 20, //评分数
        yellow_star: yellow_star, //整个黄色五角星的个数
        star_per: star_per, //一颗灰色五角星中黄色五角星的占比
        gray_star: gray_star, //整个灰色五角星的数量
      })
    } else {
      wx.showToast({
        title: '最高一百分',
        icon: 'none'
      })
    }
  },
  //评价事件，减分
  Btnstar2(e) {
    var index = e.currentTarget.dataset.index;
    console.log(index)
    if (index == '0') {
      starIndex = 1
    } else {
      starIndex = starIndex - 1
    }
    console.log(starIndex)
    if (starIndex >= 0) {
      var star = starIndex; //后台获取的评分
      var yellow_star = parseInt(star); //需要展示的整个黄色5角星，3.62分的时候需要展示3颗整个的黄色五角星。
      var star_per = parseFloat(star - yellow_star) * 100; //3.62颗评价分-3颗整个黄色星，是0.62的占比，先将它*100。这样赋值的时候比较方便。也就是一颗灰色的星星中展示出62%的黄色部位。
      var gray_star = parseInt(5 - star); //需要展示的灰色星星，正常是5-3.62=1.38颗，0.38颗已经是在百分比中了。所以此时最后需要展示的是1整个灰色五角星
      this.setData({
        star: star * 20, //评分数
        yellow_star: yellow_star, //整个黄色五角星的个数
        star_per: star_per, //一颗灰色五角星中黄色五角星的占比
        gray_star: gray_star, //整个灰色五角星的数量
      })
    } else {
      wx.showToast({
        title: '最低一分',
        icon: 'none'
      })
    }
  },

  //确定评价
  confil() {
    var that = this
    // 独立接口：会员对客服问题评分
    ports.ModuleAll.askScoreServiceEvent({
      serviceEventID: that.data.serviceEventID,
      askScore: that.data.star //可以为空，默认=100
    })
    
    //结束客服事件
    ports.ModuleAll.workScoreOneServiceEvent({
      serviceEventID: that.data.serviceEventID,
      workScore: that.data.star
    }).then(res => {
      if (res.data.header.code == 0) {
        that.setData({
          isShowConfirm: false,
        })
        wx.showToast({
          title: '评价成功！',
          icon: 'success'
        })
        setTimeout(() => {
          wx.navigateBack();
        }, 1200)
      } else {
        wx.showToast({
          title: '请先聊天，再评价',
          icon: 'none'
        })
      }
    })


  },

  setScroll() { // 数据增加完调用这个方法就会滚动到最下面了
    console.log('+++');
    this.setData({
      scrollBottom: `item${this.data.allMessageObj.length - 1}`
    })
  },


  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {
    clearInterval(timer)
    timer = null
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
    clearInterval(timer)
    timer = null
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})
