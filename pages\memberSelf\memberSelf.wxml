<view class="wrap" style="{{customStyle + '; padding-bottom:' + cust_bar_height   + 'px'}}">
    <view>

        <view class="headerBox">
            <view wx:if="{{memberInfo.name}}" class="settings-button" bindtap="toSettings">
                <image src="/assets/icon/setting_icon.png" class="icon-image"></image>
            </view>
            <!-- <view class="mgQr"></view> -->
            <view class="top_content {{!memberInfo.name ? 'top_content2':''}}">
                <image src="{{memberInfo.avatarURL ? memberInfo.avatarURL : '/assets/images/mine_defaultImg.png'}}" bindtap="toMineInfo">
                </image>
                <text wx-if='{{!memberInfo.name}}' style="flex: 1;text-align: left;font-size: 48rpx;font-weight: bold;margin-left: 24rpx;">未登录</text>

                <view bindtap="toMineInfo" wx-if='{{!memberInfo.name}}' class="btn-login" style="float: right;">授权登录</view>
                <!-- <button class="Btnserto" bindtap="Clearcache">清除缓存</button> -->
                <!-- 登录后的信息 -->
                <view class="header-userInfo" wx-if='{{memberInfo.name}}'>
                    <view class="user_message">
                        <!-- <view>用户名：{{memberInfo.shortName}} -->
                        <view>{{memberInfo.name}}</view>
                        <!-- <view>手机号：{{memberInfo.phone}}</view> -->
                        <view>{{memberInfo.phone}}</view>

                    </view>
                </view>
            </view>
        </view>
    </view>
    <!-- <student-box class="child" id="child" wx:if="{{memberInfo}}"></student-box> -->
    <view class="employee" wx:if="{{banded && memberInfo && code == 0}}">
      <view class="head">
        <view>您绑定了<text class="type-name"> 学生 </text>身份</view>
        <view>学号：{{student.code||'未设置'}}</view>
      </view>
      <view class="desc">
        <view>{{student.companyName||'未设置'}}</view>
        <!-- <view>加入时间：{{student.joinDateStr||'未设置'}}</view> -->
      </view>
    
    </view>

    <directorBox paddingTB="20" paddingLR="30" fontAD="12" radiusAD="0" imgAD="60" navigatorID="a6a49ac4c2434c62a69dea7ad684ab0f"></directorBox>
    <view style="display: flex;width: 100%;position: relative;height: 250rpx;align-items: center;" wx:if="{{memberInfo && code == 0}}">
      <view class="appointment-container" style="display: flex;align-items: center; background-color: #fff;width: 686rpx;height: 128rpx;margin:0 auto;border-radius: 12rpx;box-shadow: #aaa 3rpx 3rpx 10rpx;">
        <image style="width: 270rpx; height: 167rpx;position: relative;background-color: transparent;left: -15rpx;top: -20rpx;" src="https://3onepicture.3onedataqz.com/53847b6ca0754100ba3716afc813f9fc.png" mode=""/>
        <view class="appointment-items-wrapper">
          <view class="appointment-item" wx:for="{{appointments}}" wx:key="index" bindtap="toAppointmentList" data-type="{{item.type}}">
              <view class="status {{item.status}}">{{item.status}}</view>
              <view class="count">{{item.count}}</view>
          </view>
        </view>
    </view>
    </view>

    <serverBox navigatorID="********************************"></serverBox>

    <view wx:if="{{areaFlag&&clearFlag&&reLoadComponent && memberInfo}}" class="operateBox" style="background-color: #F1F8FF;">
        <button wx:if="{{doorFlag}}" type="primary" class="operateBtn" bindtap="openTheDoor">实训舱开门</button>
        <!-- <button type="primary" class="operateBtn" bindtap="openTheTA">助教</button> -->
    </view>
</view>
<customTabBar activeIndex="{{2}}" />