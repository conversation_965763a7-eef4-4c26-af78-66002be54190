<!--packageA/pages/machineDetail/machineDetail.wxml-->
<scroll-view scroll-y="true" bindscrolltolower="loadMore">
<view class="AD">
<!-- 首页弹出框 -->
  <noticeForUNLogin show-modal="{{showModal}}" content="{{content}}" bindclose="handleCloseModal"></noticeForUNLogin>
  <view class="itemBox" wx:if="{{backHome}}">
    <view class="title flex-row justify-between">
      <text>⬆️点击此按钮即可快速返回首页</text>
    </view>
  </view>
  <view class="itemBox">
    <view class="title flex-row justify-between">
      <text>{{info.name}}</text>
      <text>{{info.code}}</text>
    </view>
    <!-- <view class="item-text">培训名称：{{trainBusinessInfo.name}}</view> -->
    <view class="item-text">地址：{{info.address}} (距离{{distance}}米)</view>
    <view wx:if="{{trainBusinessInfo.myMemberTrainID&&enterFlag}}" class="btnAppoint" catchtap="toSetting">设置</view>
    <!-- <view class="item-text">x：{{info.mapX?info.mapX:""}} 我的x：{{myY?myY:""}}</view>
    <view class="item-text">y：{{info.mapY?info.mapY:""}} 我的y：{{myX?myX:""}}</view> -->

    <!-- <view class="item-text">在线状态：{{info.onlineType==1?'在线':info.onlineType==2?'离线':'未知'}}</view>
    <view class="item-text">使用状态：{{info.runType==4?'培训中':info.runType==3?'门开':info.runType==2?'正常':'未知'}}</view>
    <view class="item-text">价格：{{trainBusinessInfo.machineDetail}}</view> -->
  </view>

  <view class="itemBox">
    <view class="title flex-row justify-between">
      <text>{{trainBusinessInfo.name}}</text>
      <text>¥{{trainBusinessInfo.priceCash}}</text>
    </view>
    <view class="item-text">课程描述：</view>
    <rich-text wx:if="{{trainBusinessInfo.mobileDescription}}" class="ql-editor" nodes="{{trainBusinessInfo.mobileDescription}}"></rich-text>
    <text wx:else>无</text>
    <view class="title flex-row justify-between">
      <!-- <button wx:if="{{trainBusinessInfo.myMemberTrainID}}" type="primary" size="mini" style="text-align: center;" class="title " catchtap="toLearning">学习内容</button> -->
    <button wx:if="{{trainBusinessInfo.myMemberTrainID}}" type="primary" size="mini" style="text-align: center;" class="title " catchtap="toSensorBox">物料清单</button>
    <button type="primary" size="mini" style="text-align: center;" class="title " catchtap="backTrain" wx:if="{{trainBusinessInfo.myMemberTrainID&&onLearningFlag}}">返回实训</button>
    
    </view>
    
    <!-- <view style="text-align: center;" class="title serviceBtn" catchtap="toVideo">学习视频</view> -->
    <view wx:if="{{!trainBusinessInfo.myMemberTrainID}}" style="display: flex;">

      <view class="btnAppoint" catchtap="toRegister" data-id="{{trainBusinessInfo.trainBusinessID}}" data-price="{{item.price}}" >我要报名</view>

     

      <!-- <button type="primary" size="mini" style="text-align: center;" class="title" data-id="{{trainBusinessInfo.myMemberOrderID}}" bind:tap="payOrder" wx:if="{{ !governmentCompanyID }}">未付款</button> -->

      <view wx:if="{{wx.getStorageSync('USER_STUDENT')}}" class="btnAppoint" catchtap="studentRegistration" data-id="{{trainBusinessInfo.trainBusinessID}}">studentRegistration</view>
    </view>
    <view wx:else style="display: flex;">
      <view class="btnAppoint" data-id="{{trainBusinessInfo.myMemberOrderID}}" bind:tap="payOrder" if="{{trainBusinessInfo.myMemberTrainStatus == 1 && !governmentCompanyID}}">未付款</view>
    </view>
  </view>
  <view class="itemBox" wx:for="{{trainCompanyInfo}}" wx:key="id" wx:for-index="index">
  <view class="title flex-row justify-between">
    <text>{{item.toCompanyName}}</text>
    <text>¥{{item.price}}</text>
  </view>
    <view class="item-text flex-row justify-between">
      <text>补贴金额：{{item.givePrice}}</text>
      <text>补贴人数：{{item.certificatesNumber?item.certificatesNumber:0}}</text>
    </view>
    <view class="item-text">人数：{{item.applyNumber}}/{{item.maxNumber}}</view>
    <button type="primary" size="mini" style="text-align: center;" class="title" data-id="{{trainBusinessInfo.myMemberOrderID}}" bind:tap="payOrder" wx:if="{{ governmentCompanyID == item.toCompanyID }}">未付款</button>
    <view wx:if="{{!trainBusinessInfo.myMemberTrainID}}">
      <view class="btnAppoint" catchtap="toRegister" data-id="{{item.trainBusinessID}}"  data-trainCompanyid="{{item.trainCompanyID}}" data-nostu="1" data-tocompany id="{{item.toCompanyID}}" data-price="{{item.price}}" wx:if="{{item.status == 2}}">我要报名</view>
      <view wx:else class="btnAppoint" >
        <text wx:if="{{item.status==1}}">未开始</text>
        <text wx:if="{{item.status==3}}">已结束</text>
      </view>
    </view>
  </view>
    <ruleAndVideoBox inSide="{{false}}"  button-type="primary" button-size="mini" bind:clickArticle="clickArticle" bind:toVideo="toVideo"></ruleAndVideoBox>
  <!-- <view wx:if="{{flag===4}}">
    <view class="itemBox" wx:if="{{appointmentList.length === 0}}">
      <view class="noText">暂无数据</view>
    </view>
    <view class="itemBox" wx:else>
      <view class="title flex-row justify-between">
        <text>我的预约</text>
      </view>
      <view class="item flex-column justify-around card" wx:for="{{appointmentList}}" wx:key="id"  data-id="{{item.id}}">
      <view class="item-card">
        <view class="item-text"><a class="info">{{item.openDateStr?item.openDateStr:"暂无数据"}}</a><a class="info">{{item.timeIntervalName?item.timeIntervalName:"暂无数据"}}</a><a class="info">未使用</a></view>
      </view>      
    </view>
  </view>
  <view class="equipment" catchtap="appointment" data-item="{{item}}">预约</view>
  </view>
  <view wx:if="{{flag===null||flag===1}}">
    <view class="equipment" catchtap="toRegister" data-id="{{trainBusinessInfo.trainBusinessID}}">我要报名</view>
  </view>
  <view wx:if="{{flag===0}}">
    <view class="equipment" bindtap='toRegister' data-id="{{trainBusinessInfo.myMemberOrderID}}">立刻付款</view>
  </view> -->

  <!-- <view wx:if="{{trainBusinessInfo.myMemberTrainID}}"> -->
      <view wx:if="{{trainBusinessInfo.myMemberTrainID}}">
    <!-- <view wx:if="{{trainBusinessInfo.myMemberTrainID}}"> -->
    <!-- <view wx:if="{{flag===10||flag===15}}"> -->
      <view wx:if="{{false}}">
    <view class="itemBox" >
      <view class="title">
        
        <text>我的预约</text>
        <text style="color: red;"  wx:if="{{assistFlag}}" >（助教）</text>
        <view class="allBtn">
        <button type="primary" size="mini" class="btnWord" wx:if="{{!enterFlag && distance<=50}}" catchtap="quickEnter" data-item="{{item}}" disabled="{{quickEnterBtnFlag}}">立即进入</button>
        <button type="primary" size="mini" class="btnWord" catchtap="appointment" data-item="{{item}}">预约</button>
        <button type="primary" size="mini" class="btnWord" bind:tap="clickAll">全部</button>
        </view>
      </view>
      <view>
        <view class="itemBox" wx:if="{{showList.length === 0}}">
      <view class="noText">没有获取到数据</view>
    </view>
      <view wx:else style="width: auto;display: grid;padding: 10rpx 0;">
        <view class="item flex-column justify-around card" wx:for="{{showList}}" wx:key="id"  data-id="{{item.id}}" wx:for-index="index" style="{{myMemberID===item.memberID&&assistFlag?'border: 1px solid var(--themeColor);':''}}">

  <!-- 页面内容... -->
  <view class="item-card">
        <view class="item-text">
          <view>
            <view class="info">{{item.dateStr?item.dateStr:""}}</view>
            <view class="info">{{item.timeIntervalName?item.timeIntervalName:""}}</view>
           
            <view class="info" style="color: red;" wx:if="{{myMemberID!==item.memberID&&assistFlag}}">{{item.memberName?item.memberName:""}}</view>
          </view>
          <view>
            <view class="info tag" style="width: 80rpx;padding: 5rpx 0;background-color: {{item.status===1?'rgb(245, 102, 102)':'rgb(216, 215, 215)'}};">{{item.statusStr?item.statusStr:""}}</view>
            <view class="info" wx:if="{{item.orderSeq}}">{{item.orderSeq?item.orderSeq:""}}</view>
            <view class="info" wx:if="{{item.workTypeStr}}">
              {{ item.workTypeStr? item.workTypeStr:""}}
            </view>
          </view>
          <button disabled="{{item.disable&&enterFlag}}" wx:if="{{item.timeFlag&&(distance<=50)}}" class="equipment" bindtap="toBluetooth" data-index="{{index}}" data-id="{{item.appointmentID}}" data-type="{{item.workType}}" data-seq="{{item.orderSeq}}">
            {{connectStr}}
          </button>
          <!-- <button wx:if="{{item.timeFlag&&item.disable}}" class="equipment" bind:tap="light" data-sensor="GD_1">
            开/关灯
          </button> -->
          <!-- <button wx:if="{{item.timeFlag&&item.disable}}" class="equipment" bind:tap="door" data-sensor = "door">
            开/关门
          </button> -->
          
          <!-- <view wx:if="{{item.btnShow}}" class='gs_circle'>
      <view class='gs_incircle' style="{{ornot===1?'background-color:#e7ebed;cursor:auto;':(ornot===0?'':'background-color:#e6e7ed;cursor:auto;')}}">
          <view class='gs_excircle' data-id="{{item.appointmentID}}" bindtap="{{ornot===1?'goout':(ornot===0?'gosign':'')}}">
              <view class='gs_innercircle' style="{{ornot===1?'background-color:green;':(ornot===0?'':'background-color:#ddd;')}}">{{ornot===1?'学习完成\n\n准备离开':(ornot===0?'我已进入\n\n开始学习':'学习结束')}}</view>
          </view>
      </view>
  </view> -->
        </view>
        <view class="item-all">
          
          <view wx:if="{{myMemberID===item.memberID&&!enterFlag}}" class="serviceBtn" data-id="{{item.appointmentID}}" bind:tap="deleteOneAppointment">取消</view>
          <view wx:if="{{enterFlag}}" class="serviceBtn" catchtap="toLearning">返回学习</view>
          <!-- <view wx:if="{{enterFlag}}" class="serviceBtn" catchtap="goout">离开</view> -->
        </view>
        
      </view>  

       
    </view>
      </view>
      <view wx:if="{{hasMore}}" class="serviceBtn" bind:tap="loadMore" style="padding: 10rpx 0;text-align: center;">加载更多...</view> 
      <view wx:else bind:tap="loadMore" style="padding: 10rpx 0;text-align: center;">已经到底</view>     
      </view>
         <!-- 分页组件 -->
        <!--     <view class="page_div">
      <view class="page_sum">共{{pagetotal}}页</view>
      <view class="page_prev" bindtap="prevFn">上一页</view>
      <view class="page_number_div">
        <input value="{{pageNumber}}" bindinput="inputValue" data-name="pageNumber"></input>
        <view class="pageGo" bindtap="pageGo">Go</view>
      </view>
      <view class="page_next" bindtap="nextFn">下一页</view>
</view> -->
  </view>
  <!-- <view class="itemBox BTInfo" wx:if="{{deviceID}}">
    <view class="title">蓝牙设备已连接：{{deviceName}}</view>
    <view class="item-text">设备DeviceID：{{deviceID}}</view>
    <view class="item-text flex-col" wx:for="{{serviceIDs}}" wx:key="item">
      <view class='item-text' bind:tap="getBLEDeviceCharacteristics" data-item="{{item}}">设备ServiceID：{{item}}</view>
    </view>
  </view>

  <view class="itemBox BTInfo" wx:if="{{deviceID}}">
    <view class="title">选择的ServiceID：{{serviceIDSel}}</view>
    <view class="item-text flex-col" bind:tap="readBLECharacteristicValue" data-item="{{item.id}}" wx:for="{{charas}}" wx:key="item">
      <view class='item-text'>特征id：{{item.id}}</view>
      <view class='item-text'>权限：{{item.readable}}</view>
    </view>
  </view>

  <view class="itemBox BTInfo" wx:if="{{deviceID}}">
    <view class="title">特征值：{{charValue}}</view>

  </view> -->

  <!-- <button class="btnAppoint" catchtap="appointment" data-item="{{item}}">预约</button> -->
  <!-- <button class="equipment" bindtap="toBluetooth">
    连接设备
  </button> -->
  
  <view>
    <!-- <input type="text" style="border: 2rpx black solid; width: 100%; height: 20rpx; " value="{{userInput}}" bindinput="onInput" /> -->
    <!-- <button class="equipment" bind:tap="light" data-sensor="GD_1">发送开/关灯</button>
    <button class="equipment" bind:tap="door" data-sensor = "door">发送开/关门命令</button> -->
  </view>
  </view>
  <!-- <view wx:else>
    <view class="btnAppoint" bindtap='toRegister' data-id="{{trainBusinessInfo.trainBusinessID}}">立刻付款</view>
  </view> -->
  </view>
  <view wx:else class="noText">请先报名</view>
</view>
</scroll-view>