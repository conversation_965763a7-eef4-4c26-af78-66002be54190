const ports = require("../../utils/ports");
// 引入工具模块
const utils = require("../../utils/util");
// 引入经度模块
var longitude = require('@/utils/longitude.js')
// 引入好友模块
const friendModule = require('../../utils/friend');
// 引入混合模块
const mixins = require('../../utils/mixins.js');
// pages/index/index.js
// 获取应用实例
const app = getApp();
/**
 * 默认的检查学习类型列表
 * @type {Array<{value: string, name: string, checked: boolean, workType: string}>}
 */
const defaultCheckLearnType = [
  {value: '1', name: '实训/学习',checked:true, workType : '3'},
  {value: '2', name: '考试',checked:false, workType : '10'},
];
Page({
  /**
   * 页面的初始数据
   * @type {Object}
   */
  data: {
    showMemberStudy: false,
    sessionPromise: null,
    showTabBar:true,
    phoneValue:'',
    checkLearnType:defaultCheckLearnType,
    checkedOrInvite:1,// 这个如果是1，那么就显示邀请好友（通讯录里面的）如果是2，那么就显示邀请好友（微信里面的）,如果是3，那么代表他是被邀请过来的，那么他不能邀请
    friendOrGroup:false,// 这个代表弹出框显示的是好友还是群组
    checkedLearnType:'1',
    friendShowModal:false,
    friendList:[],
    logoImg: app.logoimg,


    shareAppointmentDetail:{},
    // 是否是点击的按钮进行的分享
    isCustomShare:false,
    orderSeqForShare:2,


    mySelectType:0,
    selectType:1,
    // 按钮的状态：1：可选；2：已选；3：不可选
    btn1:2,
    btn2:1,
    btn3:1,
    btn4:2,

    hasAppNextHours:{},
    showReminder: false,
    enterFlag:false,
    appointmentIndex:1,
    appointmentID:"",
    timeWorkType:'',
    hasAppointment:false,
    myAppointmentID:'',
    workType:3,
    appointmentNumber:0,
    machineList:[],
    checkedMachine:{
      id:'19d28aa3912b4e29989257c48594ebcb',
      machineName:'优蕊尔1号实训舱',
      bizType:'',
      distance:""
    },// 用户选择的机器,从缓存中获取
    nextEndTime:"",
    nextBeginTime:"",
    isLocating: false, // 开始定位
    cityList:[{applicationCityID:"",cityID:"",cityName:"上海市",name:"上海市",cityShortname:null}],
    latitude: null, //经纬度
    longitude: null, //经纬度
    city: {name:"上海"}, //当前城市信息
    municipalities: [], //直辖市列表
    hotCities: [], //热门城市列表
    recentCities: [], //最近访问的城市
    filterCity: [], //搜索过滤城市
    searchCity: '', //搜索城市
    getCityshow: false, //獲取城市列表 抽屜顯示條件
    geolocationError: false, //定位失败
    memberStatics:{},
    showModal:false,
    focusList:[],
    focusID:"eb63015b412442ac99077a9d6b580b8e",
    selected:"",
    isLoginVisible:false,
    id:"19d28aa3912b4e29989257c48594ebcb",//先写死，后面动态获取
    myMemberID:"",
    backHome:"",
    enterFlag:"",
    clearFlag:"",


    showJuli:false,
    getLocationStatus:false,
    selectItem:{},
    // 定位数据 -->>
    sensorList:[],
    info:{},
    myX: 31.230416,
    myY: 121.473701,
    distance:9999,
    distanceStr:'', // xx 米/ xx 千米
    bluetoothDeviceName:"",
    appointmentIndex:"",
    tabIndex:"",
    orderSeq:"",
    gettingFlag:false,// 是否正在获取地址
    // <<--
    showNotice:false,
    content:"",
    wxName: app.weixinAppTitle,
    height: app.globalData.systemInfo.statusBarHeight,
    top: app.globalData.menuButtonTop + app.globalData.menuButtonHeight / 2,
    curLearningBoxNeed:{
      showCurLearningBox:false,
      enterTime:"",
      appointmentTime:"",
      showType:1,
      canUseApp:[]
    },
    friend1:{},
    friend2:{},
    checkType:1,
    searchFlag: true,
  },
  

    /**
   * 检查好友信息
   * @param {Event} e - 事件对象
   * @returns {*} - 好友模块检查好友的返回值
   */
  checkFriend(e) {
    return friendModule.checkFriend(e, this);
  },

    /**
   * 邀请好友方式2
   * @returns {*} - 好友模块邀请好友方式2的返回值
   */
  inviteFriend2() {
    return friendModule.inviteFriend2(this);
  },
  toshixun(e){
    // let memberstudyid = 
    let url2 = `/packageA/pages/trainSteps/trainSteps?sectionID=${this.data.memberStudyList.sectionID
      }&machineID=${wx.getStorageSync('checkedMachine').id}`
    wx.navigateTo({
      url: url2,
    })
  },
  toExam() {
    let appointment = wx.getStorageSync('appointment')
    let url = `/packageA/pages/examSteps/examSteps?sectionID=${this.data.memberStudyList.sectionID}&shopID=${this.data.shopID}&appointmentID=${appointment.appointmentID}&machineID=${this.data.checkedMachine.id}&memberStudyID=${this.data.memberStudyList.memberStudyID}&beginTimeStr=${this.data.memberStudyList.beginTimeStr}`
    wx.setStorageSync('urlForEnterFlag', url)
    wx.navigateTo({
      url: url
    })
  },
    /**
   * 邀请好友方式3
   * @returns {*} - 好友模块邀请好友方式3的返回值
   */
  inviteFriend3() {
    return friendModule.inviteFriend3(this);
  },

    /**
   * 预约内容类型选择变化
   * @param {Event} e - 事件对象
   */
  radioChange_n(e) {
    const {type, name} = e.currentTarget.dataset;
    // 预约内容的类型，目前实训学习合为一类，只有两类
    wx.setStorageSync('checkedWorkType', { workType: type, workTypeName:name })

    const checkLearnType = this.data.checkLearnType.map(item=>{
      return {
        ...item,
        checked: item.workType === type
      }
    })

    this.setData({
      checkLearnType,
      checkedLearnType:e.currentTarget.dataset.value,
      friend1:{},
      friend2:{},
      workType: type, 
      workTypeName:name
    })
  },
  // radioChange(e) {
  //   const checkLearnType = this.data.checkLearnType
  //   for (let i = 0, len = checkLearnType.length; i < len; ++i) {
  //     checkLearnType[i].checked = checkLearnType[i].value === e.detail.value
  //   }

  //   this.setData({
  //     checkLearnType,
  //     checkedLearnType:e.detail.value,
  //     friend1:{},
  //     friend2:{}
  //   })
  // },

  getCityshowFn() {
    let that = this
    that.setData({
      getCityshow: false,
    })
  },
   // 点击选择城市
  async selectedCity(e) {
    const data = e.currentTarget.dataset.item;
    const city = this.cityModule.selectCity(data);
    this.setData({
      city,
      getCityshow: false
    });
  },

  // 打开城市列表抽屉
  async Cityshow() {
    const result = this.cityModule.openCityPicker();
    this.setData(result);
    
    if (this.data.getCityshow && this.data.cityList.length === 0) {
      try {
        const cityList = await this.cityModule.getCityList();
        this.setData({ cityList });
      } catch (error) {
        console.error('获取城市列表失败:', error);
      }
    }
  },
  // 城市相关功能
  cityModule: require('../../utils/city'),

  click3(){
    this.setData({
      btn3:this.data.btn3==3?3:2,
      btn1:this.data.btn1==3?3:1,
      btn2:this.data.btn2==3?3:1,
      btn4:this.data.btn4==3?3:1,
      selectType:3
    })
  },
  click31(){

  },
  click2(){
    this.setData({
      btn3:this.data.btn3==3?3:1,
      btn1:this.data.btn1==3?3:1,
      btn2:this.data.btn2==3?3:2,
      btn4:this.data.btn4==3?3:1,
      selectType:2
    })
  },
  click21(){

  },
  click1(){
    this.setData({
      btn3:this.data.btn3==3?3:1,
      btn1:this.data.btn1==3?3:2,
      btn2:this.data.btn2==3?3:1,
      btn4:this.data.btn4==3?3:2,
      selectType:1
    })
  },
  click11(){

  },
  click4(){
    this.setData({
      btn3:this.data.btn3==3?3:1,
      btn1:this.data.btn1==3?3:2,
      btn2:this.data.btn2==3?3:1,
      btn4:this.data.btn4==3?3:2,
      selectType:4
    })
  },
  click41(){

  },

  onRadioChange: function(e) {
    console.log(1);
    this.setData({
      selected: e.detail.value
    });
  },

  chooseTime(){
    this.setData({
      isDrawerOpen: !this.data.isDrawerOpen,
    })
    const child = this.selectComponent('#appointmentBox');
    if (child) {
      child.loadForTimes(); // 调用子组件的方法
    }
  },
  closeDrawer(){
    this.setData({
      isDrawerOpen: !this.data.isDrawerOpen,
      btn1:2,
      btn2:1,
      btn3:1,
      btn4:2
    })
    this.getTimes()
  },
  getTimes(){
    let checkedTime = wx.getStorageSync('checkedTime') || {};
    const {beginTime,endTime} = checkedTime.showTime ? this.formatShowTime(checkedTime.showTime,checkedTime.openDate) :{}
    // checkedTime.showTime // showTime: "09:00-09:50"
    console.log(checkedTime.showTime,'checkedTime');// showTime: "09:00-09:50"
    console.log("beginTime,endTime",beginTime,endTime);//beginTime,endTime 07月10日 09:00 09:50
   

    if(checkedTime.myAppointmentID){
      ports.ModuleAll.getOneAppointmentDetail({
        appointmentID: checkedTime.myAppointmentID
      }).then(resp=>{
        this.setData({
          shareAppointmentDetail:resp.data.body.data
        })
      })
    }
    // console.log('hasAppointment-',111,checkedTime.myAppointmentID?true:false)
    let hasAppointment = checkedTime.myAppointmentID?true:false;
    let aaa = {
      applicationID: app.applicationID,
    }
    ports.ModuleAll.getTrainingMachineStatistics(aaa).then(res=>{
      console.log(res,'asdasdasd');
      this.setData({
        machineStatistics: res.data.body.data || ''
      })
    })
    ports.ModuleAll.getCurrentMemberStudyList({machineID:this.data.checkedMachine.id}).then(req=>{
      const row = req.data.body.data.rows[0] || null

      if (row) {
        const startTs = row.beginTime
          ? Number(row.beginTime)
          : new Date(row.beginTimeStr).getTime()
        const diff = Date.now() - startTs
        const show = diff < 2 * 3600 * 1000

        this.setData({
          memberStudyList: row,
          showMemberStudy: show
        })
      } else {
        this.setData({
          memberStudyList: null,
          showMemberStudy: false
        })
      }
    })
    
    
    this.setData({
      nextBeginTime:beginTime,
      nextEndTime:endTime,
      // appointmentNumber:checkedTime.checkedTime,

      hasAppointment,
      myAppointmentID:checkedTime.myAppointmentID?checkedTime.myAppointmentID:'',
      friend1:{},
      friend2:{},
    })
    checkedTime.nextBeginTime = beginTime
    checkedTime.nextEndTime = endTime
    wx.setStorageSync('checkedTime', checkedTime)

    if(!hasAppointment){
      // 这个时间段没有我的预约，那么就直接返回就可以了
      this.setData({
        checkLearnType: defaultCheckLearnType,
        checkedOrInvite:1
      },()=>{
        const {workType, name} = this.data.checkLearnType.find(item=>item.checked) || {};
        wx.setStorageSync('checkedWorkType', { workType, workTypeName:name })
        this.setData({
          workType,
          workTypeName:name
      })
      })
      return
    }

    let params = {
      timeIntervalInstanceID:checkedTime.id,
      flag:1
    }
    ports.ModuleAll.getAppointmentList(params).then(resp => {
      let appointmentList = resp.data.body.data.rows
      if(appointmentList.length>0){
        appointmentList.forEach(item=>{
          // 如果是"我"选择的，那么全部不能再选择了，如果没有我预约的，那么判断别人都预约了哪个位置
          let memberID = wx.getStorageSync('USER_MEMBERID')
          let orderSeq = item.orderSeq
          if(item.memberID == memberID){

          let checkLearnType =[];
          this.data.checkLearnType.forEach(m=>{
            if(m.workType == item.workType) checkLearnType.push({...m,checked:true})
          })
            this.setData({
              checkLearnType,
              checkedOrInvite:2
            },()=>{
              const {workType, name} = this.data.checkLearnType.find(item=>item.checked) || {};
              wx.setStorageSync('checkedWorkType', { workType, workTypeName:name })
              this.setData({ workType, workTypeName:name })
            })
          }else{
          
            let checkLearnType =[];
            this.data.checkLearnType.forEach(m=>{
              if(m.workType == item.workType) checkLearnType.push({...m,checked:true})
            })

            let friend = {}
            friend.memberID = item.memberID
            friend.memberName = item.memberName
            friend.toMemberName = item.memberName
            friend.memberAvatar = item.memberAvatar
            friend.isApplyMember = item.applyMemberID=wx.getStorageSync('USER_MEMBERID')
            if(Object.keys(this.data.friend1).length == 0){
              this.setData({
                friend1:friend
              })
            }else{
              this.setData({
                friend2:friend
              })
            }
            this.setData({
              checkLearnType,
              checkedOrInvite:this.data.checkedOrInvite==2?2:3
            },()=>{
              const {workType, name} = this.data.checkLearnType.find(item=>item.checked) || {};
              wx.setStorageSync('checkedWorkType', { workType, workTypeName:name })
              this.setData({ workType, workTypeName:name })
            })
          }
        })
      }else{
        this.setData({
          checkLearnType: defaultCheckLearnType,
          checkedOrInvite:1
        },()=>{
          const {workType, name} = this.data.checkLearnType.find(item=>item.checked) || {};
          wx.setStorageSync('checkedWorkType', { workType, workTypeName:name })
          this.setData({ workType, workTypeName:name })

        })
      }
    })
  },
  handleChangeIsChecked(e){
    this.setData({
      friendOrGroup:e.detail.isChecked
    })
  },
  formatShowTime(showTime,openDate){
    let times = showTime.split('-')
    let months = openDate.split('-')
    const today = new Date();
    const currentDate =  (today.getMonth() + 1) + '月' + today.getDate() + '日'
    // 构造 beginTime 字符串
    const beginTime = `${months[1]}月${months[2]}日 ${times[0]}`;
    // 对于 endTime，只保留时间部分
    const endTimeFormatted = `${times[1].split(':')[0]}:${times[1].split(':')[1]}`;
    return {
      beginTime,
      endTime: endTimeFormatted
    };
  },
  /**
   * 智能预约时间段选择功能
   * 1. 检查接下来3小时内的预约状态
   * 2. 如果发现当前用户的预约，自动选中该时间段
   * 3. 如果没有当前用户的预约，选中下一个可用时间段
   * @param {Array} timeList - 时间段列表
   * @returns {Object} 选中的时间段信息
   */
  async getHasAppInNextHours(timeList){
    let now = new Date();
    let currentMemberID = wx.getStorageSync('USER_MEMBERID');

    // 获取当前时间信息
    let nowDate = this.getFormattedDate(now);
    let currentHour = now.getHours();

    // 智能选择逻辑：检查接下来3小时内的预约
    let selectedTime = this.findBestTimeSlot(timeList, nowDate, currentHour, currentMemberID);

    if (selectedTime) {
      this.setSelectedTimeSlot(selectedTime, '智能选择');
    } else {
      // 没有找到合适的时间段，清空状态
      this.clearSelectedTimeSlot();
    }

    return selectedTime;
  },

  /**
   * 查找最佳时间段
   * @param {Array} timeList - 时间段列表
   * @param {string} nowDate - 当前日期
   * @param {number} currentHour - 当前小时
   * @param {string} currentMemberID - 当前用户ID
   * @returns {Object|null} 最佳时间段或null
   */
  findBestTimeSlot(timeList, nowDate, currentHour, currentMemberID) {
    console.log('开始查找最佳时间段 - 时间列表长度:', timeList.length);

    // 按时间顺序排序
    let sortedTimeList = timeList.sort((a, b) => {
      if (a.openDate !== b.openDate) {
        return a.openDate.localeCompare(b.openDate);
      }
      return parseInt(a.code) - parseInt(b.code);
    });

    // 优先检查接下来3小时内是否有当前用户的预约
    let userAppointmentInNext3Hours = this.findUserAppointmentInNext3Hours(
      sortedTimeList, nowDate, currentHour, currentMemberID
    );

    if (userAppointmentInNext3Hours) {
      console.log('✅ 找到用户在3小时内的预约:', userAppointmentInNext3Hours);
      return userAppointmentInNext3Hours;
    }

    // 如果3小时内没有用户预约，找下一个可用的时间段
    let nextAvailableTime = this.findNextAvailableTimeSlot(
      sortedTimeList, nowDate, currentHour
    );

    if (nextAvailableTime) {
      console.log('✅ 找到下一个可用时间段:', nextAvailableTime);
      return nextAvailableTime;
    }

    console.log('❌ 未找到合适的时间段');
    return null;
  },

  /**
   * 查找用户在接下来3小时内的预约
   * @param {Array} timeList - 排序后的时间段列表
   * @param {string} nowDate - 当前日期
   * @param {number} currentHour - 当前小时
   * @param {string} currentMemberID - 当前用户ID
   * @returns {Object|null} 用户预约的时间段或null
   */
  findUserAppointmentInNext3Hours(timeList, nowDate, currentHour, currentMemberID) {
    console.log('查找用户3小时内预约 - 当前时间:', currentHour, '当前日期:', nowDate, '用户ID:', currentMemberID);

    // 检查从当前时间开始的接下来3小时内的预约
    for (let i = 0; i <= 3; i++) {
      let targetHour = currentHour + i;
      let targetDate = nowDate;

      // 处理跨天情况
      if (targetHour >= 24) {
        targetHour = targetHour - 24;
        let nextDay = new Date();
        nextDay.setDate(nextDay.getDate() + 1);
        targetDate = this.getFormattedDate(nextDay);
      }

      let hourCode = targetHour < 10 ? '0' + targetHour : targetHour.toString();

      console.log(`检查时间段: ${targetDate} ${hourCode}点`);

      let timeSlot = timeList.find(time =>
        time.code === hourCode &&
        time.openDate === targetDate
      );

      if (timeSlot) {
        console.log('找到时间段:', timeSlot);
        console.log('myAppointmentID:', timeSlot.myAppointmentID);
        console.log('appointmentNumber:', timeSlot.appointmentNumber);

        // 检查是否有当前用户的预约
        if (timeSlot.myAppointmentID) {
          console.log('找到用户预约时间段:', timeSlot);
          return timeSlot;
        }
      }
    }

    console.log('未找到用户在3小时内的预约');
    return null;
  },

  /**
   * 查找下一个可用的时间段
   * @param {Array} timeList - 排序后的时间段列表
   * @param {string} nowDate - 当前日期
   * @param {number} currentHour - 当前小时
   * @returns {Object|null} 可用的时间段或null
   */
  findNextAvailableTimeSlot(timeList, nowDate, currentHour) {
    let currentTime = new Date();
    let currentMinutes = currentTime.getMinutes();

    // 确定搜索的起始时间
    let searchStartHour = currentHour;
    if (currentMinutes >= 30) {
      searchStartHour = currentHour + 1;
    }

    for (let time of timeList) {
      let timeHour = parseInt(time.code);

      // 检查是否是今天且时间已过
      if (time.openDate === nowDate && timeHour < searchStartHour) {
        continue;
      }

      // 检查是否是可预约的时间段（没有预约或者有当前用户的预约）
      if (time.appointmentNumber === 0 || time.myAppointmentID) {
        return time;
      }
    }

    return null;
  },

  /**
   * 设置选中的时间段
   * @param {Object} timeSlot - 选中的时间段
   * @param {string} source - 选择来源（用于日志）
   */
  setSelectedTimeSlot(timeSlot, source) {
    if (!timeSlot) return;

    timeSlot.hasApp = !!timeSlot.myAppointmentID;
    // const hasApp = !!timeSlot.myAppointmentID;
  // 计算是否显示提醒（仅当 hasApp 为 true 时计算）
  // const shouldShowReminder = hasApp ? this.calculateReminderVisibility(timeSlot) : false;
    // timeSlot.hasApp = !!timeSlot.myAppointmentID;
    timeSlot.showTime = timeSlot.beginHour + '-' + timeSlot.endHour;
    // console.log(timeSlot,'timeSlot');

    this.setData({
      hasAppNextHours: timeSlot,
      // showReminder: shouldShowReminder
    });

    console.log(`${source}选中时间段:`, timeSlot);
  },
  calculateReminderVisibility(timeSlot) {
    // 获取当前时间
    const now = new Date();
    
    // 处理跨天情况（预约日期非当天时不显示）
    const today = now.toISOString().split('T')[0];
    if (timeSlot.openDate !== today) return false;
    
    // 构造开始时间和结束时间对象
    const startTime = new Date(`${timeSlot.openDate}T${timeSlot.beginHour}:00`);
    const endTime = new Date(`${timeSlot.openDate}T${timeSlot.endHour}:00`);
    
    // 判断当前时间是否在预约时间段之前
    return now < startTime;
  },

  /**
   * 清空选中的时间段状态
   */
  clearSelectedTimeSlot() {
    this.setData({
      hasAppNextHours: { hasApp: false }
    });

    console.log('清空选中时间段状态');
  },
  getNowCheckTime() {
    const checkedMachine = wx.getStorageSync('checkedMachine')
    
    let params = {
      applicationID: app.applicationID,
      applicationID: app.applicationID,
      companyID: app.companyID,
      memberID: wx.getStorageSync('USER_MEMBERID'),
      pageNumber: 24,
      objectID: checkedMachine.id,
      opendateBeginTime: utils.formatDate(new Date().getTime(), 'yyyy-MM-dd'), // 当前时间
      opendateEndTime: utils.formatDate(new Date().getTime() + 86400000, 'yyyy-MM-dd')//默认10小时
    }

    ports.ModuleAll.getTimeIntervalInstanceList(params).then(resp => {
      let timeList = resp.data.body?.data?.rows || []

      // 使用智能选择功能检查接下来三个小时的预约并自动选择
      this.getHasAppInNextHours(timeList).then(selectedTime => {
        if (selectedTime) {
          // 智能选择找到了合适的时间段，直接使用
          console.log("智能选择找到时间段:", selectedTime);
          wx.setStorageSync('checkedTime', selectedTime);
          this.getTimes();
          return;
        }

        // 如果智能选择没有找到，使用原有的备用逻辑
        this.fallbackTimeSelection(timeList);
      }).catch(error => {
        console.error('智能选择时间段失败:', error);
        // 出错时使用备用逻辑
        this.fallbackTimeSelection(timeList);
      });
    })
  },

  /**
   * 备用时间选择逻辑（保持原有逻辑作为备用）
   * @param {Array} timeList - 时间段列表
   */
  fallbackTimeSelection(timeList) {
    let nowCode = this.getNextOrCurrentHour();
    let nowDate = this.getFormattedDate(new Date());

    // 如果没有找到符合条件的当前或下一个整点时间，尝试寻找后续的时间段
    timeList.sort((a, b) => a.dayOrderSeq - b.dayOrderSeq);

    for (let i = 0; i < timeList.length; i++) {
      const time = timeList[i];
      if (time.openDate >= nowDate && time.code >= nowCode && (time.appointmentNumber == 0 || time.myAppointmentID)) {
        time.showTime = time.beginHour + '-' + time.endHour;
        console.log("备用逻辑找到时间段:", time);
        wx.setStorageSync('checkedTime', time);
        this.getTimes();
        return;
      }
    }

    console.log("未找到任何可用时间段");
  },
  getFormattedDate(now) {
    // 获取年、月、日
    const year = now.getFullYear();
    let month = (now.getMonth() + 1).toString(); // 注意：getMonth() 返回的是0-11
    let day = now.getDate().toString();
    // 确保月份和日期都是两位数（例如，09 而不是 9）
    if (month.length < 2) month = '0' + month;
    if (day.length < 2) day = '0' + day;
    // 拼接成 YYYY-MM-DD 格式
    return `${year}-${month}-${day}`;
  },
  getNextOrCurrentHour() {
    // 创建一个新的 Date 对象表示当前时间
    const now = new Date();
    // 获取当前时间的小时和分钟
    let currentHour = now.getHours();
    let currentMinute = now.getMinutes();
    // 如果当前分钟数大于等于30，则返回下一个整点小时；否则返回当前小时
    if (currentMinute >= 30) {
      // 返回下一个整点小时，处理跨天的情况
      let nextHour = currentHour + 1;
      if (nextHour === 24) {
        nextHour = 0; // 如果是午夜，则重置为0
      }
      if(nextHour<10){
        nextHour = '0'+nextHour
      }
      return nextHour;
    } else {
      if(currentHour<10){
        currentHour = '0'+currentHour
      }
      return currentHour;
    }
  },




  
  // -->>从组件调用的进入方法
  enterThisAppNew(e){
    let selectItem = e.detail
    this.setData({
      selectItem:selectItem
    })
    wx.setStorageSync('selectItem', selectItem)
    let appointmentInStorage = wx.getStorageSync('appointmentList')
    let pa = {
      machineID: selectItem.objectID
    }
    ports.ModuleAll.getMachineDetail(pa).then(respP =>{
      this.setData({
        info: respP.data.body.data
      })
    })
    wx.showLoading({
      title: '正在连接......',
      mask: true,
    })
    this.setData({
      bluetoothDeviceName: this.data.info.name, //传入设备名
      appointmentIndex: selectItem.index,
      appointmentID: selectItem.appointmentID,
      tabIndex: selectItem.workType === 3 ? 1 : 0,
      orderSeq: selectItem.orderSeq,
    })
    ports.ModuleAll.getOneAppointmentDetail({
      appointmentID: selectItem.appointmentID
    }).then(resp => {
      let appointment = resp.data.body.data
      wx.setStorageSync('appointment', appointment)
    })
    ports.ModuleAll.isCanInOneMachineByAppointment({
      sessionID: wx.getStorageSync('USER_SESSIONID'),
      machineID: selectItem.macineDetail.machineID
    }).then(resp => {
      let obj = resp.data.body.data.rows[0]
      let orderSeq = obj.appointmentNumber + 1;
      wx.setStorageSync('orderSeq', orderSeq)
    })
    this.getSensor(selectItem.objectID);
    this.manualOpenBluetoothAdapter(e);
  },
  // // enterThisApp(e){
  // //   let selectItem = e.detail
  // //   this.setData({
  // //     selectItem:selectItem
  // //   })
  // //   wx.setStorageSync('selectItem', selectItem)
  // //   let flag1 = '0'
  // //   if(this.data.gettingFlag){
  // //     wx.showToast({
  // //       title: '正在获取地址',
  // //       icon:'none'
  // //     })
  // //     flag1 = '1'
  // //     return
  // //   }
  // //   console.log('log flag1',flag1);

  // //   let appointmentInStorage = wx.getStorageSync('appointmentList')
  // //   let pa = {
  // //     machineID: appointmentInStorage[0].objectID
  // //   }
  // //   ports.ModuleAll.getMachineDetail(pa).then(respOne => {
  // //     this.setData({
  // //       info: respOne.data.body.data
  // //     })
  // //     console.log(this.data.info);
  // //     wx.showLoading({
  // //       title: '请稍等',
  // //     })
  // //     if(this.data.distance!=9999){
  // //       wx.hideLoading()
  // //       if(this.data.distance>50){
  // //         wx.showModal({
  // //           title: '提示',
  // //           content: '当前距离实训舱'+this.data.distance+'m,距离过远，无法开门,是否重新获取距离',
  // //           complete: (res) => {
  // //             if (res.cancel) {
  // //               console.log('取消');
  // //             }
  // //             if (res.confirm) {
  // //               console.log('确定');
  // //               this.setData({
  // //                 gettingFlag:true
  // //               })
  // //               this.getDistance()
  // //             }
  // //           }
  // //         })
  // //       }else if(this.data.distance == 0){
  // //         wx.showModal({
  // //           title: '提示',
  // //           content: '获取距离有误，请重试',
  // //           complete: (res) => {
  // //             if (res.cancel) {
  // //               console.log('取消')
  // //             }
  // //             if (res.confirm) {
  // //               console.log('确定')
  // //             }
  // //           }
  // //         })
  // //       }else{
  // //         wx.showToast({
  // //           title: '可以进入',
  // //         })
  // //         wx.showLoading({
  // //           title: '正在连接......',
  // //           mask: true,
  // //         })
  // //         this.setData({
  // //           bluetoothDeviceName: this.data.info.name, //传入设备名
  // //           appointmentIndex: appointmentInStorage[0].index,
  // //           appointmentID: appointmentInStorage[0].appointmentID,
  // //           tabIndex: appointmentInStorage[0].workType === 3 ? 1 : 0,
  // //           orderSeq: appointmentInStorage[0].orderSeq,
  // //         })
  // //         ports.ModuleAll.getOneAppointmentDetail({
  // //           appointmentID: appointmentInStorage[0].appointmentID
  // //         }).then(resp => {
  // //           let appointment = resp.data.body.data
  // //           wx.setStorageSync('appointment', appointment)
  // //           let orderSeq = appointment.appointmentNumber + 1;
  // //           wx.setStorageSync('orderSeq', orderSeq)
  // //         })
  // //         this.getSensor(appointmentInStorage[0].objectID);
  // //         this.manualOpenBluetoothAdapter(e);
  // //       }
  // //     }else{
  // //       this.setData({
  // //         gettingFlag:true
  // //       })
  // //       this.getDistance().then(()=>{
  // //         wx.hideLoading()
  // //         if(this.data.distance == 9999){
  // //           wx.showToast({
  // //             title: '距离获取失败',
  // //             icon:'none'
  // //           })
  // //           return
  // //         }
  // //         if(this.data.distance>50){
  // //           wx.showModal({
  // //             title: '提示',
  // //             content: '当前距离实训舱'+this.data.distance+'m,距离过远，无法开门',
  // //             complete: (res) => {
  // //               if (res.cancel) {
  // //                 console.log('取消');
  // //               }
            
  // //               if (res.confirm) {
  // //                 console.log('确定');
  // //               }
  // //             }
  // //           })
  // //         }else if(this.data.distance == 0){
  // //           wx.showModal({
  // //             title: '提示',
  // //             content: '获取距离有误，请重试',
  // //             complete: (res) => {
  // //               if (res.cancel) {
  // //                 console.log('取消')
  // //               }
  // //               if (res.confirm) {
  // //                 console.log('确定')
  // //               }
  // //             }
  // //           })
  // //         }else{
  // //           wx.showToast({
  // //             title: '可以进入',
  // //           })
  // //           wx.showLoading({
  // //             title: '正在连接......',
  // //             mask: true,
  // //           })
  // //           this.setData({
  // //             bluetoothDeviceName: this.data.info.name, //传入设备名
  // //             appointmentIndex: appointmentInStorage[0].index,
  // //             appointmentID: appointmentInStorage[0].appointmentID,
  // //             tabIndex: appointmentInStorage[0].workType === 3 ? 1 : 0,
  // //             orderSeq: appointmentInStorage[0].orderSeq,
  // //           })
  // //           ports.ModuleAll.getOneAppointmentDetail({
  // //             appointmentID: appointmentInStorage[0].appointmentID
  // //           }).then(resp => {
  // //             let appointment = resp.data.body.data
  // //             wx.setStorageSync('appointment', appointment)
  // //             let orderSeq = appointment.appointmentNumber + 1;
  // //             wx.setStorageSync('orderSeq', orderSeq)
  // //           })
  // //           this.getSensor(appointmentInStorage[0].objectID);
  // //           this.manualOpenBluetoothAdapter(e);
  // //         }
  // //       })
  // //     }
  // //   })
  // // },

  // //手动搜索设备
  // //第一步
  // manualOpenBluetoothAdapter(e) {
  //   wx.setStorageSync('blueDeviceID', '')
  //   this.closeBluetoothAdapter(e)
  //   this.openBluetoothAdapter(e)
  //   // clearInterval(date)
  // },
  // //移除蓝牙
  // closeBluetoothAdapter(e) {
  //   wx.closeBluetoothAdapter()
  //   this.setData({
  //     _discoveryStarted: false
  //   })
  // },
  // //开始扫描
  // //第二步
  // openBluetoothAdapter(e) {
  //   var that = this
  //   //初始化蓝牙模块所有接口只能初始化后才能调佣
  //   wx.openBluetoothAdapter({
  //     //蓝牙初始化成功
  //     success: (res) => {
  //       console.log('openBluetoothAdapter success', res)
  //       this.startBluetoothDevicesDiscovery() //开始搜寻附近的蓝牙外围设备
  //     },
  //     //蓝牙初始化失败
  //     fail: (res) => {
  //       //手机蓝牙未打开或不支持使用蓝牙返回10001
  //       if (res.errCode === 10001) {
  //         //监听用户的蓝牙是否打开（监听蓝牙的状态的改变）也可以调用蓝牙模块的所有API。开发者在开发中应该考虑兼容用户在使用小程序过程中打开/关闭蓝牙开关的情况，并给出必要的提示
  //         // wx.showLoading({
  //         //   title: '请打开蓝牙',
  //         // })
  //         wx.hideLoading()
  //         wx.showToast({
  //           title: '请打开蓝牙',
  //           icon: 'none',
  //         })
  //         wx.onBluetoothAdapterStateChange(function (res) {
  //           wx.hideLoading()
  //           if (res.available) {
  //             // wx.showToast({
  //             //   title: '搜索设备中...',
  //             //   icon: 'none'
  //             // }, 1000)
  //             that.startBluetoothDevicesDiscovery(e)
  //           }
  //         })
  //       } else {
  //         wx.hideLoading()
  //         wx.showToast({
  //           title: '连接失败，请重试',
  //           icon: 'none',
  //         })
  //       }
  //     }
  //   })
  // },
  // //扫描并发现外围设备
  // //第三步
  startBluetoothDevicesDiscovery(e) {
    var that = this
    if (this.data._discoveryStarted) return
    this.setData({
      _discoveryStarted: true
    })
    //调用API扫描发现外围设备
    wx.startBluetoothDevicesDiscovery({
      // services:["6E400001-B5A3-F393-E0A9-E50E24DCCA9E"],
      // allowDuplicatesKey: true,
      interval: 1000,
      success: (res) => {
        console.log('startBluetoothDevicesDiscovery success', res)
        // 设置定时器，30秒后停止查找
        const timer=setTimeout(()=> {
          if (that.data.searchFlag) {
            wx.hideLoading();
            wx.showToast({
              title: '未搜索到设备',
              icon: 'none',
            })
            wx.stopBluetoothDevicesDiscovery({
              success:  (res)=> {
                console.log('停止查找蓝牙设备', res)
              }
            })
          }
        }, 10000)
        //监听蓝牙发现的设备
        this.onBluetoothDeviceFound(e)
      },
    })
  },
  // //监听蓝牙搜索设备
  // //第四步
  // // onBluetoothDeviceFound(e) {
  // //   var deviceData = []
  // //   var that = this
  // //   //返回扫描到的设备
  // //   wx.onBluetoothDeviceFound((res) => {
  // //     console.log('返回扫描到的设备', res);
  // //     res.devices.forEach(device => {
  // //       if ((!device.name && !device.localName) || !device.connectable) {
  // //         // wx.showToast({
  // //         //   title: '未搜索到设备',
  // //         //   icon: 'none',
  // //         // }) 
  // //         return
  // //       }
  // //       if (device.localName === this.data.info.iccid || device.name === this.data.info.iccid) {
  // //         that.createBLEConnection(device.deviceId, device.name,e)
  // //         that.data.searchFlag = false
  // //         return
  // //       }
  // //     })
  // //   })
  // // },
  // //点击事件创建设备连接
  // createBLEConnection(id, name,e) {
  //   let deviceId = id
  //   wx.setStorageSync('blueDeviceID', id)
  //   wx.setStorageSync('blueDeviceName', name)
  //   //调用API连接设备
  //   wx.createBLEConnection({
  //     //连接的设备id
  //     deviceId,
  //     //连接成功
  //     success: (res) => {
  //       console.log('连接成功', res);
  //       //获得service
  //       wx.getBLEDeviceServices({
  //         deviceId, // 搜索到设备的 deviceId
  //         success: (res) => {
  //           let serviceIDs = []
  //           for (let i = 0; i < res.services.length; i++) {
  //             if (res.services[i].isPrimary) {
  //               serviceIDs.push(res.services[i].uuid)
  //               // 可根据具体业务需要，选择一个主服务进行通信
  //             }
  //             //保存serviceID们到本地
  //             wx.setStorageSync('blueServiceIDs', serviceIDs)
  //           }
  //         },
  //         fail: (res) => {
  //           console.log(res, 999);
  //         }
  //       })
  //       //读写特征值
  //       wx.getBLEDeviceCharacteristics({
  //         deviceId, // 搜索到设备的 deviceId
  //         serviceId: wx.getStorageSync('blueServiceIDs'), // 上一步中找到的某个服务
  //         success: (res) => {
  //           for (let i = 0; i < res.characteristics.length; i++) {
  //             let item = res.characteristics[i]
  //             if (item.properties.write) {
  //               let buffer = new ArrayBuffer(1)
  //               let dataView = new DataView(buffer)
  //               dataView.setUint8(0, 0)
  //               wx.writeBLECharacteristicValue({
  //                 deviceId,
  //                 serviceId,
  //                 characteristicId: item.uuid,
  //                 value: buffer,
  //               })
  //             }
  //             if (item.properties.read) { // 该特征值可读
  //               wx.readBLECharacteristicValue({
  //                 deviceId,
  //                 serviceId,
  //                 characteristicId: item.uuid,
  //               })
  //             }
  //             if (item.properties.notify || item.properties.indicate) {
  //               wx.notifyBLECharacteristicValueChange({
  //                 deviceId,
  //                 serviceId,
  //                 characteristicId: item.uuid,
  //                 state: true,
  //               })
  //             }
  //           }
  //         },
  //         fail: (res) => {
  //           console.log(res, 999);
  //         }
  //       })
  //       wx.stopBluetoothDevicesDiscovery({
  //         success: function (res) {
  //           console.log('停止查找蓝牙设备', res)
  //         }
  //       })
  //       // let str1 = 'showList[' + this.data.appointmentIndex + '].disable'
  //       // this.setData({
  //       //   [str1]: true
  //       // })
  //       // let str2 = 'showList[' + this.data.appointmentIndex + '].btnShow'
  //       // this.setData({
  //       //   [str2]: true
  //       // })
  //       this.door(e)
  //     },
  //     fail: (err) => {
  //       wx.hideLoading();
  //       wx.showToast({
  //         title: '连接失败，请重试',
  //         icon: 'none'
  //       })
  //       console.log(err, '连接失败');
  //     }
  //   })
  // },
  // door(e) {
  //   wx.hideLoading();
  //   this.action('door', 'unlock',e)
  // },
  // //按下按钮对应的操作
  // action(sensor, act,e) {
  //   let that = this
  //   //调用写特征值（开门命令
  //   this.getCommand(sensor, act).then(jsonData => {
  //     if (jsonData == 'fail')
  //       return alert('获取通讯key失败')
  //     wx.closeBLEConnection({
  //       deviceId: wx.getStorageSync('blueDeviceID'), // 蓝牙设备ID
  //       success: (res) => {
  //         wx.removeStorageSync('blueDeviceID')
  //         console.log('断开蓝牙连接成功', res);
  //       },
  //       fail: (err) => {
  //         console.log('断开蓝牙连接失败', err);
  //       }
  //     });
  //     // if (!wx.getStorageSync('blueDeviceID'))
  //     //   return alert('请连接蓝牙设备') 
  //     let params = {
  //       sessionID: wx.getStorageSync('USER_SESSIONID'),
  //       machineID: that.data.id,
  //       operateType: 13,
  //       operateName: '蓝牙连接',
  //       lastScanMac: that.data.info.macAddress,
  //     }
  //     ports.ModuleAll.operateOneMachine(params).then(res => {
  //       if (res.data.header.code === 0) {
  //         console.log("开始一次机器使用记录")
  //       }
  //     })
  //     wx.showToast({
  //       title: '请在听到门锁啪嗒一声后，再推门进入',
  //       icon: 'none',
  //       duration: 1000
  //     })
  //     let selectItem = this.data.selectItem
  //     let reqURL = `getSectionDetail?sectionID=${selectItem.resourcesObjectID}&sessionID=${wx.getStorageSync('USER_SESSIONID')}`
  //     ports.ModuleAll.getSectionDetail(reqURL).then(respo => {
  //       let sectionDetail =respo.data.body.apiSectionDetailDto
  //       setTimeout(() => {
  //         wx.setStorageSync('enterFlag', true)
  //         this.setData({
  //           enterFlag: true
  //         })
  //         console.log(this.data.enterFlag, this.data.ornot)
  //         let machineID = that.data.selectItem.objectID
  //         let appointmentID = that.data.selectItem.appointmentID;
  //         let trainBusinessID = that.data.info.trainBusinessID;
  //         let shopID = that.data.info.shopID;
  //         let mapAddress = that.data.info.address;
  //         let mapX = that.data.info.mapX;
  //         let mapY = that.data.info.mapY;
  //         let tabIndex = wx.getStorageSync('tabIndex');
  //         let orderSeq = wx.getStorageSync('orderSeq');
  //         let isVisit = false
          
  //         let url1 = `/packageA/pages/learnSteps/learnSteps?trainMinutes=${sectionDetail.trainMinutes}&sectionId=${sectionDetail.sectionID}&name=${sectionDetail.name}&shopID=${shopID}&appointmentID=${appointmentID}&mapAddress=${mapAddress}&mapX=${mapX}&mapY=${mapY}&machineID=${machineID}&orderSeq=${orderSeq}`
  //         let url2 = `/packageA/pages/trainSteps/trainSteps?sectionID=${sectionDetail.sectionID}&shopID=${shopID}&appointmentID=${appointmentID}&mapAddress=${mapAddress}&mapX=${mapX}&mapY=${mapY}&machineID=${machineID}&orderSeq=${orderSeq}`
  //         let url3 = `/packageA/pages/examSteps/examSteps?sectionID=${sectionDetail.sectionID}&shopID=${shopID}&appointmentID=${appointmentID}&mapAddress=${mapAddress}&mapX=${mapX}&mapY=${mapY}&machineID=${machineID}&orderSeq=${orderSeq}`
  //         if(selectItem.workType==3){
  //           this.toNewIndex1()
  //         }else{
  //           let url = selectItem.workType==2?url1:selectItem.workType==3?url2:url3
  //           wx.setStorageSync('urlForEnterFlag', url)
  //           wx.navigateTo({
  //             url:url
  //           })
  //         }
  //       }, 1000)
  //       //调用写特征值（开门命令）
  //       this.writeBLECharacteristicValue(jsonData)
  //     })
  //   })
  // },
  //写BEI特征值
  // writeBLECharacteristicValue(jsonData) {
  //   console.log(jsonData, "jsondata")
  //   // 向蓝牙设备发送数据
  //   //let uuid = wx.getStorageSync('uuid');
  //   let that = this
  //   const serviceId = '0000FFF0-0000-1000-8000-00805F9B34FB' //写死 每个蓝牙都一样
  //   const characteristicId = "0000FFF2-0000-1000-8000-00805F9B34FB" //写死 每个蓝牙都一样
  //   let asciiArray = jsonToAscii(jsonData)
  //   let buffer = new ArrayBuffer(asciiArray.length);
  //   let dataView = new DataView(buffer)
  //   for (let i = 0; i < asciiArray.length; i++) {
  //     dataView.setUint8(i, asciiArray[i]);
  //   }
  //   wx.writeBLECharacteristicValue({
  //     deviceId: wx.getStorageSync('blueDeviceID'),
  //     serviceId,
  //     characteristicId,
  //     value: buffer,
  //     success(res) {
  //       console.log("消息发送成功")
  //     },
  //     fail(e) {
  //       console.log("发送消息失败: " + e.errMsg, );
  //     },
  //   })
  //   wx.onBLECharacteristicValueChange((characteristic) => {
  //     console.log(characteristic, "characteristic")
  //   })
  // },
  //获取蓝牙命令Json
  // getCommand(sensorCode, commandValue) {
  //   return new Promise(resolve => {
  //     let appointmentList = wx.getStorageSync('appointmentList')
  //     //先获取实训仓的 所有传感器列表
  //     ports.ModuleMachine.getSensorList({
  //       machineID: appointmentList[0].objectID,
  //       pageNumber: 999
  //     }).then(res => {
  //       if (res.data.header.code == 0) {
  //         this.setData({
  //           sensorList:res?.data?.body?.data?.rows || []
  //         })
  //         let sensorID = ''
  //         //筛选出 传入名称 的传感器
  //         res.data.body.data.rows.forEach(row => {
  //           if (row.code == sensorCode)
  //             sensorID = row.sensorID
  //         });
  //         //获取该传感器的操作命令，先 写死 unlock 开门
  //         ports.ModuleMachine.getOneMachineCommandForSanOne({
  //           machineID: appointmentList[0].objectID,
  //           sensorID,
  //           commandValue,
  //         }).then(res2 => {
  //           if (res2.data.header.code == 0) {
  //             let c = "SE" + wx.getStorageSync('orderSeq') + "_computer"
  //             console.log("c",c);
  //             console.log("sensorList"+this.data.sensorList);
  //             let o=this.data.sensorList.find(sensor => sensor.code === c)
  //             console.log("company",o);
  //             //请先关门电脑自动打开
  //             this.sleep(10000).then(() => {
  //               this.querySensorStatus(sensorID,o)
  //             })
  //               resolve(res2.data.body.data)       
  //           } else {
  //             return resolve('fail')
  //           }
  //         })
  //       } else return resolve('fail')
  //     })
  //   })
  // },
  // sleep(ms) {
  //   return new Promise(resolve => setTimeout(resolve, ms));
  // },
  // querySensorStatus(sensorID,o,attempt = 1, maxAttempts = 99){
  //   console.log("查询门是否关闭");
  //   let appointmentList = wx.getStorageSync('appointmentList')
  //   //睡眠
  //   ports.ModuleMachine.getOneSensorDetail({
  //     sensorID,
  //   }).then(res9 => {
  //     console.log("openStatus"+res9.data.body.data.openStatus);
  //     if (res9.data.header.code == 0&&res9.data.body.data.openStatus==0) {
  //       ports.ModuleMachine.getOneMachineCommandForSanOne({
  //         machineID: appointmentList[0].objectID,
  //         sensorID: o.sensorID,
  //         commandValue: "on",
  //       }).then(res3 => {
  //         if (res3.data.header.code == 0) {
  //           console.log("开启电脑")
  //         } else {
  //           return resolve('fail')
  //         }
  //       })
  //     }else if (attempt < maxAttempts) {
  //       wx.showToast({
  //         title: '请关闭门锁',
  //         icon: 'none',
  //         duration: 1000
  //       })
  //       setTimeout(() => {
  //         this.querySensorStatus(sensorID,o, attempt + 1, maxAttempts);
  //       }, 2000);
  //     }else {
  //       // 达到最大尝试次数，处理失败情况
  //       console.error('达到最大尝试次数，无法开启电脑');
  //     }
  //   })
  // },


  
  // getSensor(machineID) {
  //   var that = this;
  //   ports.ModuleMachine.getSensorList({
  //     machineID: this.data.id,
  //     pageNumber: 99,
  //   }).then(res => {
  //     if (res.data.header.code == 0) {
  //       that.setData({
  //         sensorList: res.data.body.data.rows
  //       })

  //     } else return 
  //   })
  // },
  async getLocationNew(){
    let that = this
    this.setData({
      getLocationStatus:false
    })
    return new Promise((resolve,reject)=>{
      wx.getLocation({
        type: 'gcj02', // 默认为 wgs84 返回 gps 坐标，gcj02 返回可用于 wx.openLocation 的坐标
        altitude: true, // true 会返回高度信息
        isHighAccuracy: true, //开启高精度定位
        highAccuracyExpireTime: 3100, //高精度定位超时时间(ms)
        success: (res) => {
          console.log('success');
          // if(!this.data.showJuli) wx.showToast({
          //   title: '获取位置成功',
          // })
         
          // this.setData({
          //   myX: res.latitude
          // })
          // this.setData({
          //   myY: res.longitude
          // })
          this.setData({
            myX: res.latitude,
            myY: res.longitude,
            getLocationStatus:true,
            showJuli:true
          },()=>{
            resolve()
          })
          console.log("this.getLocationStatus",this.getLocationStatus);
        },
        fail:(err) =>{
          console.log(err,'获取位置失败')
          if (err.errMsg.indexOf('auth deny') > -1 || err.errCode === 2) {
            // 用户拒绝授权
            wx.showModal({
              title: '提示',
              content: '您已拒绝授权获取位置信息，请前往设置页面开启权限。',
              showCancel: true,
              cancelText: '取消',
              confirmText: '去设置',
              success(modalRes) {
                if (modalRes.confirm) {
                  // 打开设置页面让用户手动开启权限
                  wx.openSetting({
                    success(settingData) {
                      if (settingData.authSetting['scope.userLocation']) {
                        // 用户重新授权后再次尝试获取位置
                       console.log("设置了地址权限");
                      } else {
                        console.log('用户未重新授权');
                        // 提供默认行为或者提示用户无法继续某些功能
                      }
                    }
                  });
                }
                that.setData({
                  getLocationStatus:true,
                  showJuli:false
                },()=>{
                  resolve()
                })
              }
            });
          }else{
            console.log("获取距离失败");
            // wx.showToast({
            //   title: '定位失败',
            //   icon:"error"
            // })
            that.setData({
              getLocationStatus:true,
              showJuli:false
            },()=>{
              resolve()
            })
          }
        },
      })
    })
  },
  formatDistance(distanceInMeters) {
    if (distanceInMeters >= 1000) {
      // 将距离从米转换为千米，并保留两位小数
      const distanceInKilometers = (distanceInMeters / 1000).toFixed(2);
      return `${distanceInKilometers} 千米`;
    } else {
      return `${distanceInMeters} 米`;
    }
  },
  calculateDistanceNew(lat1, lng1, lat2, lng2) {
    var that = this;
    let rad1 = lat1 * Math.PI / 180.0;
    let rad2 = lat2 * Math.PI / 180.0;
    let a = rad1 - rad2;
    let b = lng1 * Math.PI / 180.0 - lng2 * Math.PI / 180.0;
    let s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2) + Math.cos(rad1) * Math.cos(
      rad2) * Math.pow(
      Math.sin(b / 2), 2)));
    s = s * 6378.137;
    s = Math.round(s * 10000) / 10000;
    s = s.toString();
    s = s.substring(0, s.indexOf('.') + 2);
    console.log('距离：', s);
    return s; //返回距离
  },
  async getDistance() {
    if(this.data.gettingFlag){
      return
    }
    this.setData({
      gettingFlag:true
    })
    function calculateDistance(lat1, lng1, lat2, lng2) {
      var that = this;
      let rad1 = lat1 * Math.PI / 180.0;
      let rad2 = lat2 * Math.PI / 180.0;
      let a = rad1 - rad2;
      let b = lng1 * Math.PI / 180.0 - lng2 * Math.PI / 180.0;
      let s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2) + Math.cos(rad1) * Math.cos(
        rad2) * Math.pow(
        Math.sin(b / 2), 2)));
      s = s * 6378.137;
      s = Math.round(s * 10000) / 10000;
      s = s.toString();
      s = s.substring(0, s.indexOf('.') + 2);
      console.log('距离：', s);
      return s; //返回距离
    }
    wx.getLocation({
      type: 'gcj02', // 默认为 wgs84 返回 gps 坐标，gcj02 返回可用于 wx.openLocation 的坐标
      altitude: true, // true 会返回高度信息
      isHighAccuracy: true, //开启高精度定位
      highAccuracyExpireTime: 3100, //高精度定位超时时间(ms)
      success: (res) => {
        wx.showToast({
          title: '获取位置成功',
        })
        let distance = longitude.calculateDistance(this.data.info.mapY, this.data.info.mapX, res.latitude, res.longitude)
        console.log(distance, "距离")
        let distanceStr = distance;
        if(distanceStr && distanceStr != 9999 && !isNaN(Number(distanceStr)) ){
          distanceStr = this.formatDistance(Number(distanceStr))
        }
        this.setData({
          distance:distance == 9999 ? 9998 :distance,
          distanceStr
        })
        this.setData({
          myX: res.latitude
        })
        this.setData({
          myY: res.longitude
        })
      },
      fail:(err) =>{
        console.log(err,'获取位置失败')
        if (err.errMsg.indexOf('auth deny') > -1 || err.errCode === 2) {
          // 用户拒绝授权
          wx.showModal({
            title: '提示',
            content: '您已拒绝授权获取位置信息，请前往设置页面开启权限。',
            showCancel: true,
            cancelText: '取消',
            confirmText: '去设置',
            success(modalRes) {
              if (modalRes.confirm) {
                // 打开设置页面让用户手动开启权限
                wx.openSetting({
                  success(settingData) {
                    if (settingData.authSetting['scope.userLocation']) {
                      // 用户重新授权后再次尝试获取位置
                     console.log("设置了地址权限");
                    } else {
                      console.log('用户未重新授权');
                      // 提供默认行为或者提示用户无法继续某些功能
                    }
                  }
                });
              }
            }
          });
        }else{
          wx.showModal({
            title: '提示',
            content: '获取位置失败，请查看手机定位权限是否开启，是否重新获取',
            complete: (res) => {
              if (res.cancel) {
                console.log('取消');
              }
          
              if (res.confirm) {
                // this.enterThisApp()
              }
            }
          })
        }
      },
      complete:()=>{
        wx.hideLoading()
        this.setData({
          gettingFlag:false
        })
      }
    })

  },
  // <<--
    /**
   * 生命周期函数--监听页面显示
   */
  async onShow(){
    // 确保页面每次显示的时候重置分享状态
    wx.showLoading({ title: '加载中' });
    this.setData({ isCustomShare: false });
  
    try {
      // 按顺序执行所有异步操作
      await this.checkHasCurLearning();
      await this.checkHasCurLearningNew();
      if (this.data.mycurLearningBoxNeed !== true) {
        this.setData({
          mycurLearningBoxNeed: true
        });
      }
      
      // 等待 setData 完成
      await new Promise(resolve => setTimeout(resolve, 50));

      if (this.data.mycurLearningBoxNeed == true) {
        await this.getPoint();
        await this.getNowCheckTime();
        // 如果 checkLogin 是异步的，需要加 await
        // await this.checkLogin(); 
        await this.changeMachine();
        await this.getWorkTypes();
        await this.init1();
        await this.childInit();
      }
    } catch (error) {
      console.error("接口执行失败:", error);
      wx.showToast({ title: '加载失败', icon: 'none' });
    } finally {
      wx.hideLoading(); // 确保无论成功失败都隐藏loading
    }
    // 展示上次选的预约或者存储默认预约内容
  },
  
  getWorkTypes(){
    const _cur_workType = wx.setStorageSync('checkedWorkType')?.workType;
    console.log('getWorkTypes',_cur_workType)
    if(!_cur_workType){
      const {workType, name} = this.data.checkLearnType.find(item=>item.checked) || {};
      wx.setStorageSync('checkedWorkType', { workType, workTypeName:name })
      this.setData({ workType, workTypeName:name })
    }else{
      const checkLearnType =  this.data.checkLearnType.map(item=>{
        return {
          ...item,
          checked: item.workType == _cur_workType
        }
      })
      this.setData({checkLearnType,workType:_cur_workType.workType, workTypeName:_cur_workType.name})
    }
  },
  changeMachine(){
    let checkedMachine = wx.getStorageSync('checkedMachine')
    if(checkedMachine){
      this.setData({
        checkedMachine:checkedMachine
      })
      let distanceStr = this.data.checkedMachine.distance;
      if(distanceStr && distanceStr != 9999 && !isNaN(Number(distanceStr)) ){
        distanceStr = this.formatDistance(Number(distanceStr))
      }
      if(this.data.checkedMachine.distance){
        this.setData({
          distance:this.data.checkedMachine.distance,
          distanceStr
        })
      }
    } else {
      this.getMachineList()
    }
    let that = this
    let p = {
      machineID: that.data.checkedMachine.id,
    }
    ports.ModuleAll.getMachineDetail(p).then(resOne => {
      let info = resOne.data.body.data
      console.log('23123info:',info);
      this.setData({
        info: resOne.data.body.data
      })
      if (wx.getStorageSync('USER_SESSIONID')&&this.data.distance==9999) {
        this.getDistance();
      }
    })
  },
  checkLogin(){
    const that =this
    if (!wx.getStorageSync('USER_SESSIONID')) {
      that.showLogin1()
    }
    // else{
    //   that.notShowLogin1()
    //   let param = {
    //     sessionID : wx.getStorageSync('USER_SESSIONID')
    //   }
    //   ports.ModuleAll.checkSessionIsOK(param).then(resp=>{
    //     if(resp.data.body.isTimeOut == true){
    //       utils.LOGIN(1)
    //     }
    //   })
    // }
  },
  showLogin1(){
    this.setData({
      isLoginVisible : true
    })
  },
  notShowLogin1(){
    this.setData({
      isLoginVisible : false
    })
  },
  // 获取积分
  getPoint(){
    let that = this
    var req = {};
    ports.ModuleAll.getMyStatisticsData(req).then(res => {
      if (res.data.header.code == 0) {
        var data = res.data.body.memberStatisticsDto;
        that.setData({
          memberStatics: data
        })
      }
    })
  },
  /**
   * 生命周期函数--监听页面加载
   */
  async onLoad(options) {
    this.init();
    wx.setNavigationBarColor({
      frontColor: '#000000',//黑
      backgroundColor: '#000000',
    })
    if (wx.getStorageSync('USER_SESSIONID')) {
      // 如果任何一个存在，则直接退出，不执行登录与获取 openid 等操作
      if (wx.getStorageSync('USER_OPENID') || wx.getStorageSync('USER_UNIONID')) {
        // 任一存在，跳过如下代码
      } else {
        // 两者都不存在，执行获取操作
        let res = await ports.ModuleAll.loginWX();
        let resOne = await ports.ModuleAll.getWeiXinAppOpenId({
          publicNo: app.publicNo,
          js_code: res.code,
        })
        var openID = resOne.data.body.openID
        var unionID = resOne.data.body.unionid
        wx.setStorageSync('USER_OPENID', openID)
        wx.setStorageSync('USER_UNIONID', unionID)
      }
    }
    // this.checkLogin()

    this.getfocusList()
    this.setData({
      id: options.id,
      myMemberID: wx.getStorageSync('USER_MEMBERID'),
    })
    //scene是扫码进来的
    if (options.scene) {
      this.setData({
        backHome: true,
        id: options.scene,
      })
      let pa = {
        machineID: options.scene
      }
      console.log("options.scene",options.scene);
      ports.ModuleAll.getMachineDetail(pa).then(resp => {
        this.setData({
          info: resp.data.body.data
        })
        let checkedMachine = resp.data.body.data
        checkedMachine.id=checkedMachine.machineID
        checkedMachine.machineName=checkedMachine.name
        checkedMachine.address=checkedMachine.address
        checkedMachine.code=checkedMachine.code;
        checkedMachine.bizType=checkedMachine.bizType;
        wx.setStorageSync('checkedMachine', checkedMachine)
        console.log("checkedMachine",checkedMachine);
        this.setData({
          checkedMachine:checkedMachine
        })
        this.getDistance()
      })
      
      let params = {
        machineID: this.data.checkedMachine.id,
        operateType: 6,
        operateName: "扫描二维码"
      }
      ports.ModuleAll.operateOneMachine(params).then(res => {
        if (res.data.header.code === 0) {
          console.log("开始一次机器使用记录")
        }
      })
    }
    if (!wx.getStorageSync('deviceID')) this.getDevice();
    if (wx.getStorageSync('enterFlag') && wx.getStorageSync('openTime')) {
      let nowTimeTimestamp = new Date().getTime();
      let openTime = wx.getStorageSync('openTime');
      if (nowTimeTimestamp > (openTime + 3600000)) {
        wx.removeStorageSync('enterFlag');
        wx.removeStorageSync('openTime');
        this.setData({
          enterFlag: false
        })
      }
    }
    //分享推荐会员
    if(options.scene) wx.setStorageSync('recommendCode',options.scene)
    if(options.memberID) wx.setStorageSync('recommendMemberID',options.memberID)
    
    utils.getIP();
    
  },
  getMemberGroupList() {
    let params = {
      applicationID: app.applicationID,
      sessionID: wx.getStorageSync('USER_SESSIONID'),
      headMemberID: wx.getStorageSync('USER_MEMBERID'),
      pageNumber: 99,
      sortTypeTime: 2
    }
    params.headMemberID = wx.getStorageSync('USER_MEMBERID')
    ports.ModuleAll.getMemberGroupList(params).then(res => {
      let memberGroupList = res.data.body.data.rows || []

      Promise.all(
        memberGroupList.map((item, index) => {
          return new Promise((resolve, reject) => {
            // 获取课程列表
            ports.ModuleAll.getMemberGroupJoinList({
              sessionID: wx.getStorageSync('USER_SESSIONID'),
              memberGroupID: item.memberGroupID,
              // memberGroupID: '29827e7aedb043f5a67fd9a701d9bc1a',
              pageNumber: 99,
              sortTypeTime: 2
            }).then(res => {
              if (res.data.header.code == 0) {
                let list = res.data.body.data.rows || []
                memberGroupList[index].sonList = list
                resolve(memberGroupList[index].sonList)
              }
            })
          })
        })).then(newRes => {
        this.setData({
          memberGroupList,
          chooseStatus:true
        })
      })

    })
  },
  checkMyGroup(e){
    let memberGroup = e.currentTarget.dataset.item;
    if(memberGroup.sonList.length>3){
      wx.showModal({
        title: '提示',
        content: '您选择的群组人数超过实训舱规定人数，请选择其他群组',
        complete: (res) => {
          if (res.cancel) {
            
          }
      
          if (res.confirm) {
            
          }
        }
      })
      return
    }else if(memberGroup.sonList.length==1){
      wx.showModal({
        title: '提示',
        content: '您选择的群组人数只有您自己，请选择其他群组',
        complete: (res) => {
          if (res.cancel) {
            
          }
      
          if (res.confirm) {
            
          }
        }
      })
      return
    }
    console.log(memberGroup);
    // 成员数量大于1小于4才可以预约
    let sonList = memberGroup.sonList
    this.setData({
      friend1:{},
      friend2:{}
    })
    sonList.forEach(member=>{
      console.log(member);
      if(member.memberID!=wx.getStorageSync('USER_MEMBERID')){
        console.log(member.memberName);
        let friend = {}
        friend.toMemberID=member.memberID
        friend.toMemberName=member.memberName
        if(Object.keys(this.data.friend1).length == 0){
          this.setData({
            friend1:friend
          })
        }else{
          this.setData({
            friend2:friend
          })
        }
      }
    })
    this.setData({
      friendShowModal:false
    })
  },

  addFriend(){
    this.setData({
      showModalForAdd: true
    })
  },
  phoneValueclick(e) {
    // console.log(e.detail.value)
    this.setData({
      phoneValue: e.detail.value
    })
  },
  // 添加好友
  addSure() {
    // sessionID: wx.getStorageSync('USER_SESSIONID'),
    ports.ModuleAll.createOneFriend({
      phone: this.data.inquirePhone,
      toMemberID: this.data.inquireMemberID,
      name: this.data.inquireName,
    }).then(res => {
      if (res.data.header.code == 0) {
        wx.showModal({
          title:'申请成功!',
          content: '该好友可在小程序中我->好友列表->新的朋友中通过好友请求',
          showCancel: false,
          confirmText: '知道了'
        });
        this.hideModal();
        this.clearDialogValues()
      } else {
        // wx.showToast({
        //   title: res.data.header.msg || '添加失败',
        //   icon: 'error'
        // })
        let msg = res.data.header.msg;
        wx.showModal({
          title: msg || '添加失败',
          content: msg && msg.includes('已添加') ? '可提醒好友在小程序内通过申请' : '请稍后重试',
          showCancel: false,
          confirmText: '知道了'
        });

      }
    })
  },
  clearDialogValues() {
    this.setData({
      phoneValue: '',
      inquireName: '',
      inquirePhone: '',
      inquireAvatarURL: '',
      inquireAactive: false,
      inquireMemberID: '',
    })
  },
  inquire_btn() {
    let req1 = {}
    req1.tel = this.data.phoneValue
    if (req1.tel.trim().length == 11) {
      console.log(111);
      let data = {
        phone: req1.tel,
        applicationID: app.applicationID,
      }
      ports.ModuleAll.searchMemberByPhone(data).then(res => {
        if (res.data.header.code == 0) {
          this.setData({
            inquireName: res.data.body.name,
            inquirePhone: res.data.body.phone,
            inquireAvatarURL: res.data.body.avatarURL,
            inquireAactive: true,

            inquireMemberID: res.data.body.memberID,
            showModalForAdd: true
          })
        } else {
          wx.showToast({
            title: '手机号码未找到',
            icon: 'error'
          })
        }
      })
    } else {
      wx.showToast({
        title: '请输入合法号码',
        icon: 'error'
      })
      this.hideModal()
    }
  },
  hideModal() {
    this.setData({
      showModalForAdd: false
    })
  },
  getfocusList(){
    let params = {
      focusID: this.data.focusID,
      sortTypeOrder: 1
    }
    ports.ModuleAll.getFocusPictureList(params).then(res=>{
      this.setData({
        focusList:res.data.body.data.rows || []
      })
    })
  },
  //进行赠送
  async createOneMemberTrain(){
    if (!wx.getStorageSync('deviceID')) this.getDevice();
    if (!wx.getStorageSync('USER_MEMBER')) await utils.LOGIN(0);
    let params = {
      sessionID: wx.getStorageSync('USER_SESSIONID'),
      siteID: app.siteID,
      shopID: app.shopID,
      buyType: 1, //类型为培训业务
      trainBusinessID: this.data.trainBusinessID,
      joinMemberID: wx.getStorageSync('USER_MEMBERID'),
    }
    if (this.data.trainCompanyID!=="undefined") {
      params.trainCompanyID = this.data.trainCompanyID
    }
  },
  // 确认预约页
  toAppointmentConfirm(){
    const {id,machineName,machineDistance,address,code,bizType} = this.data.checkedMachine;
    const data = {
      city:this.data.city,
      checkedMachine:{id,machineName,machineDistance,address,code,bizType},
      nextBeginTime: this.data.nextBeginTime,
      nextEndTime: this.data.nextEndTime,
      distance:this.data.distance,
      isLocating:this.data.isLocating,
      friend1:this.data.friend1,
      friend2:this.data.friend2,
    }
    let url = buildUrl_n('/packageA/pages/appointmentConfirm/appointmentConfirm',data)
    console.log(data,'url');
    wx.navigateTo({
        url: url,
      })
  },
  toNewIndex1(openDoorSuccess){

    if((this.data.selectType==0)||(this.data.btn1==3&&this.data.btn2==3&&this.data.btn3==3&&this.data.btn4==3)){
      wx.showModal({
        title: '提示',
        content: '当前时段预约已被实训人员使用，无法继续预约，请选择其他时段',
        complete: (res) => {
          if (res.cancel) {
            
          }
      
          if (res.confirm) {
            
          }
        }
      })
      return
    }
    let workType = this.data.checkLearnType.find(item=>item.checked)?.workType;
    let url =  `/packageA/pages/newIndex1/newIndex1?nextBeginTime=${this.data.nextBeginTime}&nextEndTime=${this.data.nextEndTime}&orderSeq=${this.data.selectType}&btn1=${this.data.btn1}&btn2=${this.data.btn2}&btn3=${this.data.btn3}&btn4=${this.data.btn4}&=workType=${workType}&openDoorSuccess=${!!openDoorSuccess}`
    wx.navigateTo({
      url: url,
    })
  },
  enterDoor(){
    console.log('info:22',this.data.info)
    let thatNew = this
    if(this.data.distance > 100){
      wx.showModal({
        title: '提示',
        content: '您当前距离实训舱过远，无法开门,是否查看学习内容',
        complete: (res) => {
          if (res.cancel) {
            console.log('取消');
          }
          if (res.confirm) {
            console.log('确定');
            this.toNewIndex1()
          }
        }
      })
      return
    }
    let checkedTime = wx.getStorageSync('checkedTime')
    const appHour = checkedTime.name.match(/\d+/)
    const appDate = new Date(checkedTime.openDateStr)
    const currentDate = new Date()
    if(!(this.isWithinTimeRange(appHour)&&appDate.toDateString==currentDate.toDateString)){
      console.log("时间不对");
      wx.showModal({
        title: '提示',
        content: '当前未到进入时间，请检查选择的学习时段以及实训舱,是否查看学习内容',
        complete: (res) => {
          if (res.cancel) {
            console.log('取消');
          }
      
          if (res.confirm) {
            console.log('确定');
            this.toNewIndex1()
          }
        }
      })
      return
    }
    
    let pa = {
      machineID: this.data.info.machineID
    }
    wx.showLoading({
      title: '正在连接......',
      mask: true,
    })
    ports.ModuleAll.getMachineDetail(pa).then(resp => {
      this.setData({
        info: resp.data.body.data
      })
      let machineDetail = resp.data.body.data
      ports.ModuleAll.getOneAppointmentDetail({
        appointmentID: checkedTime.myAppointmentID
      }).then(respT =>{
        let appointment = respT.data.body.data
        console.log("appointment19",appointment)
        wx.setStorageSync('appointment', appointment)
        thatNew.setData({
          bluetoothDeviceName: this.data.info.name, //传入设备名
          appointmentID: appointment.appointmentID,
          tabIndex: appointment.workType === 3 ? 1 : 0,
          orderSeq: appointment.orderSeq,
          selectItem: appointment
        })
        ports.ModuleAll.isCanInOneMachineByAppointment({
          sessionID: wx.getStorageSync('USER_SESSIONID'),
          machineID: machineDetail.machineID
        }).then(respO => {
          let obj = respO.data.body.data.rows[0]
          let orderSeq = obj.appointmentNumber + 1;
          wx.setStorageSync('orderSeq', orderSeq)
          this.getSensor(checkedTime.objectID);
          this.manualOpenBluetoothAdapter();
        })
      })
    })
  },
  //手动搜索设备
  //第一步
  manualOpenBluetoothAdapter() {
    wx.setStorageSync('blueDeviceID', '')
    this.closeBluetoothAdapter()
    this.openBluetoothAdapter()
    // clearInterval(date)
  },
  //移除蓝牙
  closeBluetoothAdapter() {
    wx.closeBluetoothAdapter()
    this.setData({
      _discoveryStarted: false
    })
  },
  submitOneObjectBrowse(name,res) {
    console.log("提交浏览对象",name,res);
    
    var that = this
   
    ports.ModuleAll.submitOneObjectBrowse1({
      objectDefineID:"8a2f462a94df2dc80194df442b660024",
      objectID:wx.getStorageSync("USER_MEMBERID"),
      name:name,
      objectName:res,
    })
  },
    //开始扫描
  //第二步
  openBluetoothAdapter() {
    var that = this
    //初始化蓝牙模块所有接口只能初始化后才能调佣
    wx.openBluetoothAdapter({
      //蓝牙初始化成功
      success: (res) => {
        console.log('openBluetoothAdapter success', res)
        this.startBluetoothDevicesDiscovery() //开始搜寻附近的蓝牙外围设备
      },
      //蓝牙初始化失败
      fail: (res) => {
        //手机蓝牙未打开或不支持使用蓝牙返回10001
        if (res.errCode === 10001) {
          //监听用户的蓝牙是否打开（监听蓝牙的状态的改变）也可以调用蓝牙模块的所有API。开发者在开发中应该考虑兼容用户在使用小程序过程中打开/关闭蓝牙开关的情况，并给出必要的提示
          // wx.showLoading({
          //   title: '请打开蓝牙',
          // })
          wx.hideLoading()
          wx.showToast({
            title: '请打开蓝牙',
            icon: 'none',
          })
          wx.onBluetoothAdapterStateChange(function (res) {
            wx.hideLoading()
            if (res.available) {
              // wx.showToast({
              //   title: '搜索设备中...',
              //   icon: 'none'
              // }, 1000)
              that.startBluetoothDevicesDiscovery()
            }
          })
        } else {
          wx.hideLoading()
          wx.showToast({
            title: '连接失败，请重试',
            icon: 'none',
          })
          //测试使用，上线注释
            this.toNewIndex1(true)
        }
      }
    })
  },
    //扫描并发现外围设备
  //第三步
 
  //监听蓝牙搜索设备
  //第四步
  onBluetoothDeviceFound() {
    var deviceData = []
    var that = this
    //返回扫描到的设备
    wx.onBluetoothDeviceFound((res) => {
      res.devices.forEach(device => {
        console.log("搜索到的设备：",res.devices);
        this.submitOneObjectBrowse("搜索到的设备",JSON.stringify(device))
        
        if (!device.name && !device.localName) {
          return
        }
        if (device.localName === that.data.info.iccid || device.name === that.data.info.iccid) {
          this.submitOneObjectBrowse("匹配成功的设备",JSON.stringify(device))
          that.createBLEConnection(device.deviceId, device.name)
          that.data.searchFlag = false
          return
        }
      })
    })
  },
  
  
   //点击事件创建设备连接
   createBLEConnection(id, name,e) {
    let deviceId = id
    wx.setStorageSync('blueDeviceID', id)
    wx.setStorageSync('blueDeviceName', name)
    //调用API连接设备
    wx.createBLEConnection({
      //连接的设备id
      deviceId,
      //连接成功
      success: (res) => {
        console.log('连接成功', res);
        //获得service
        wx.getBLEDeviceServices({
          deviceId, // 搜索到设备的 deviceId
          success: (res) => {
            let serviceIDs = []
            for (let i = 0; i < res.services.length; i++) {
              if (res.services[i].isPrimary) {
                serviceIDs.push(res.services[i].uuid)
                // 可根据具体业务需要，选择一个主服务进行通信
              }
              //保存serviceID们到本地
              wx.setStorageSync('blueServiceIDs', serviceIDs)
            }
          },
          fail: (res) => {
            console.log(res, 999);
          }
        })
        //读写特征值
        wx.getBLEDeviceCharacteristics({
          deviceId, // 搜索到设备的 deviceId
          serviceId: wx.getStorageSync('blueServiceIDs'), // 上一步中找到的某个服务
          success: (res) => {
            for (let i = 0; i < res.characteristics.length; i++) {
              let item = res.characteristics[i]
              if (item.properties.write) {
                let buffer = new ArrayBuffer(1)
                let dataView = new DataView(buffer)
                dataView.setUint8(0, 0)
                wx.writeBLECharacteristicValue({
                  deviceId,
                  serviceId,
                  characteristicId: item.uuid,
                  value: buffer,
                })
              }
              if (item.properties.read) { // 该特征值可读
                wx.readBLECharacteristicValue({
                  deviceId,
                  serviceId,
                  characteristicId: item.uuid,
                })
              }
              if (item.properties.notify || item.properties.indicate) {
                wx.notifyBLECharacteristicValueChange({
                  deviceId,
                  serviceId,
                  characteristicId: item.uuid,
                  state: true,
                })
              }
            }
          },
          fail: (res) => {
            console.log(res, 999);
          }
        })
        wx.stopBluetoothDevicesDiscovery({
          success: function (res) {
            console.log('停止查找蓝牙设备', res)
          }
        })
        this.door()
      },
      fail: (err) => {
        wx.hideLoading();
        wx.showToast({
          title: '连接失败，请重试',
          icon: 'none'
        })
        console.log(err, '连接失败');
      }
    })
  },
  door() {
    wx.hideLoading();
    this.action('door', 'unlock')
  },
  //获取蓝牙命令Json
  getCommand(sensorCode, commandValue) {
    return new Promise(resolve => {
      let appointment = this.data.selectItem
      //先获取实训仓的 所有传感器列表
      ports.ModuleMachine.getSensorList({
        machineID: appointment.objectID,
        pageNumber: 999
      }).then(res => {
        if (res.data.header.code == 0) {
          this.setData({
            sensorList:res?.data?.body?.data?.rows || []
          })
          let sensorID = ''
          //筛选出 传入名称 的传感器
          res.data.body.data.rows.forEach(row => {
            if (row.code == sensorCode)
              sensorID = row.sensorID
          });
          //获取该传感器的操作命令，先 写死 unlock 开门
          ports.ModuleMachine.getOneMachineCommandForSanOne({
            machineID: appointment.objectID,
            // sensorID,
            code:'door',
            commandValue,
          }).then(res2 => {
            if (res2.data.header.code == 0) {
              utils.gosign(appointment.appointmentID);
              let c = "SE" + wx.getStorageSync('orderSeq') + "_computer"
              console.log("c",c);
              console.log("sensorList"+this.data.sensorList);
              let o=this.data.sensorList.find(sensor => sensor.code === c)
              console.log("company",o);
              //请先关门电脑自动打开
              this.sleep(10000).then(() => {
                this.querySensorStatus(sensorID,o)
              })
                resolve(res2.data.body.data)       
            } else {
              return resolve('fail')
            }
          })
        } else return resolve('fail')
      })
    })
  },
  querySensorStatus(sensorID,o,attempt = 1, maxAttempts = 99){
    console.log("查询门是否关闭");
    let appointmentList = wx.getStorageSync('appointmentList')
    //睡眠
    ports.ModuleMachine.getOneSensorDetail({
      sensorID,
    }).then(res9 => {
      console.log("openStatus"+res9.data.body.data.openStatus);
      if (res9.data.header.code == 0&&res9.data.body.data.openStatus==0) {
        ports.ModuleMachine.getOneMachineCommandForSanOne({
          machineID: appointmentList.objectID,
          // sensorID: o.sensorID,
          code:'door',
          commandValue: "on",
        }).then(res3 => {
          if (res3.data.header.code == 0) {
            console.log("开启电脑")
          } else {
            return resolve('fail')
          }
        })
      }else {
        if (attempt < maxAttempts) {
          wx.showToast({
            title: '请关闭门锁',
            icon: 'none',
            duration: 1000
          })
          setTimeout(() => {
            this.querySensorStatus(sensorID,o, attempt + 1, maxAttempts);
          }, 2000);
        }else {
          // 达到最大尝试次数，处理失败情况
          console.error('达到最大尝试次数，无法开启电脑');
        }
      }
    })
  },
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  },
  //按下按钮对应的操作
  action(sensor, act) {
    let that = this
    //调用写特征值（开门命令
    this.getCommand(sensor, act).then(jsonData => {
      if (jsonData == 'fail')
        return alert('获取通讯key失败')
      wx.closeBLEConnection({
        deviceId: wx.getStorageSync('blueDeviceID'), // 蓝牙设备ID
        success: (res) => {
          wx.removeStorageSync('blueDeviceID')
          console.log('断开蓝牙连接成功', res);
        },
        fail: (err) => {
          console.log('断开蓝牙连接失败', err);
        }
      });
      let params = {
        sessionID: wx.getStorageSync('USER_SESSIONID'),
        machineID: that.data.info.machineID,
        operateType: 13,
        operateName: '蓝牙连接',
        lastScanMac: that.data.info.macAddress,
      }
      ports.ModuleAll.operateOneMachine(params).then(res => {
        if (res.data.header.code === 0) {
          console.log("开始一次机器使用记录")
        }
      })
      wx.showToast({
        title: '请在听到门锁啪嗒一声后，再推门进入',
        icon: 'none',
        duration: 1000
      })
      // 这里直接进新页面，对于时间和地点已经判断过了，直接进就可以了
      if (this.data.isFromEnterMyLearing) {
        // 如果是从 enterMyLearing 调用的，使用预约项参数跳转
        let appointment = wx.getStorageSync('appointment')
        this.toNewIndex1ForAppointment(appointment, true);
      } else {
        // 否则使用原有逻辑
        this.toNewIndex1(true)
      }
      return
      let selectItem = this.data.selectItem
      let reqURL = `getSectionDetail?sectionID=${selectItem.resourcesObjectID}&sessionID=${wx.getStorageSync('USER_SESSIONID')}`
      ports.ModuleAll.getSectionDetail(reqURL).then(respo => {
        let sectionDetail =respo.data.body.apiSectionDetailDto
        console.log("sectionDetail19",sectionDetail);
        console.log("sectionDetail1922",this.data.selectItem);
        console.log("sectionDetail1911",respo);
        setTimeout(() => {
          wx.setStorageSync('enterFlag', true)
          this.setData({
            enterFlag: true
          })
          console.log(this.data.enterFlag, this.data.ornot)
          let machineID = that.data.selectItem.objectID
          let appointmentID = that.data.appointmentID;
          let trainBusinessID = that.data.info.trainBusinessID;
          let shopID = that.data.info.shopID;
          let mapAddress = that.data.info.address;
          let mapX = that.data.info.mapX;
          let mapY = that.data.info.mapY;
          let tabIndex = wx.getStorageSync('tabIndex');
          let orderSeq = wx.getStorageSync('orderSeq');
          let isVisit = false
          
          let url1 = `/packageA/pages/learnSteps/learnSteps?trainMinutes=${sectionDetail.trainMinutes}&sectionId=${sectionDetail.sectionID}&name=${sectionDetail.name}&shopID=${shopID}&appointmentID=${appointmentID}&mapAddress=${mapAddress}&mapX=${mapX}&mapY=${mapY}&machineID=${machineID}&orderSeq=${orderSeq}`
          let url2 = `/packageA/pages/trainSteps/trainSteps?sectionID=${sectionDetail.sectionID}&shopID=${shopID}&appointmentID=${appointmentID}&mapAddress=${mapAddress}&mapX=${mapX}&mapY=${mapY}&machineID=${machineID}&orderSeq=${orderSeq}`
          let url3 = `/packageA/pages/examSteps/examSteps?sectionID=${sectionDetail.sectionID}&shopID=${shopID}&appointmentID=${appointmentID}&mapAddress=${mapAddress}&mapX=${mapX}&mapY=${mapY}&machineID=${machineID}&orderSeq=${orderSeq}`
          if(selectItem.workType==3){
            this.toNewIndex1()
          }else{
            let url = selectItem.workType==2?url1:selectItem.workType==3?url2:url3
            wx.setStorageSync('urlForEnterFlag', url)
            wx.navigateTo({
              url:url
            })
          }
        }, 1000)
        //调用写特征值（开门命令）
        this.writeBLECharacteristicValue(jsonData)
      })
    })
  },
   //写BEI特征值
   writeBLECharacteristicValue(jsonData) {
    console.log(jsonData, "jsondata")
    // 向蓝牙设备发送数据
    //let uuid = wx.getStorageSync('uuid');
    let that = this
    const serviceId = '0000FFF0-0000-1000-8000-00805F9B34FB' //写死 每个蓝牙都一样
    const characteristicId = "0000FFF2-0000-1000-8000-00805F9B34FB" //写死 每个蓝牙都一样
    let asciiArray = jsonToAscii(jsonData)
    let buffer = new ArrayBuffer(asciiArray.length);
    let dataView = new DataView(buffer)
    for (let i = 0; i < asciiArray.length; i++) {
      dataView.setUint8(i, asciiArray[i]);
    }
    wx.writeBLECharacteristicValue({
      deviceId: wx.getStorageSync('blueDeviceID'),
      serviceId,
      characteristicId,
      value: buffer,
      success(res) {
        console.log("消息发送成功")
      },
      fail(e) {
        console.log("发送消息失败: " + e.errMsg, );
      },
    })
    wx.onBLECharacteristicValueChange((characteristic) => {
      console.log(characteristic, "characteristic")
    })
  },
  getSensor(machineID) {
    var that = this;
    ports.ModuleMachine.getSensorList({
      machineID: machineID,
      pageNumber: 99,
    }).then(res => {
      if (res.data.header.code == 0) {
        that.setData({
          sensorList: res.data.body.data.rows
        })

      } else return 
    })
  },
  isWithinTimeRange(hour){
    return appointmentModule.isWithinTimeRange(hour);
    // 创建一个新的 Date 对象表示当前时间
    const now = new Date();
    // 获取当前时间的小时和分钟
    const currentHour = now.getHours();
    const currentMinute = now.getMinutes();
    // 构建目标时间范围的开始和结束时间
    const targetStart = new Date(now);
    targetStart.setHours(hour - 1, 30, 0, 0); 
    const targetEnd = new Date(now);
    targetEnd.setHours(hour, 50, 0, 0); 
    // 比较当前时间是否在目标时间范围内
    return now >= targetStart && now <= targetEnd;
  },
  toRuleAndView(){
    wx.navigateTo({
      url: '/packageA/pages/userProfessionalism/userProfessionalism',
    })
  },
  async getDevice() {
    let res = await ports.ModuleAll.getSystemInfo();
    let ADCID = new Date().getTime().toString(36) + Math.random().toString(36).substr(2, 9);
    let params = {
      applicationID: app.applicationID,
      siteID: app.siteID,
      company: res.brand, //制造商名称
      name: res.model, //设备型号
      shortName: res.brand, //设备型号
      category: 3, //设备类型
      OSname: res.system, //操作系统
      model: res.model, //品牌型号
      // udid: openId, //一个字符串
      // imei: openId, //手机特有的1个串
      mac: ADCID, //设备的mac地址
    }
    let resDevice = await ports.ModuleAll.submitDeviceInfo(params)
    wx.setStorageSync('deviceID', resDevice.data.body.deviceID)
    this.setData({
      clearFlag: true
    })
  },
  showNotice(){
    let params = {
      articleID: app.aboutUsArticleID
    }
    ports.ModuleAll.getArticleMoreDetail(params).then(res => {
      if (res.data.header.code === 0) {
        this.setData({
          content: res.data.body.data.content,
          showModal: true,
        })
        wx.setStorageSync('hasShowModal', true)
      }
    })
  },
  handleCloseModal(){
    this.setData({
      showModal:false,
    })
  },
  toHref(){
    wx.navigateTo({
      url: '/packageA/pages/machineListNew/machineListNew',
    })
  },

  // getDistance() {
  //   function calculateDistance(lat1, lng1, lat2, lng2) {
  //     var that = this;
  //     let rad1 = lat1 * Math.PI / 180.0;
  //     let rad2 = lat2 * Math.PI / 180.0;
  //     let a = rad1 - rad2;
  //     let b = lng1 * Math.PI / 180.0 - lng2 * Math.PI / 180.0;
  //     let s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2) + Math.cos(rad1) * Math.cos(
  //       rad2) * Math.pow(
  //       Math.sin(b / 2), 2)));
  //     s = s * 6378.137;
  //     s = Math.round(s * 10000) / 10000;
  //     s = s.toString();
  //     s = s.substring(0, s.indexOf('.') + 2);
  //     console.log('距离：', s);
  //     return s; //返回距离
  //   }
  //   wx.getLocation({
  //     type: 'gcj02', // 默认为 wgs84 返回 gps 坐标，gcj02 返回可用于 wx.openLocation 的坐标
  //     altitude: true, // true 会返回高度信息
  //     isHighAccuracy: true, //开启高精度定位
  //     highAccuracyExpireTime: 3100, //高精度定位超时时间(ms)
  //     success: (res) => {
  //       console.log(res, '获取位置成功');
  //       // console.log(this.info, '获取位置成功');
  //       let distance = longitude.calculateDistance(this.data.info.mapY, this.data.info.mapX, res.latitude, res.longitude)
  //       console.log(distance, "距离")
  //       this.setData({
  //         distance
  //       })
  //       this.setData({
  //         myX: res.latitude
  //       })
  //       this.setData({
  //         myY: res.longitude
  //       })
  //       wx.stopPullDownRefresh();
  //     },
  //     fail: (err) => {
  //       console.log('获取距离失败');
  //       wx.stopPullDownRefresh();
  //     }
  //   })
  // },

  async init1(){

    let checkedMachine = wx.getStorageSync('checkedMachine')
    if(checkedMachine.id){
      this.setData({
        checkedMachine:checkedMachine
      })
      let distanceStr = this.data.checkedMachine.distance;
      if(distanceStr && distanceStr != 9999 && !isNaN(Number(distanceStr)) ){
        distanceStr = this.formatDistance(Number(distanceStr))
      }
      if(this.data.checkedMachine.distance){
        this.setData({
          distance:this.data.checkedMachine.distance,
          distanceStr
        })
      }
    } else {
      this.getMachineList()
      return
    }
    let that = this
    let p = {
      machineID: that.data.checkedMachine.id,
    }
    ports.ModuleAll.getMachineDetail(p).then(resOne => {
      let info = resOne.data?.body?.data || {}
      info.machineName=info.name
      info.id=info.machineID
      const checkedMachine = {
        id:info.id,
        machineName:info.machineName,
        address: info.address,
        code:info.code,
        distance:this.data.distance || '',
        bizType:info.bizType
      }
      

      this.setData({
        info: resOne.data.body.data,
        checkedMachine
      })
      wx.setStorageSync('checkedMachine',checkedMachine);

      if (wx.getStorageSync('USER_SESSIONID')) {
        this.getDistance();
      }
    })
  },

  async getMachineList() {
      let params = {
        applicationID: app.applicationID,
        companyID: app.companyID,
        pageNumber: 999
      };
      try {
        const res = await ports.ModuleAll.getMachineList(params);
        let list = res.data.body.data.rows || [];

        //去掉不在时间内的数据
        const currentDate = new Date();
        currentDate.setHours(0, 0, 0, 0);
        list = list.filter(machine => {
          const { beginDateStr, endDateStr } = machine;
          if (!beginDateStr || !endDateStr) {
            return true;
          }
          const beginDate = new Date(beginDateStr);
          const endDate = new Date(endDateStr);
          return currentDate >= beginDate && currentDate <= endDate;
        });
       
        this.setData({
          machineList: list
        });
        // 确保 getDistanceNew 和 getMemberTrain 是异步函数并返回 Promise
        // 使用并行处理替代顺序执行（性能更好）
        await Promise.all(list.map(async (item, i) => {
          await this.getDistanceNew(item.mapX, item.mapY, i);
          await this.getMemberTrain(item.trainBusinessID, i);
        }));
  
        // 确保所有异步操作完成后再执行
        setTimeout(async() => {
          await this.getNearMachine();
        }, 1000);
      } catch (error) {
        console.error('Error fetching machine list:', error);
      }
  },
  getNearMachine(){
    let machineList = this.data.machineList
    // console.log(machineList,'machineList');

    const minItem = machineList.reduce((min, current) => 
      current.distanceReal < min.distanceReal ? current : min
    );
    
    // console.log(minItem,'distanceReal');
    let checkedMachine = {
      id:minItem.id,
      machineName:minItem.name,
      distance:minItem.distanceReal,
      address:minItem.address,
      code:minItem.code
    }
    wx.setStorageSync('checkedMachine', checkedMachine)
    this.setData({
      checkedMachine:checkedMachine
    })
    let distanceStr = this.data.checkedMachine.distance;
    if(distanceStr && distanceStr != 9999 && !isNaN(Number(distanceStr)) ){
      distanceStr = this.formatDistance(Number(distanceStr))
    }
    if(this.data.checkedMachine.distance){
      this.setData({
        distance:this.data.checkedMachine.distance,
        distanceStr
        
      })
    }
    this.init()
  },

  async getMemberTrain(id,i) {
    let params = {
      applicationID: app.applicationID,
      sessionID: wx.getStorageSync('USER_SESSIONID'),
      trainBusinessID: id,
      joinMemberID: wx.getStorageSync('USER_MEMBERID')
    }
    ports.ModuleAll.getMemberTrainList(params).then(res => {
      
      let list = res.data.body.data.rows || []
      let tempStr = "machineList["+ i + "].isShow"; 
      this.setData({
        [tempStr]: list.length === 0 ? false : true
      })
    })
  },

  async getDistanceNew(mapX,mapY,i) {
    function calculateDistance(lat1, lng1, lat2, lng2) {
        var that = this;
        let rad1 = lat1 * Math.PI / 180.0;
        let rad2 = lat2 * Math.PI / 180.0;
        let a = rad1 - rad2;
        let b = lng1 * Math.PI / 180.0 - lng2 * Math.PI / 180.0;
        let s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2) + Math.cos(rad1) * Math.cos(
          rad2) * Math.pow(
          Math.sin(b / 2), 2)));
        s = s * 6378.137;
        s = Math.round(s * 10000) / 10000;
        s = s.toString();
        s = s.substring(0, s.indexOf('.') + 2);
        console.log('距离：', s);
        return s; //返回距离
    } 
    function formatDistace(distanceInMeters) {
      if (distanceInMeters >= 1000) {
        // 将距离从米转换为千米，并保留两位小数
        const distanceInKilometers = (distanceInMeters / 1000).toFixed(2);
        return `${distanceInKilometers} 千米`;
      } else {
        return `${distanceInMeters} 米`;
      }
    }
    
    wx.getLocation({
      type: 'gcj02', // 默认为 wgs84 返回 gps 坐标，gcj02 返回可用于 wx.openLocation 的坐标
      altitude: true, // true 会返回高度信息
      isHighAccuracy: true, //开启高精度定位
      highAccuracyExpireTime: 3100, //高精度定位超时时间(ms)
      success: (res)=> {
        // console.log(res, '获取位置成功');
        // let distance = calculateDistance(this.data.info.mapX,this.data.info.mapY, res.latitude, res.longitude)
        let distance = longitude.calculateDistance(mapY,mapX, res.latitude, res.longitude)
        console.log(distance,"距离")
        let tempStr = "machineList["+ i + "].distance"; 
        let tempStrReal = "machineList["+ i + "].distanceReal"; 
        this.setData({
          [tempStr]: formatDistace(distance),
          [tempStrReal]:distance
        })
        // let distance = longitude.calculateDistance(this.info.mapX, this.info.mapY, res.latitude, res.longitude)
      },
      fail: (err) => {
        console.error(err)
        console.log('获取距离失败');
      }
    })
  },

  childInit(){
     //导航条加载动画
     wx.showNavigationBarLoading()
     //loading 提示框
     wx.showLoading({
       title: 'Loading...',
     })
    let components = this.selectAllComponents(".child")
    for (var i = 0; i < components.length; i++) {
      components[i].init();
      if(i===components.length -1){
        setTimeout(()=>{
          wx.hideLoading(); 
          wx.hideNavigationBarLoading();
          //停止下拉刷新
          wx.stopPullDownRefresh();
        },1000)
      }
    }
  },
  async init() {
    if (!wx.getStorageSync('weixinAppMemberID')) this.createWeixinAppMember();
    if (!wx.getStorageSync('deviceID')) this.getDevice();
    if (wx.getStorageSync('USER_SESSIONID')) {
      let isLogin = await this.checkSession();
      
      if (!isLogin) {
        this.data.sessionPromise=await wx.getStorageSync('USER_SESSIONID')
        return
      }
      if (isLogin) {
       this.data.sessionPromise= await ports.ModuleAll.sessionTimeoutLogin({
          deviceID: wx.getStorageSync('deviceID'),
          memberID: wx.getStorageSync('USER_MEMBERID')
        }).then(res=>{
          return res.data.body.sessionID
        })
        wx.setStorageSync("USER_SESSIONID", this.data.sessionPromise)
      }
    } else {
      await utils.LOGIN(1)
      if(!wx.getStorageSync('hasShowModal')){
        this.fetchContent();
      }
    }
    console.log("getNowCheckTime1")
    // this.childInit();
    this.init1();
    this.getMemberGroupList();
  },
  getArticleSimpleDetail() {
    let params = {
      articleID: app.aboutUsArticleID
    }
    ports.ModuleAll.getArticleMoreDetail(params).then(res => {
      if (res.data.header.code === 0) {
        //console.log(res.data.body.data.content)
        this.setData({
          content: res.data.body.data.content,
        })
      }
    })
  },
  fetchContent: function() {
    let params = {
      articleID: app.aboutUsArticleID
    }
    ports.ModuleAll.getArticleMoreDetail(params).then(res=>{
      if(res.data.header.code === 0){
        this.setData({
          content:res.data.body.data.content,
          showModal:true
        })
        wx.setStorageSync('hasShowModal', true)
      }
    })
  },
  /**
   * 关闭模态框
   */
  handleCloseModal: function() {
    this.setData({ showModal: false });
  },
  createWeixinAppMember() {
    let params = {
      name: app.weixinAppTitle + '首页关注',
      applicationID: app.applicationID,
      companyID: app.companyID,
      siteID: app.siteID,
      weixinAppID: app.weixinAppID
    }
    ports.ModuleAll.createOneWeixinAppMember(params).then(res => {
      wx.setStorageSync('weixinAppMemberID', res.data.body.weixinAppMemberDetailID)
    })
  },
  async toMineInfo() {
    if (!wx.getStorageSync('deviceID')) this.getDevice();
    if (!wx.getStorageSync('USER_MEMBER')) {
      wx.showLoading({
        title: '加载中',
      })
      wx.setStorageSync('previousUrl', `pages/index/index`)
      await utils.LOGIN(0);
      this.setData({
        memberInfo: wx.getStorageSync('USER_MEMBER')
      })  
    }
  },
  async getDevice() {
    let res = await ports.ModuleAll.getSystemInfo();
    console.log(res,'-----设备');
    let ADCID = new Date().getTime().toString(36) + Math.random().toString(36).substr(2, 9);
    let params = {
      applicationID: app.applicationID,
      siteID: app.siteID,
      company: res.brand, //制造商名称
      name: res.model, //设备型号
      shortName: res.brand, //设备型号
      category: 3, //设备类型
      OSname: res.system, //操作系统
      model: res.model, //品牌型号
      // udid: openId, //一个字符串
      // imei: openId, //手机特有的1个串
      mac: ADCID, //设备的mac地址
    }
    let resDevice = await ports.ModuleAll.submitDeviceInfo(params)
    wx.setStorageSync('deviceID', resDevice.data.body.deviceID)
  },
  checkSession() {
    return new Promise((resolve)=>{
      ports.ModuleAll.checkSessionIsOK({
        sessionID: wx.getStorageSync('USER_SESSIONID')
      }).then(res => {
        resolve(res.data.body.isTimeOut) 
      })
    })
  },
  // 微信好友
  handleShare(e){
    console.log('handleShare',e)
    this.setData({
      isCustomShare:true,
      orderSeqForShare:e.currentTarget.dataset.item
    })
    return
    let appointmentDetail = this.data.shareAppointmentDetail
    let machineDetail = this.data.info
    let item = wx.getStorageSync('checkedTime')
    const USER_MEMBERID = wx.getStorageSync('USER_MEMBERID');
    const urlParams = {
      applyMemberID: USER_MEMBERID,
      machineID: machineDetail.machineID,
      objectID: machineDetail.machineID, // 如果需要，可以检查是否重复
      objectName: machineDetail.name,
      customerCompanyID: machineDetail.customerCompanyID,
      playCompanyID: machineDetail.playCompanyID,
      distributorCompanyID: machineDetail.distributorCompanyID,
      timeIntervalInstanceID: appointmentDetail.timeIntervalInstanceID,
      timeIntervalName: appointmentDetail.timeIntervalName,
      workType: appointmentDetail.workType,
      orderSeq: this.data.orderSeqForShare,
      resourcesObjectDefineID: '8a2f462a5c820afb015c8237567e1540',
      resourcesObjectID: appointmentDetail.resourcesObjectID,
      resourcesObjectName: appointmentDetail.resourcesObjectName,
      name: item.timeIntervalDefineName,
      companyID: app.companyID,
      objectDefineID: app.machineObjectDefineID,
      openDate: utils.formatDate(new Date().getTime(), 'yyyy-MM-dd')
    };
    let url = buildUrl('/pages/sharePageForInvite/sharePageForInvite', urlParams);
    console.log("url:",url);
    wx.navigateTo({
      url: url,
    })
  },
  //微信分享
  async onShareAppMessage(res) {
    if(res.from==='button'){
      let appointmentDetail = this.data.shareAppointmentDetail
      let options={
        objectDefineID:'8a2f462a76d1a4b30176d5a2cf3608c0', //appointment
        objectID:appointmentDetail.appointmentID,
        objectName:appointmentDetail.name,
        name:appointmentDetail.memberName+'邀请好友来参加'+appointmentDetail.objectName+'实训',
        companyID:app.companyID,
        memberID:appointmentDetail.memberID,
        memberName:appointmentDetail.memberName
      }
      const forwardID = await ports.ModuleForward.createOneForward(options)
      
      let machineDetail = this.data.info
      let item = wx.getStorageSync('checkedTime')
      const USER_MEMBERID = wx.getStorageSync('USER_MEMBERID');
      let member = wx.getStorageSync('USER_MEMBER')
      
      const urlParams = {
        applyMemberID: USER_MEMBERID,
        bizType:machineDetail.bizType,
        machineID: machineDetail.machineID,
        objectID: machineDetail.machineID, // 如果需要，可以检查是否重复
        objectName: machineDetail.name,
        nextTime:`${this.data.nextBeginTime} - ${this.data.nextEndTime}`,
        memberAvatar: member.avatarURL,
        customerCompanyID: machineDetail.customerCompanyID,
        playCompanyID: machineDetail.playCompanyID,
        distributorCompanyID: machineDetail.distributorCompanyID,
        timeIntervalInstanceID: appointmentDetail.timeIntervalInstanceID,
        timeIntervalName: appointmentDetail.timeIntervalName,
        workType: appointmentDetail.workType,
        orderSeq: this.data.orderSeqForShare,
        resourcesObjectDefineID: '8a2f462a5c820afb015c8237567e1540',
        resourcesObjectID: appointmentDetail.resourcesObjectID,
        resourcesObjectName: appointmentDetail.resourcesObjectName,
        name: item.timeIntervalDefineName,
        companyID: app.companyID,
        objectDefineID: app.machineObjectDefineID,
        openDate: utils.formatDate(new Date().getTime(), 'yyyy-MM-dd'),

        // 其他参数
        applyMemberName:member.name,
        trainBusinessID:machineDetail.trainBusinessID,
        appointmentID:this.data.myAppointmentID,
        forwardID,
      };
      let path = buildUrl_n('/pages/sharePageForInvite/sharePageForInvite', urlParams);
      return {
        title: '邀请好友来'+machineDetail.name+'',
        path,
        imageUrl: app.logoimg
      }
    }else{
      let path = utils.sharUrl("/pages/index/index")
      return {
        title: app.weixinAppTitle,
        path,
        imageUrl: app.logoimg
      }
    }
  },
  onPullDownRefresh(){
    if (this.data.showModal) {
      wx.stopPullDownRefresh(); // 如果模态框显示，停止下拉刷新
    } else {
      console.log('触发');
      this.init1()
      if(wx.getStorageSync('loginTime')){
        utils.isLoginTimeOut()
      }
      this.childInit();
      this.checkHasCurLearningNew()
    }
  },
  enterMyLearing(){
    let appointment = wx.getStorageSync('appointment')
    this.setData({
      selectItem:appointment,
      isFromEnterMyLearing: true  // 标识是从 enterMyLearing 调用的
    })

    // 设置 checkedTime 和 checkedMachine 缓存，确保跳转参数正确
    this.setCheckedTimeForAppointment(appointment);
    this.setCheckedMachineForAppointment(appointment);

    let pa = {
      machineID: appointment.objectID
    }
    ports.ModuleAll.getMachineDetail(pa).then(respP =>{
      this.setData({
        info: respP.data.body.data
      })
    })
    wx.showLoading({
      title: '正在连接......',
      mask: true,
    })
    this.setData({
      bluetoothDeviceName: this.data.info.name, //传入设备名
      appointmentIndex: appointment.index,
      appointmentID: appointment.appointmentID,
      tabIndex: appointment.workType === 3 ? 1 : 0,
      orderSeq: appointment.orderSeq,
    })
    ports.ModuleAll.isCanInOneMachineByAppointment({
      sessionID: wx.getStorageSync('USER_SESSIONID'),
      machineID: appointment.objectID
    }).then(resp => {
      let obj = resp.data.body.data.rows[0]
      let orderSeq = obj.appointmentNumber + 1;
      wx.setStorageSync('orderSeq', orderSeq)
    })
    this.getSensor(appointment.objectID);
    this.manualOpenBluetoothAdapter();
  },

  /**
   * 为预约项设置 checkedTime 缓存
   * @param {Object} appointment - 预约项数据
   */
  setCheckedTimeForAppointment(appointment) {
    // 从 appointment 中获取时间信息
    const openDateStr = appointment.openDateStr; // 格式: "2025-07-03"
    const timeIntervalName = appointment.name; // 格式: "18点"

    if (!openDateStr || !timeIntervalName) {
      console.error('缺少必要的时间信息:', { openDateStr, timeIntervalName });
      return;
    }

    // 从 timeIntervalName 中提取小时数
    const hourMatch = timeIntervalName.match(/(\d+)/);
    if (!hourMatch) {
      console.error('无法解析 timeIntervalName:', timeIntervalName);
      return;
    }

    const hour = parseInt(hourMatch[1], 10);

    // 格式化 nextBeginTime: "07月03日 18:00"
    const [, month, day] = openDateStr.split('-');
    const nextBeginTime = `${month}月${day}日 ${hour.toString().padStart(2, '0')}:00`;

    // 格式化 nextEndTime: "18:50"
    const nextEndTime = `${hour.toString().padStart(2, '0')}:50`;

    // 构造 showTime (格式: "18:00-18:50")
    const showTime = `${hour.toString().padStart(2, '0')}:00-${nextEndTime}`;

    // 构造 checkedTime 对象
    const checkedTime = {
      id: appointment.timeIntervalInstanceID || appointment.appointmentID || '',
      name: timeIntervalName,
      openDateStr: openDateStr,
      openDate: openDateStr,
      showTime: showTime,
      beginHour: `${hour.toString().padStart(2, '0')}:00`,
      endHour: nextEndTime,
      nextBeginTime: nextBeginTime,
      nextEndTime: nextEndTime,
      myAppointmentID: appointment.appointmentID || null,
      timeIntervalInstanceID: appointment.timeIntervalInstanceID || appointment.appointmentID || '',
      timeIntervalDefineName: timeIntervalName
    };

    console.log('设置 checkedTime 缓存 (从预约):', checkedTime);
    wx.setStorageSync('checkedTime', checkedTime);
  },

  /**
   * 为预约项设置 checkedMachine 缓存
   * @param {Object} appointment - 预约项数据
   */
  setCheckedMachineForAppointment(appointment) {
    const checkedMachine = {
      id: appointment.objectID || '',
      machineName: appointment.objectName || '',
      address: appointment.address || '',
      code: appointment.code || '',
      distance: appointment.distance || 0,
      bizType: appointment.bizType || ''
    };

    console.log('设置 checkedMachine 缓存 (从预约):', checkedMachine);
    wx.setStorageSync('checkedMachine', checkedMachine);
  },

  /**
   * 为预约项跳转到 newIndex1 页面
   * @param {Object} appointment - 预约项数据
   * @param {Boolean} openDoorSuccess - 开门成功状态
   */
  toNewIndex1ForAppointment(appointment, openDoorSuccess) {
    // 从预约项中提取时间信息
    const timeIntervalName = appointment.name; // 格式: "18点"
    const openDateStr = appointment.openDateStr; // 格式: "2025-07-03"

    if (!timeIntervalName || !openDateStr) {
      console.error('缺少必要的时间信息:', { timeIntervalName, openDateStr });
      return;
    }

    // 从 timeIntervalName 中提取小时数
    const hourMatch = timeIntervalName.match(/(\d+)/);
    if (!hourMatch) {
      console.error('无法解析 timeIntervalName:', timeIntervalName);
      return;
    }

    const hour = parseInt(hourMatch[1], 10);
    const [, month, day] = openDateStr.split('-');

    // 构造跳转参数
    const nextBeginTime = `${month}月${day}日 ${hour.toString().padStart(2, '0')}:00`;
    const nextEndTime = `${hour.toString().padStart(2, '0')}:50`;
    const orderSeq = appointment.orderSeq || '';
    const workType = appointment.workType || '';

    let url = `/packageA/pages/newIndex1/newIndex1?nextBeginTime=${nextBeginTime}&nextEndTime=${nextEndTime}&orderSeq=${orderSeq}&workType=${workType}&openDoorSuccess=${!!openDoorSuccess}`;

    console.log('跳转到 newIndex1 (从预约):', { nextBeginTime, nextEndTime, orderSeq, workType, openDoorSuccess });

    wx.navigateTo({
      url: url,
    });
  },
  
  async checkHasCurLearningNew(){
    // await this.getLocationNew()
    // console.log('checkHasCurLearningNew-getLocationNew')

    let enterFlag = wx.getStorageSync('enterFlag')
    if(enterFlag == true){
      let appointmentInStorage = wx.getStorageSync('appointment')
      let appointment={}
      ports.ModuleAll.getOneAppointmentDetail({
        appointmentID: appointmentInStorage.appointmentID
      }).then(async res=>{
        appointment = res.data.body.data
        let entertime = appointment.applyTimeStr
        const timePart = entertime.split(' ')[1];
        console.log(timePart);
        let appointmentTime = appointment.name
        const appHour = appointmentTime.match(/\d+/)
        const appDate = new Date(appointment.openDateStr)
        const currentDate = new Date()      
        if(appointment.status===6&&this.isWithinTimeRange(appHour)&&appDate.toDateString==currentDate.toDateString){
          let curLearningBoxNeed={
            showCurLearningBox:true,
            enterTime:timePart,
            appointmentTime:appointmentTime,
            showType:1,
            canUseApp:[]
          }
          await utils.setDataAsync(this, {
            curLearningBoxNeed,
            mycurLearningBoxNeed: true  // 添加这里
          })
          // 如果这个用户有状态为签到的学习，那么直接返回，无需再判断list,在首页部分就不在显示预约的list了
          return
        }
      })
    }else{
    await this.getAllAppointment()
    }
    let intervalId;
    intervalId = setInterval(() => {
      if (this.data.getLocationStatus === true) {
        console.log('开始发');
        clearInterval(intervalId); // 清除定时器
        this.checkHasCurLearning(); // 执行下一步操作
      }
      console.log('不开始');
      this.setData({
        mycurLearningBoxNeed: true
      })
    }, 500); // 设置间隔时间为500毫秒（即0.5秒）
  },
  async checkHasCurLearning(){
    await this.getLocationNew()
    console.log('checkHasCurLearning-getLocationNew')
    let that = this
    // 通过判断是否需要登录来进行判断是否显示正在进行的预约学习
    // if(!wx.getStorageSync('loginTime')){
    //   utils.LOGIN(1);
    //   return
    // }
    const date = wx.getStorageSync('loginTime');
    const now = new Date();
    const diff = now - date;
    const diffInHours = diff / (1000 * 60 * 60);
    
    if (false) {
      console.log('111checkHasCurLearning时间戳表示的时间早于一小时前');
      // utils.LOGIN(1);
      // 如果未登录则不显示正在进行的预约学习
      that.setData({
        curLearningBoxNeed:{
          showCurLearningBox:false,
          enterTime:"",
          appointmentTime:"",
          canUseApp:[],
          showType:1
        }
      })
    } else { 
      console.log('222checkHasCurLearning时间戳表示的时间在一小时内');
      let enterFlag = wx.getStorageSync('enterFlag')
      if(enterFlag == false){
        // this.getAllAppointment()
      }else{
        let appointmentInStorage = wx.getStorageSync('appointment')
        let appointment={}
        ports.ModuleAll.getOneAppointmentDetail({
          appointmentID: appointmentInStorage.appointmentID
        }).then(res=>{
          appointment = res.data.body.data
          let entertime = appointment.applyTimeStr 
          const timePart = entertime.split(' ')[1];
          console.log(timePart);
        let appointmentTime = appointment.name

        if(appointment.status===6){
          // let curLearningBoxNeed={
          //   showCurLearningBox:true,
          //   enterTime:timePart,
          //   appointmentTime:appointmentTime,
          //   showType:1,
          //   canUseApp:[]
          // }
          // that.setData({
          //   curLearningBoxNeed:curLearningBoxNeed
          // })
          // 如果这个用户有状态为签到的学习，那么直接返回，无需再判断list,在首页部分就不在显示预约的list了
          // console.log("i'm free!!");
          return
        }else{
          this.getAllAppointment()
        }
        })
      }
    }
  },
  addCodeToTimestamp(timestamp, code) {
    // 确保 code 是数字类型，并且在0-23之间
    code = parseInt(code, 10);
    if (isNaN(code) || code < 0 || code > 23) {
      throw new Error('Invalid hour code');
    }
    // 创建一个新的 Date 对象并设置其时间为给定的时间戳
    let date = new Date(timestamp);
    // 设置小时部分
    date.setHours(code, 0, 0, 0); // 设置分钟、秒和毫秒为0
    return date.getTime(); // 返回新的时间戳
  },
  // 这个方法先不调用
  // getAllAppointmentNewbak(){
  //   let that = this
  //   let params = {
  //     applicationID: app.applicationID,
  //     // sessionID: wx.getStorageSync('USER_SESSIONID'),
  //     memberID: wx.getStorageSync('USER_MEMBERID'),
  //     isBlank: 1,
  //     sortTypeOpen:1,
  //     sortTypeName:1,
  //     pageNumber:10000,
  //     currentPage: 1
  //   }
  //   ports.ModuleAll.getAppointmentList(params).then(respA => {
  //     let allAppointments=[]
  //     allAppointments=respA.data.body.data.rows
  //     let canUseApp=[]
  //     if(allAppointments.length===0) {
  //       that.setData({
  //         curLearningBoxNeed:{
  //           showCurLearningBox:false,
  //           enterTime:"",
  //           appointmentTime:"",
  //           canUseApp:[],
  //           showType:1
  //         }
  //       })
  //       return
  //     }
  //     allAppointments.forEach(app=>{
  //       if(app.status===1){
  //         app.createdTimeStr = app.createdTimeStr.split('-')[1]+'-'+app.createdTimeStr.split('-')[2]
  //         app.distanceUse = 9990
  //         app.openDateStrNew=app.openDateStr.split('-')[1]+'-'+app.openDateStr.split('-')[2]
  //         app.canInFlag = false;
  //         console.log('app',app)
  //         canUseApp.push(app)
  //         wx.setStorageSync('appointmentList', canUseApp)
  //         if(canUseApp.length===0){
  //           that.setData({
  //             curLearningBoxNeed:{
  //               showCurLearningBox:false,
  //               enterTime:"",
  //               appointmentTime:"",
  //               canUseApp:[],
  //               showType:1
  //             }
  //           })
  //           return
  //         }
  //         console.log('canUseApp',canUseApp)
  //         setTimeout(()=>{
  //           that.setData({
  //             // curLearningBoxNeed:{
  //             //   showCurLearningBox:true,
  //             //   enterTime:"",
  //             //   appointmentTime:"",
  //             //   canUseApp:canUseApp,
  //             //   showType:2
  //             // }
  //           })
  //         },300)
          
  //       }
  //     })

  //   })
  // },
  async getAllAppointment(){
    const that = this;

    if (!that.data.positionReady) {
      await new Promise(resolve => {
        that.onPositionReady = resolve; // 在位置获取回调中调用resolve
        wx.getLocation({
          type: 'wgs84',
          success: (res) => {
            that.setData({
              myX: res.longitude,
              myY: res.latitude,
              positionReady: true
            }, () => that.onPositionReady?.());
          }
        });
      });
    }


    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    
    // 2. 拼接成 “YYYY-MM-DD” 格式
    const beginTime = `${year}-${month}-${day}`;
    const params = {
      applicationID: app.applicationID,
      sessionID: wx.getStorageSync('USER_SESSIONID'),
      memberID: wx.getStorageSync('USER_MEMBERID'),
      isBlank: 1,
      sortTypeOpen:1,
      sortTypeName:1,
      pageNumber: 999,
      currentPage: 1,
      beginTime
    };

    try {
      // 1. 获取预约列表
      const resp = await ports.ModuleAll.getAppointmentList(params);
      const allAppointments = resp.data.body.data.rows || [];
      console.log('获取预约列表成功', allAppointments);

      if (allAppointments.length === 0) {
        that.setData({
          curLearningBoxNeed: {
            showCurLearningBox: false,
            enterTime: "",
            appointmentTime: "",
            canUseApp: [],
            showType: 1
          }
        });
        return;
      }

      // 2. 筛选有效的预约
      const validAppointments = allAppointments.filter(app => app.status === 1);
      
      // 3. 顺序处理每个预约的机器详情
      const canUseApp = [];
      
      // 使用 for...of 循环确保顺序执行
      for (const app of validAppointments) {
        app.createdTimeStr = app.createdTimeStr.split('-')[1] + '-' + app.createdTimeStr.split('-')[2];
        const param = { machineID: app.objectID };
        
        // 等待每个机器详情请求完成
        const respN = await ports.ModuleAll.getMachineDetail(param);
        
        // 处理机器详情数据
        app.macineDetail = respN.data.body.data;
        
        // 距离计算
        if (that.data.showJuli) {
          app.distanceUse = longitude.calculateDistance(
            respN.data.body.data.mapY,
            respN.data.body.data.mapX,
            that.data.myX,
            that.data.myY
          );
        } else {
          app.distanceUse = 9999;
        }
        
        // 时间判断
        const appHour = app.name.match(/\d+/);
        const appDate = new Date(app.openDateStr);
        const currentDate = new Date();
        
        app.canInFlag = (
          that.isWithinTimeRange(appHour) && 
          appDate.toDateString() === currentDate.toDateString()
        );
        
        // 距离限制
        if (app.distanceUse > 100) {
          app.canInFlag = false;
        }
        
        // 格式化距离
        if (app.distanceUse !== 9999) {
          app.distanceUse = that.formatDistance(app.distanceUse);
        }
        
        app.openDateStrNew = app.openDateStr.split('-')[1] + '-' + app.openDateStr.split('-')[2];
        
        // 添加到可用列表
        if (!app.canInFlag && canUseApp.length < 5) {
          canUseApp.push(app);
        }
      }
      
      // 4. 所有请求完成后设置数据
      wx.setStorageSync('appointmentList', canUseApp);
      // console.log(canUseApp,'canUseApp','appointmentList111');
      await utils.setDataAsync(that, {
        curLearningBoxNeed: {
          showCurLearningBox: canUseApp.length > 0,
          enterTime: "",
          appointmentTime: "",
          canUseApp: canUseApp,
          showType: canUseApp.length > 0 ? 2 : 1,
          setp: 2
        }
      });
    return true;
      
    } catch (error) {
      console.error('获取数据失败:', error);
      await utils.setDataAsync(that, {
        curLearningBoxNeed: {
          showCurLearningBox: false,
          enterTime: "",
          appointmentTime: "",
          canUseApp: [],
          showType: 1
        }
      });
      return true;
    }
  },
  isWithinTimeRange(hour){
    // 创建一个新的 Date 对象表示当前时间
    const now = new Date();
    // 获取当前时间的小时和分钟
    const currentHour = now.getHours();
    const currentMinute = now.getMinutes();
    // 构建目标时间范围的开始和结束时间
    const targetStart = new Date(now);
    targetStart.setHours(hour - 1, 30, 0, 0); 
    const targetEnd = new Date(now);
    targetEnd.setHours(hour, 50, 0, 0); 
    // 比较当前时间是否在目标时间范围内
    return now >= targetStart && now <= targetEnd;
  },
  getFirstAndLastDayOfMonth(date) {
    let firstDay = new Date(date.getFullYear(), date.getMonth(), 1);
    let lastDay = new Date(date.getFullYear(), date.getMonth() + 1, 0);
    return {
        firstDay: firstDay,
        lastDay: lastDay
    };
  },
  // 是否展示底部tab
  toggleTabBar(show){
    this.setData({showTabBar: show})
  },
  
  
})
function alert(text) {
  wx.showToast({
    title: text,
    icon: 'none'
  })
}
function jsonToAscii(jsonObj) {
  // 将JSON对象转换为字符串
  const jsonString = JSON.stringify(jsonObj);
  // 将字符串转换为ASCII码数组
  const asciiArray = jsonString.split('').map(char => char.charCodeAt(0));
  // 返回ASCII码数组
  return asciiArray;
}
function buildUrl(base, params) {
  const encodedParams = Object.entries(params)
    .map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(value)}`)
    .join('&');
  return `${base}?${encodedParams}`;
}
function buildUrl_n(base, params) {
  const encodedParams = Object.entries(params)
    .map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(typeof value === 'object' && value !== null ? JSON.stringify(value) : (value || ''))}`)
    .join('&');
  return `${base}?${encodedParams}`;
}



