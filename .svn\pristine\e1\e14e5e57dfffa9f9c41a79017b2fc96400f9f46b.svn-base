/* view {
box-sizing: border-box;
} */

.scroll-box  {
  width: 100%;
  height: 100%;
}

.coupon {
  margin: 10px;
  width: 95%;
  height: 100px;
  background-color: #fff;
  align-items: center;
  box-sizing: border-box;
  padding: 10px;
  /* padding: 5px 10px; */
}
.content {
  display: inline-flex;
  height: 100%;
}
.coupon .item {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin:  0 5px;
  width: 150px;
  height: 100%;
  background-color: #fff1f0;
  border-radius: 5px;
}
.text-1 {
  color: #fd1a4b;
}
.text-2 {
  font-size: 13px;
  color: #504b4b;
}
.text-3 {
  font-size: 10px;
  color: #9b9693;
}
.btn {
  margin-top: 5px;
  width: 100px;
  height: 25px;
  background-color: #fe1a4b;
  border-radius: 12.5px;
  text-align: center;
  line-height: 25px;
  font-size: 13px;
  color: #fff;
}
.grey{
  background-color:#999 ;
}