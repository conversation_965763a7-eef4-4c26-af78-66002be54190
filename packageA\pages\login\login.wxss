/* pages/publicPages/login/login.wxss */



.container {
  padding: 0 60rpx;
}

.logoBox {
  padding: 80rpx 0 48rpx;
  border-bottom: 1rpx solid #e3e3e3;
  margin-bottom: 72rpx;
  text-align: center;
}

.logoBox .imgBox {
  width: 190rpx;
  height: 190rpx;
  border: 2px solid #fff;
  margin: 0rpx auto 0;
  border-radius: 50%;
  overflow: hidden;
  box-shadow: 1px 0px 5px rgba(50, 50, 50, 0.3);
}

.auth-title {
  color: #585858;
  font-size: 40rpx;
  margin-bottom: 40rpx;
  text-align: center;
}

.agreementBox{
  width: 100%;
  margin-bottom: 40rpx;
  align-items: flex-end;
}
.wx-checkbox-input {
  width: 30rpx !important;
  height: 30rpx !important;
}
.checkText{
  font-size: 14px;
  color: #A3A3A3;
}
.checkText>span{
  color: #0091ff;
}
.login-btn {
  width: 220rpx;
  height: 80rpx;
  line-height: 80rpx;
  color: #fff;
  font-size: 11pt;
  border-radius: 999rpx;
}
.login-btn::after {
  border: none;
  border-radius: 0;
}
.backBtn{
  background: #FF6666;
}
.logBtn{
  background-color: var(--mainColor);
}
.logBtn[disabled]:not([type]){
  background-color: var(--mainColor);
  color: #ffffff;
}



/* 遮罩的css */
/* 手机号 */
.maskBg{
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  z-index: 99;  
}
.setInfoBox{
  width: 80%;
  height: 900rpx;
  background: #fff;
  border-radius: 30rpx;
  margin: 50rpx auto;
  max-height: 90vh;
  overflow-y: auto;
}
.setInfoBox>.title{
  width: 100%;
  height: 100rpx;
  text-align: center;
  font-size: 32rpx;
  color: #666666;
  line-height: 100rpx;
  border-bottom: 1rpx solid #e7e7e7;
}
.content{
  width: 100%;
  height: calc(100% - 101rpx);
  padding: 20rpx 0;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.imgBox{
  width: 120rpx;
  height: 120rpx;
  margin: 10rpx 0;
  border-radius: 50%;
  overflow: hidden;
}

.reminder_title{
  color: #ff6666;
  padding: 0rpx 30rpx;
}
.nameInput{
  width: 80%;
  margin: 10rpx auto;
  height: 70rpx;
  color: #fff;
  background-color: var(--mainColor);
  border-radius: 50rpx;
  padding-left: 50rpx;
  box-sizing: border-box;
}

.ask_btn{
  width: 230rpx;
  height: 80rpx;
  background: var(--mainColor);
  color: #fff;
  line-height: 80rpx;
  text-align: center;
  border-radius: 30rpx;
  margin-top: 20rpx;
  font-size: 28rpx;
  flex-shrink: 0;
}
.ask_btn::after {
  border: none;
  border-radius: 0;
}

/* 新增表单样式 */
.form-container {
  width: 100%;
  padding: 0;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.form-item {
  margin-bottom: 15rpx;
  width: 100%;
}

.form-item:first-child {
  margin-bottom: 20rpx;
}

.form-label {
  color: #fff;
  font-size: 22rpx;
  margin-bottom: 6rpx;
  padding-left: 30rpx;
}

.form-picker {
  width: 80%;
  margin: 0 auto;
  background-color: var(--mainColor);
  border-radius: 50rpx;
  height: 70rpx;
  display: flex;
  align-items: center;
  padding: 0 50rpx;
  box-sizing: border-box;
  z-index: 99;
}


.form-picker .picker-display {
  width: 100%;
  color: #fff;
  font-size: 26rpx;
  text-align: left;
}

.placeholder {
  color: rgba(255, 255, 255, 0.6);
}