<!--packageA/pages/myAppointment/myAppointment.wxml-->
<view class="mainPage">
  <view class="page-section">
    <view wx:for="{{switchList}}" wx:for-index="index" wx:key="index" class="switch {{item.flag ?'checked':''}}" data-index="{{index}}" data-id="{{item.code}}" bind:tap="showList2" wx:if="{{index <= 2}}">
      {{item.name}}
    </view>
  </view>

  <view class="page-section">
    <view wx:for="{{switchList}}" wx:for-index="index" wx:key="index" class="switch {{item.flag ?'checked':''}}" data-index="{{index}}" data-id="{{item.code}}" bind:tap="showList2"  wx:if="{{index >= 3}}">
      {{item.name}}
    </view>
  </view>
<view class="page-box ">
  <view class="line-box flex-row justify-between pickBox">
    <!-- 时间段 -->
    <view wx:if="{{isMonth}}" class="picker_group">
      <!-- <button class="time-btn" bindtap="minus1month">上一月</button> -->
      <picker mode="date" fields="month" value="{{selectedDate}}" bindchange="bindDateChange">
        <view class="picker time-picker-btn flex-row align-center">
          <image class="icon" src="/assets/icon/time_b.png"></image>
          <text>{{selectedDate}}</text>
        </view>
      </picker>
      <!-- <button class="time-btn" bindtap="add1month">下一月</button> -->
    </view>
    <view wx:else style="pointer-events: none;">
      <image style="width: 33rpx;height: 33rpx;margin-right: 16rpx;" src="/assets/icon/time_b.png"></image>
      <text style="font-weight: 600;font-size: 32rpx;color: #5e9df5;">所有日期</text>
    </view>
    <view class="picker_group end ">
      <!-- <picker style="width: 100%;" range="{{machineList}}" value="{{currentMachine}}" bindchange="bindMachineChange" range-key="name">
        <view class="picker object-picker-btn flex-row align-center">
          <text>{{machineList[currentMachine].name}}</text>
          <image class="icon" src='/assets/icon/arrow_right.png'></image>
        </view>
      </picker> -->
      <view bind:tap="changeAll">{{changeName}}</view>
      <image class="icon" src='/assets/icon/arrow_right.png'></image>
    </view>
  </view>
</view>
<!-- list -->
  <view style="height: 100%;">
    <!-- <scroll-view
      scroll-y
      refresher-enabled="true"
      bindrefresherrefresh="refresh"
      refresher-triggered="{{isRefreshing}}"
      bindscroll="scrollHandle"
      scroll-with-animation="true"
      lower-threshold="{{10}}"
      bindscrolltolower="loadMore"
      style="height: 100%;"
    > -->
      <view class="noText" wx:if="{{isNoData}}">没有获取到数据</view>
      <view class="itemBox page-box" wx:else>
        <!-- <view class="item flex-column justify-around" wx:for="{{appointmentList}}" wx:key="memberTrainID" bindtap="href" data-item="{{item}}"> -->
        <!-- <view class="item-title" wx:if="{{type==1}}">{{item.trainBusinessName}}</view>
          <view class="item-title" wx:elif="{{type==2}}">{{item.trainCourseName}}</view>
          <view class="item-title" wx:elif="{{type==3}}">{{item.trainHourName}}</view>

          <view wx:if="{{timeType==1}}">
            <view class="item-text">培训开始日期：{{item.beginDateStr}}</view>
            <view class="item-text">培训结束日期：{{item.endDateStr}}</view>
          </view>
          <view wx:elif="{{timeType==2}}">
            <view class="item-text">上课日期：{{item.beginDateStr}}</view></view>
          <view wx:elif="{{timeType==3}}">
            <view class="item-text">上课日期：{{item.beginDateStr}}</view>
            <view class="item-text">结束日期：{{item.endDateStr}}</view>
          </view>

          <view class="item-text">报名时间：{{item.applyTimeStr}}</view> -->
        <!-- <view class="item-text">价格：{{item.priceCash}}</view>
          <view class="item-text">付款状态：{{item.payStatusText}}</view> -->
        <!-- <view class="item-text">状态：{{item.statusText}}</view>
          <view class="item-text" style="text-decoration: underline;" data-id="{{item.appointmentID}}" bind:tap="deleteOneAppointment">删除</view>  -->
        <!-- </view> -->
        <view class="card" wx:for="{{showList}}" wx:key="appointmentID" data-id="{{item.appointmentID}}" wx:for-index="index">
          <view class="item-card">
            <view class="flex-row align-center info-title-box">
              <view class="object-name">{{item.address?item.address:""}}</view>
              <view class="tag" wx:if="{{item.workTypeStr}}">
              {{ item.workTypeStr? item.workTypeStr:""}}
              </view>
              <!-- status 1 -->
              <view class="tag new" wx:if="{{item.status===1}}" >
                <image class="icon" src="/assets/icon/tag_new.png"></image>
              </view>
              <view wx:else class="tag" style="background-color: rgb(216, 215, 215);">{{item.statusStr?item.statusStr:""}}</view>
              
              <!-- <view class="info" wx:if="{{item.orderSeq}}">{{item.orderSeq?item.orderSeq:""}}</view> -->
     
            </view>
            <view class="flex-row align-center info-box">
              <view style="margin-right: 26rpx;">
                <view class="gray-text">实训舱</view>
                <view class="">{{item.objectName?item.objectName:""}}</view>
              </view>
              <view>
                <view class="gray-text">预约时段</view>
                <view>
                  <text style="margin-right: 10rpx;">{{item.openDateStr?item.openDateStr:""}}</text>
                  <text >{{item.timeIntervalName?item.timeIntervalName:""}}</text>
                </view>
              </view>
            </view>
            <view class="info-btn-box align-center flex-row justify-between" style="position:relative;">
              <view class="gray-text2">
                <text>预约创建时间：<text>{{item.createdTimeString}}</text></text>
              </view>
              <view class="flex-row align-center">
                <view wx:if="{{item.status!==11}}" class="btn serviceBtn" data-id="{{item.appointmentID}}" bind:tap="deleteOneAppointment">取消</view>
                <view wx:if="{{item.status===11}}" class="btn videoBtn" bindtap="navigateToVideo" data-id="{{item.appointmentID}}">观看视频</view>
                <view wx:if="{{item.status===11}}" class="btn envScoreBtn" bindtap="showEnvScore" data-id="{{item.appointmentID}}">环境评分</view>
              </view>
            </view>
          </view>
        </view>
         
        <!-- 分页组件 -->
        <!--     <view class="page_div">
                <view class="page_sum">共{{pagetotal}}页</view>
                <view class="page_prev" bindtap="prevFn">上一页</view>
                <view class="page_number_div">
                    <input value="{{pageNumber}}" bindinput="inputValue" data-name="pageNumber"></input>
                    <view class="pageGo" bindtap="pageGo">Go</view>
                  </view>
                <view class="page_next" bindtap="nextFn">下一页</view>
        </view> -->
      </view>
    <!-- </scroll-view> -->
  </view>

</view>
<simple-dialog show="{{showEnvScoreDialog}}" title="环境评分" list="{{envScoreList}}" bind:close="onCloseEnvScoreDialog" />