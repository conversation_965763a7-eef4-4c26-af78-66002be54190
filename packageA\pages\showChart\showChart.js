// pages/mine/aboutUs/aboutUs.js

var ports = require("../../../utils/ports.js")
var app = getApp()

Page({
  data: {
    logoimg: app.logoimg,
    version: {},
    id: '',
    sensorList: [],
    isOpenAirCondition: false,
    array: ['自动', '制冷', '制热', '除湿', '送风', '舒适'],
    windSpeedList: ['自动', '1', '2', '3'],
    index: 0,
    wIndex: 0,
    num: 16,
    i: 0,
    airconditionID:'',
  },
  async onLoad(options) {
    let seq
    if(options.seq == 'undefined' || options.seq == 'null'){
      seq = 1
    }else{
      seq = options.seq
    }
    this.setData({
      id: options.id,
      seq: seq
    })
    await this.getSensor()
    this.sensorTimer = setInterval(async () => {
      await this.getSensor();
    }, 5000);
  },
  getSensor() {
    wx.stopPullDownRefresh()
    var that = this;
    // wx.showLoading()
    ports.ModuleMachine.getSensorList({
      machineID: this.data.id,
      pageNumber: 99,
      isControl:1,
    }).then(res => {
      wx.hideLoading()
      if (res.data.header.code == 0) {
        let rows1 = res.data.body.data.rows;
        let rows=[]
        let list = [];
          // 创建一个包含所有异步操作的 Promise 数组
        const promises = rows1.map(sensor => {
          return ports.ModuleMachine.getSensorDefineDetail({
            sensorDefineID: sensor.sensorDefineID
          }).then(resp1 => {
            if (resp1.data.body.data.isControl==1) {
              rows.push(sensor);
            }
          });
        });
        Promise.all(promises).then(() => {
          rows.sort(function (a, b) {
            let nameA = a.name.toUpperCase(); // 不区分大小写
            let nameB = b.name.toUpperCase();
            if (nameA < nameB) {
              return -1;
            }
            if (nameA > nameB) {
              return 1;
            }
            return 0;
          });
          rows.forEach((r)=>{
            if (r.code==="AirQualitySensor"||r.code==="all"||r.code==="FF9042869"||r.code==="FF9042867"||r.code==="FF9042863"||r.code==="FF8409638"||r.code==="BC7011597") 
            {}else {
              
              if (r.code.includes("SE")) {
                if (r.code===("SE_"+this.data.seq)||r.code===("SE"+this.data.seq+"_computer")) {
                  console.log(1)
                  r.isShow=true
                }else {
                  console.log(2)
                  r.isShow=false
                }
              } else {
                console.log(1)
                r.isShow=true
              }
              list.push(r)
            }
          })
          list.forEach((l)=>{
            if (l.code==="step") {
              l.order = 1
            }
            if (l.code==="GD_1") {
              l.order = 2
            }
            if (l.code==="OW_1") {
              l.order = 3
            }
            if (l.code==="SE1_computer") {
              l.order = 4
            }
            if (l.code==="SE2_computer") {
              l.order = 5
            }
            if (l.code==="SE3_computer") {
              l.order = 6
            }
            if (l.code==="SE_1") {
              l.order = 7
            }
            if (l.code==="SE_2") {
              l.order = 8
            }
            if (l.code==="SE_3") {
              l.order = 9
            }
            if (l.code==="Prac1_computer") {
              l.order = 10
            }
            if (l.code==="bed") {
              l.order = 11
            }
            if (l.code==="Ventilator") {
              l.order = 12
            }
            if (l.code==="door") {
              l.order = 13
            }
            if (l.code==="E_Curtain_1") {
              l.order = 14
            }
            if (l.code==="E_Curtain_2") {
              l.order = 15
            }
            if (l.code==="Aircondition") {
              l.order = 16
            }
          })
          list.sort((a, b) => a.order - b.order);
          if (list.length>0) {
            for (let i = 0;i < list.length;i ++) {
              // if(list[i].openStatus == 1) {
              //   list[i].isOn = true
              // } else {
              //   list[i].isOn = false
              // }
              // if(list[i].code=="E_Curtain_1"){
              //   console.log(list[i].code,list[i].openStatus);
              //   if(list[i].openStatus == 1) {
              //     list[i].isOn = true
              //   } else {
              //     list[i].isOn = false
              //   }
              // }else if(list[i].code=="E_Curtain_2"){
              //   console.log(list[i].code,list[i].openStatus);
              //   if(list[i].openStatus == 1) {
              //     list[i].isOn = true
              //   } else {
              //     list[i].isOn = false
              //   }
              // }

              // if(list[i].moveStatus==="on"||list[i].moveStatus==="bottom"||list[i].moveStatus==="expanded"||list[i].moveStatus==="自动") {
              //   list[i].isOn = true
              // } 
              // else {
              //   list[i].isOn = false
              // } 
              if(list[i].code==="Aircondition"){
                if(list[i].moveStatus!=="off") {
                  list[i].isOn = true
                } else {
                  list[i].isOn = false
                }
                this.setData({
                  num: parseInt(list[i].temper),
                  airconditionID: list[i].sensorID,
                  i: i,
                })
              }
          }
        }
          that.setData({
            sensorList: list,
          })
          let f = this.data.sensorList.find(o => o.code === 'Aircondition').isOn
          this.setData({
            isOpenAirCondition: f
          })
        })
      } else return console.log('更新失败')
    })
  },
  bindPickerChange: function(e) {
    console.log('picker发送选择改变，携带值为', e.detail.value)
    this.setData({
      index: e.detail.value
    })
    this.getCommand(this.data.airconditionID,this.data.array[e.detail.value],this.data.i);
  },
  bindPickerWindChange: function(e) {
    console.log('picker发送选择改变，携带值为', e.detail.value)
    this.setData({
      wIndex: e.detail.value
    })
    this.getCommand(this.data.airconditionID,"w"+this.data.windSpeedList[e.detail.value],this.data.i);
  },
  bindMinus: function() { // 减少数值
    if (this.data.num-1<16) {
      wx.showToast({
        icon:'none',
        title: '空调的最低温度只能设置到16℃',
      })
      return;
    }
    this.setData({ num: this.data.num - 1 });
    this.getCommand(this.data.airconditionID,"t"+this.data.num,this.data.i);
  },
  bindPlus: function() { // 增加数值
    if (this.data.num+1>30) {
      wx.showToast({
        icon:'none',
        title: '空调的最高温度只能设置到30℃',
      })
      return;
    }
    this.setData({ num: this.data.num + 1 });
    this.getCommand(this.data.airconditionID,"t"+this.data.num,this.data.i);
  },
  bindManual: function(e) { // 处理输入框变化
    let val = e.detail.value
    if (val<16) {
      val=16
    }
    if (val<30) {
      val=30
    }
    this.setData({ num: val });
    this.getCommand(this.data.airconditionID,"t"+this.data.num,this.data.i);
  },
  async action(e) {
    // clearInterval(this.sensorTimer);
    let i = e.currentTarget.dataset.index;
    let flag = !this.data.sensorList[i].isOn;
    let sensorCode = this.data.sensorList[i].code;
    let sensorID = this.data.sensorList[i].sensorID;
    let moveStatus = this.data.sensorList[i].moveStatus;
    let commandValue;
    let cc = await ports.ModuleAll.getOneSensorDetail({
      sensorID,
      sensorCode
    })
    if (sensorCode==="SE_1"||sensorCode==="SE_2"||sensorCode==="SE_3"||sensorCode==="all"||sensorCode==="GD_1"||sensorCode==="OW_1"||sensorCode==="Aircondition"||sensorCode==="Ventilator"||sensorCode==="SE1_computer"||sensorCode==="SE2_computer"||sensorCode==="SE3_computer"||sensorCode==="Prac1_computer") {
      if(cc.data.body.data.openStatus == 0){
          commandValue = "on"
        }else{
          commandValue = "off"
        }
      // if (flag) {
      //   commandValue = "on"
      // } else {
      //   commandValue = "off"
      // }
    }
    if (sensorCode==="E_Curtain_1"||sensorCode==="E_Curtain_2") {
      if(moveStatus == 'falling' || moveStatus ==  'rising'){
        wx.showToast({
          title: '正在运行',
          icon:'none'
        })
        return
      }

      if(moveStatus == 'top' && cc.data.body.data.openStatus == 0){
        commandValue = "falling"
      }else{
        commandValue = "rising"
      }
      // if (flag) {
      //   commandValue = "falling"
      // } else {
      //   commandValue = "rising"
      // }
    }
    if (sensorCode==="door") {
      if (flag) {
        commandValue = "unlock"
      } else {
        commandValue = "lock"
      }
    }
    if (sensorCode==="bed"||sensorCode==="step") {
      if(moveStatus == 'expanding' || moveStatus ==  'folding'){
        wx.showToast({
          title: '正在运行',
          icon:'none'
        })
        return
      }
      if(moveStatus == 'folded' && cc.data.body.data.openStatus == 0){
        commandValue = "expanding"
      }else{
        commandValue = "folding"
      }
      // if (flag) {
      //   commandValue = "expanding"
      // } else {
      //   commandValue = "folding"
      // }
    }
    
    if (commandValue) {
      this.getCommand(sensorID,commandValue,i,sensorCode)

    } else {
      wx.showToast({
        icon:'none',
        title: '该组件尚不支持远程控制',
      })
    }
    
  },
  async getCommand(sensorID,commandValue,i,sensorCode) {
    let that = this
    let str = "sensorList["+ i + "].isOn"; 
    let flag = !this.data.sensorList[i].isOn; 
    let params = {
      machineID: this.data.id,
      // sensorID,
      code:sensorCode,
      isControl:1,
      commandValue,
    }
    if (this.data.sensorList[i].code==="Aircondition") {
      this.setData({
        airconditionID: sensorID,
        i: i,
      })
      if (commandValue==="on"||commandValue==="off") {
        params.commandName="power";
      }
      if (commandValue==="lon"||commandValue==="loff") {
        params.commandName="light";
        if (commandValue==="lon") {
          params.commandValue="on"
        }
        if (commandValue==="loff") {
          params.commandValue="off"
        }
      }
      if (commandValue==="自动"||commandValue==="制冷"||commandValue==="制热"||commandValue==="除湿"||commandValue==="送风"||commandValue==="舒适") {
        params.commandName="mode";
      }
      if (commandValue.charAt(0)==="t") {
        let str =commandValue.slice(1);
        params.commandValue = parseInt(str)
        params.commandName="temper";
        params.parameter1Min=16;
        params.parameter1Miax=30;
      }
      if (commandValue.charAt(0)==="w") {
        params.commandValue=commandValue.slice(1);
        params.commandName="windspeed";
      }
    }
    wx.showLoading({mask: true})
    //先获取实训仓的 所有传感器列表
    ports.ModuleMachine.getOneMachineCommandForSanOne(params).then(async res => {
      setTimeout(()=>{
        wx.hideLoading()
      },1000)
      console.log(res,"操作命令")
      if (res.data.header.code === 0) {
        if (!params.commandName||params.commandName==="power") {
          this.setData({
            // [str]: flag
          })
        }
        let f = this.data.sensorList.find(o => o.code === 'Aircondition').isOn
        this.setData({
          isOpenAirCondition: f
        })
      } else {
        wx.showToast({
          icon:'none',
          title: '远程控制失败，请重试',
        })
      }
    })

  },
  onUnload() {
    // 页面退出时清除定时器
    clearInterval(this.sensorTimer);
  },
    /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.getSensor()

  },
})