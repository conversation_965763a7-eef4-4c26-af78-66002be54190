// packageA/pages/machineListNew/machineListNew.js
var ports = require("../../../utils/ports")
var app = getApp()
var longitude = require('@/utils/longitude.js')
Page({

  /**
   * 页面的初始数据
   */
  data: {
    machineList:[],
    keyWords:''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.getMachineList()
  },
  clickArticle() {
    let url='/packageA/pages/agreement/agreement?articleID=' + app.articleID;
    wx.navigateTo({
      url: url,
    })
  },
  // 输入框内容变化时触发
  onSearchInput(e) {
    console.log(e);
    this.setData({
      keyWords: e.detail.value
    });
  },
  getMachineList() {
    console.log(this.data.keyWords);
    // debugger;
    let params = {
      applicationID: app.applicationID,
      companyID: app.companyID,
      pageNumber: 999,
      keyWords:this.data.keyWords===''||this.data.keyWords===null||this.data.keyWords===undefined?'':this.data.keyWords
      // customerCompanyID:app.companyID
    }
    ports.ModuleAll.getMachineList(params).then(res => {
      wx.stopPullDownRefresh()
      let list = res.data.body.data.rows || []
      let now = Date.now();
      list.forEach((item, index) => {
        this.getDistance(item.mapX, item.mapY, index);
        this.getMemberTrain(item.trainBusinessID, index);

        const beginTs = item.beginDateStr
          ? new Date(item.beginDateStr).getTime()
          : -Infinity;
        const hasEnd = !!item.endDateStr;
        const endTs = hasEnd
          ? new Date(item.endDateStr).getTime()
          : null;

        // 只有 begin 且无 end
        if (now < beginTs) {
          item.currentStatus = false;
        } else if (hasEnd && now > endTs) {
          item.currentStatus = false;
        } else {
          item.currentStatus = true;
        }
      });
      this.setData({
        machineList: list
      })
      // console.log(this.data.machineList);
    })
  },
  getMemberTrain(id,i) {
    let params = {
      applicationID: app.applicationID,
      sessionID: wx.getStorageSync('USER_SESSIONID'),
      trainBusinessID: id,
      joinMemberID: wx.getStorageSync('USER_MEMBERID')
    }
    ports.ModuleAll.getMemberTrainList(params).then(res => {
      
      let list = res.data.body.data.rows || []
      let tempStr = "machineList["+ i + "].isShow"; 
      this.setData({
        [tempStr]: list.length === 0 ? false : true
      })
    })
  },
  getDistance(mapX,mapY,i) {
    function calculateDistance(lat1, lng1, lat2, lng2) {
        var that = this;
        let rad1 = lat1 * Math.PI / 180.0;
        let rad2 = lat2 * Math.PI / 180.0;
        let a = rad1 - rad2;
        let b = lng1 * Math.PI / 180.0 - lng2 * Math.PI / 180.0;
        let s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2) + Math.cos(rad1) * Math.cos(
          rad2) * Math.pow(
          Math.sin(b / 2), 2)));
        s = s * 6378.137;
        s = Math.round(s * 10000) / 10000;
        s = s.toString();
        s = s.substring(0, s.indexOf('.') + 2);
        console.log('距离：', s);
        return s; //返回距离
    } 
    function formatDistance(distanceInMeters) {
      if (distanceInMeters >= 1000) {
        // 将距离从米转换为千米，并保留两位小数
        const distanceInKilometers = (distanceInMeters / 1000).toFixed(2);
        return `${distanceInKilometers} 千米`;
      } else {
        return `${distanceInMeters} 米`;
      }
    }
    
    wx.getLocation({
      type: 'gcj02', // 默认为 wgs84 返回 gps 坐标，gcj02 返回可用于 wx.openLocation 的坐标
      altitude: true, // true 会返回高度信息
      isHighAccuracy: true, //开启高精度定位
      highAccuracyExpireTime: 3100, //高精度定位超时时间(ms)
      success: (res)=> {
        console.log(res, '获取位置成功');
        console.log(this.info, '获取位置成功');
        // let distance = calculateDistance(this.data.info.mapX,this.data.info.mapY, res.latitude, res.longitude)
        let distance = longitude.calculateDistance(mapY,mapX, res.latitude, res.longitude)
        console.log(distance,"距离")
        let tempStr = "machineList["+ i + "].distance"; 
        let tempStrReal = "machineList["+ i + "].distanceReal"; 
        this.setData({
          [tempStr]: formatDistance(distance),
          [tempStrReal]:distance
        })
        // let distance = longitude.calculateDistance(this.info.mapX, this.info.mapY, res.latitude, res.longitude)
      }
    })

  },
  href(e){
    let id = e.currentTarget.dataset.id
    let url = `/packageA/pages/machineDetail/machineDetail?id=${id}`
    // let url = `/packageA/pages/machineDetail/machineDetail?scene=19d28aa3912b4e29989257c48594ebcb`
    wx.navigateTo({
      url,
    })
  },
  appointment(e){
    let item = e.currentTarget.dataset.item
    console.log(item,'+++');
    let url = `/packageA/pages/appointment/appointment?machineID=${item.id}&trainBusinessID=${item.trainBusinessID}&machineName=${item.name}`
    wx.navigateTo({
      url,
    })
  },
  toVideo(e){
    let url = `/packageA/pages/caseList/caseList?navigatorID=${app.videoID}`
      wx.navigateTo({
        url
      })
  },
  checkMachine(e){
    let id = e.currentTarget.dataset.id
    let machineName = e.currentTarget.dataset.name
    let code = e.currentTarget.dataset.code
    let address = e.currentTarget.dataset.address

    let machineDistance = e.currentTarget.dataset.distance
    let bizType=e.currentTarget.dataset.biztype
    let checkedMachine = {
      id:id,
      machineName:machineName,
      address: address,
      code,
      distance:machineDistance,
      bizType,
    }
    wx.showModal({
      title: '提示',
      content: '是否选择'+machineName+'?',
      complete: (res) => {
        if (res.cancel) {
          console.log('取消选择');
        }
    
        if (res.confirm) {
          wx.setStorageSync('checkedMachine', checkedMachine)
          wx.showToast({
            title: '选择成功',
            duration: 2000
          })
          wx.navigateBack()
        }
      }
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.getMachineList()
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})