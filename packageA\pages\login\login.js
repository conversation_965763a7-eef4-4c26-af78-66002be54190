// pages/publicPages/login/login.js
var ports = require("../../../utils/ports");
var utils  = require("../../../utils/util.js");
var app = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    member: {
      shortName: '',
      gender: '', // 性别：1-男，2-女
      birthday: '', // 生日
      provinceID: '', // 省ID
      provinceName: '', // 省名称
      cityID: '', // 市ID
      cityName: '', // 市名称
      countyID: '', // 县ID
      countyName: '', // 县名称
    },
    weixinAppTitle: app.weixinAppTitle,
    logoimg: app.logoimg,
    mainColor: `--mainColor:${app.mainColor}`,
    agreementFlag: false,
    disabled: true,
    showRegister: false,
    // 性别选择相关
    genderArray: ['男', '女'],
    genderIndex: 0, // 默认选择第一个选项（男）
    // 生日选择相关
    startTime: '1950-01-01',
    endTime: '2000-01-01',
    // 省市县选择相关
    Myshenglsit: [], // 省市县数据
    currentPickerValue: [0, 0, 0], // 当前选择的索引
    shengobj: '', // 显示的省市县字符串
    municipalities: ['上海市', '北京市', '天津市',  '重庆市'], // 直辖市列表
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    if (!wx.getStorageSync('agreementID')) this.signOneBlankAgreement();
    // this.initDateRange();
    this.getshengList();
    // 初始化默认性别
    this.setData({
      ['member.gender']: app.man // 默认选择男性
    });
  },

  // 初始化日期范围
  initDateRange() {
    const currentDate = new Date();
    const endTime = currentDate.getFullYear() + '-' +
                   String(currentDate.getMonth() + 1).padStart(2, '0') + '-' +
                   String(currentDate.getDate()).padStart(2, '0');
    this.setData({
      endTime: endTime
    });
  },
  //授权登录
  getPhoneNumber(e) {
    var that = this
    wx.showToast({
      title: '获取中',
      icon: 'loading',
      mask: true,
      duration: 1000
    })
    if (e.detail.errMsg == "getPhoneNumber:ok") {
      that.ADC(e);
    } else {
      wx.showToast({
        title: '获取失败',
        icon: 'none'
      })
    }
  },
  async ADC(e) {
    var that = this
    let resWXLog = await ports.ModuleAll.loginWX()
    let params = {
      'appID': app.appid,
      'encryptedData': e.detail.encryptedData,
      'iv': e.detail.iv,
      'js_code': resWXLog.code,
    }
    console.log(e, params);
    let res = await ports.ModuleAll.getWeixinMemberPhone(params)
    wx.setStorageSync('USER_PHONE', res.data.body.phoneNumber)
    let resTwo = await ports.ModuleAll.searchMemberByPhone({
      applicationID: app.applicationID,
      phone: res.data.body.phoneNumber
    })
    let resData = resTwo.data.body
    if (resData.memberID) {
      var phone = resData.phone
      that.toLogin(phone)
    } else {
      that.setData({
        showRegister: true
      })
    }
  },
  // 登录
  async toLogin(phone) {
    var that = this
    try {
      let params = {
        deviceID: wx.getStorageSync('deviceID'),
        siteID: app.siteID,
        phone,
        verifyCode: 999999,
        password: 111111,
        loginName: phone,
      }
      let res = await ports.ModuleAll.memberLogin(params)
      if (res.data.header.code !== 0) {
        return wx.showToast({
          title: '登录出错',
          icon: 'none'
        })
      }
      setTimeout(async() => {
      var data = res.data.body;
      wx.setStorageSync("USER_SESSIONID", data.sessionID)
      wx.setStorageSync("USER_MEMBERID", data.memberID)

      if(!wx.getStorageSync('USER_OPENID')){
        const res =  await ports.ModuleAll.loginWX()
        console.log(res)
        const res2 = await ports.ModuleAll.getWeiXinAppOpenId({
          publicNo: app.publicNo,
          js_code: res.code,
        })
  
        if (res2.data.header.code !== 0) throw(res2.data.header.msg) //错误返回
        wx.setStorageSync('USER_OPENID', res2?.data?.body?.openID)
        wx.setStorageSync('USER_UNIONID', res2?.data?.body?.unionid)
        wx.setStorageSync('LOGINTIME',new Date().getTime())

      }
      utils.queryStudent();
      const ADC = await ports.ModuleAll.bandingMemberByPhone({
        applicationID: app.applicationID,
        phone,
        openID: wx.getStorageSync('USER_OPENID'),
        unionID: wx.getStorageSync('USER_UNIONID'),
      })
      console.log('绑定成功', ADC);
      that.bindingAgreement();

      let resOne = await ports.ModuleAll.getOneMemberDetail({
        sessionID: data.sessionID,
        memberID: data.memberID
      })
      if (resOne.data.header.code !== 0) {
        return wx.showToast({
          title: '登录失败',
          icon: 'none'
        })
      }
      wx.setStorageSync("USER_MEMBER", resOne.data.body)
      wx.setStorageSync('recommendID', resOne.data.body.recommendID)
      that.queryWXMember();
      wx.showToast({
        title: '登录成功',
      })
      // 检查是否需要返回上一层页面
      const shouldReturnBack = wx.getStorageSync('shouldReturnBack')
      const previousUrl = wx.getStorageSync('previousUrl')

      setTimeout(() => {
        if (previousUrl) {
          console.log(previousUrl)
        }
        wx.navigateBack({
          delta: 1,
          success: function () {
            // 清除存储的标识和路径信息
            wx.removeStorageSync('previousUrl');
            wx.removeStorageSync('shouldReturnBack');
          }
        });
      }, 200)
    }, 5000);
      
    } catch (error) {
      throw (error)
    }
  },
  //签署空白协议
  signOneBlankAgreement() {
    let params = {
      agreementDefineID: app.agreementDefineID
    }
    ports.ModuleAll.signOneBlankAgreement(params).then(res => {
      wx.setStorageSync('agreementID', res.data.body.agreementID)
    })
  },
  //空白协议绑定
  bindingAgreement() {
    ports.ModuleAll.registerSignOneAgreement({
      sessionID: wx.getStorageSync('USER_SESSIONID'),
      agreementID: wx.getStorageSync('agreementID')
    }).then(res => {
      console.log('绑定协议成功', res);
      wx.removeStorageSync('agreementID')
    })
  },
  checkboxChange(e) {
    let val = e.detail.value
    if (val.length > 0) {
      this.setData({
        agreementFlag: true,
        disabled: false
      })
    } else {
      this.setData({
        agreementFlag: false,
        disabled: true
      })
    }

  },
  href(e) {
    var type = e.currentTarget.dataset.type
    console.log(type);
    //0是隐私协议  1是注册协议
    if (type == 0) {
      wx.navigateTo({
        url: '/packageA/pages/agreement/agreement?show=1&articleID=' + app.privacyAgreementID,
      })
    } else {
      wx.navigateTo({
        url: '/packageA/pages/agreement/agreement?show=1&articleID=' + app.registerAgreementID,
      })
    }
  },
  backHome() {
    wx.navigateBack()
    // wx.switchTab({
    //   url: "/pages/index/index"
    // })
  },
  bindNameInput(e) {
    var member = this.data.member;
    member.shortName = e.detail.value.trim();
    this.setData({
      member
    })
  },

  // 性别选择
  bindGenderChange(e) {
    const index = e.detail.value;
    const gender = index == 0 ? app.man : app.men; // 1-男，2-女
    console.log("gender", gender);
    
    var member = this.data.member;
    member.gender = gender;
    this.setData({
      genderIndex: index,
      member
    });
  },

  // 生日选择
  bindBirthdayChange(e) {
    var member = this.data.member;
    member.birthday = e.detail.value;
    this.setData({
      member
    });
  },
  async toRegister() {
    var that = this
    if (!that.data.member.shortName) {
      return wx.showToast({
        title: '昵称不能为空',
        icon: 'none'
      })
    }

    // 验证必填字段
    if (!that.data.member.gender) {
      return wx.showToast({
        title: '请选择性别',
        icon: 'none'
      })
    }

    if (!that.data.member.birthday) {
      return wx.showToast({
        title: '请选择生日',
        icon: 'none'
      })
    }

    if (!that.data.member.provinceID || !that.data.member.cityID || !that.data.member.countyID) {
      return wx.showToast({
        title: '请选择所在地区',
        icon: 'none'
      })
    }
    wx.showLoading({
      title: '注册中',
    })
    let phone = wx.getStorageSync('USER_PHONE')
    let res = await ports.ModuleAll.loginWX();
    let resOne = await ports.ModuleAll.getWeiXinAppOpenId({
      publicNo: app.publicNo,
      js_code: res.code,
    })
    var openID = resOne.data.body.openID
    var unionID = resOne.data.body.unionid
    let params = {
      deviceID: wx.getStorageSync('deviceID'),
      siteID: app.siteID,
      phone: phone,
      loginName: phone,
      shortName: that.data.member.shortName,
      name: that.data.member.shortName,
      verifyCode: 999999,
      password: 111111,
      bizBlock: 0,
      recommendCode: wx.getStorageSync('recommendCode') || '',
      recommendMemberID: wx.getStorageSync('recommendMemberID') ||  '',
      openID: openID,
      unionID: unionID, //uniid   不一定每个小程序都有   没有就注释掉这个参数
      // 新增字段
      titleID: that.data.member.gender, // 性别：1-男，2-女
      birthday: that.data.member.birthday, // 生日
      shengID: that.data.member.provinceID, // 省ID
      shengName: that.data.member.provinceName, // 省名称
      shiID: that.data.member.cityID, // 市ID
      shiName: that.data.member.cityName, // 市名称
      xianID: that.data.member.countyID, // 县ID
      xianName: that.data.member.countyName, // 县名称
    }
    console.log('openID',wx.getStorageSync('USER_OPENID'));
    console.log(wx.getStorageSync('recommendMemberID'),'=======');
    console.log(params,'++++++');
    try {
      let res = await ports.ModuleAll.memberPhoneRegister(params)
      if (res.data.header.code !== 0) {
        return wx.showToast({
          title: res.data.header.msg || '注册失败',
          icon: 'none'
        })
      }

      var data = res.data.body;
      wx.setStorageSync("USER_SESSIONID", data.sessionID)
      wx.setStorageSync("USER_MEMBERID", data.memberID)
      wx.setStorageSync('USER_OPENID', openID)
      wx.setStorageSync('USER_UNIONID', unionID)
      that.bindingAgreement();
      let resADC = await ports.ModuleAll.getOneMemberDetail({
        sessionID: data.sessionID,
        memberID: data.memberID
      })
      if (resADC.data.header.code !== 0) {
        return wx.showToast({
          title: '登录失败',
          icon: 'none'
        })
      }
      wx.setStorageSync("USER_MEMBER", resADC.data.body)
      wx.setStorageSync('recommendID', resADC.data.body.recommendID)
      utils.queryStudent();
      that.queryWXMember();
      if(wx.getStorageSync('recommendMemberID')){
        //如果有推荐人默认关注他
        ports.ModuleAll.submitOneMemberFollow({
          sessionID:wx.getStorageSync('USER_SESSIONID'),
          followID:wx.getStorageSync('recommendMemberID')
        }).then(followRes=>{
          console.log(followRes,'关注推荐会员');
        })

        // 发送注册成功通知消息给邀请人
        that.sendRegistrationNotification(resADC.data.body);
      }
      wx.hideLoading()

      wx.showToast({
        title: '注册成功',
      })
      // 检查是否需要返回上一层页面
      const shouldReturnBack = wx.getStorageSync('shouldReturnBack')
      const previousUrl = wx.getStorageSync('previousUrl')

      if (shouldReturnBack || previousUrl) {
        setTimeout(() => {
          if (previousUrl) {
            console.log(previousUrl)
          }
          wx.navigateBack({
            delta: 1,
            success: function () {
              // 清除存储的标识和路径信息
              wx.removeStorageSync('previousUrl');
              wx.removeStorageSync('shouldReturnBack');
            }
          });
        }, 200)
      } else {
        setTimeout(() => {
          // wx.switchTab({
          //   url: '/pages/memberSelf/memberSelf',
          // })
          wx.navigateBack()
        }, 200)
      }

    } catch (error) {
      throw (error)
    }
  },

  /**
   * 发送注册成功通知消息给邀请人
   * @param {Object} newUserInfo - 新注册用户信息
   */
  sendRegistrationNotification(newMembernfo) {
    const recommendMemberID = wx.getStorageSync('recommendMemberID');
    if (!recommendMemberID || !newMembernfo) {
      return;
    }

    try {
      // 生成注册成功消息内容
      const messageContent = ports.ModuleMessage.generateRegistrationSuccessMessage({
        newMemberName: newMembernfo.shortName || newMembernfo.name || '新会员'
      });

      // 发送注册成功通知消息
      ports.ModuleMessage.sendRegistrationSuccessMessage({
        senderMemberID: newMembernfo.memberID, // 新注册用户作为发送者
        receiverMemberID: recommendMemberID, // 邀请人作为接收者
        messageContent: messageContent
      }).then(res => {
        if (res.data.header.code === 0) {
          console.log('注册成功通知消息发送成功');
        } else {
          console.log('注册成功通知消息发送失败:', res.data.header.msg);
        }
      }).catch(error => {
        console.log('发送注册成功通知消息异常:', error);
      });
    } catch (error) {
      console.log('发送注册成功通知消息错误:', error);
    }
  },

  //查询微信小程序关注记录
  queryWXMember() {
    var that = this
    let params = {
      sessionID: wx.getStorageSync('USER_SESSIONID'),
      weixinAppID: app.weixinAppID,
      openid: wx.getStorageSync('USER_OPENID'),
      queryType: 1
    }
    ports.ModuleAll.queryWeixinAppMember(params).then(res => {
      console.log(res, 'res');
      if (res.data.body.data == null) {
        if (!wx.getStorageSync('weixinAppMemberID')) {
          that.creatWXMember();
        } else {
          that.updateWXMember();
        }
      } else {
        wx.setStorageSync('weixinAppMemberID', res.data.body.data.weixinAppMemberDetailID)
      }
    })
  },
  //创建微信小程序关注记录
  creatWXMember() {
    let params = {
      sessionID: wx.getStorageSync('USER_SESSIONID'),
      name: this.data.member.shortName + '小程序关注记录',
      openid: wx.getStorageSync('USER_OPENID'),
      weixinAppID: app.weixinAppID,
      weixinUnionID: wx.getStorageSync('USER_UNIONID') || '',
      siteID: app.siteID,
    }

    ports.ModuleAll.createOneWeixinAppMember(params).then(res => {
      console.log(res, '本地没有id创建关注记录');
      wx.removeStorageSync('recommendID')
      wx.setStorageSync('weixinAppMemberID', res.data.body.weixinAppMemberDetailID)
    })
  },
  //修改微信小程序关注记录
  updateWXMember() {
    let recommendID = wx.getStorageSync('recommendID')
    let params = {
      weixinAppMemberID: wx.getStorageSync('weixinAppMemberID'),
      sessionID: wx.getStorageSync('USER_SESSIONID'),
      name: this.data.member.shortName + '小程序关注记录',
      openid: wx.getStorageSync('USER_OPENID'),
      weixinUnionID: wx.getStorageSync('USER_UNIONID') || '',
      weixinAPPID: app.weixinAppID,
      phone: wx.getStorageSync('USER_PHONE'),
      deviceID: wx.getStorageSync('deviceID'),
      recommendMemberID: recommendID ? recommendID : '',
    }
    ports.ModuleAll.updateWeixinAppMember(params).then(res => {
      console.log(res);
      wx.removeStorageSync('recommendID')
    })
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },

  //获取省 
  getshengList() {
    var that = this
    var Myshenglsit = that.data.Myshenglsit
    ports.ModuleAll.getCityList({
      sessionID: wx.getStorageSync('USER_SESSIONID'),
      cityID: app.ChinaID,
      pageNumber: 9999,
      sortTypeName: 1,
      depth: 1
    }).then(res => {
      if (res.data.header.code === 0) {
        var shengList = res.data.body.data.rows
        
        // 对省份列表进行排序：优先直辖市，然后按字母顺序
        shengList.sort((a, b) => {
          const aIsMunicipality = that.data.municipalities.includes(a.name);
          const bIsMunicipality = that.data.municipalities.includes(b.name);
          
          // 如果都是直辖市，按直辖市列表中的顺序排序
          if (aIsMunicipality && bIsMunicipality) {
            return that.data.municipalities.indexOf(a.name) - that.data.municipalities.indexOf(b.name);
          }
          
          // 如果只有a是直辖市，a排在前面
          if (aIsMunicipality && !bIsMunicipality) {
            return -1;
          }
          
          // 如果只有b是直辖市，b排在前面
          if (!aIsMunicipality && bIsMunicipality) {
            return 1;
          }
          
          // 都不是直辖市，按字母顺序排序
          return a.name.localeCompare(b.name, 'zh-CN');
        });
        
        Myshenglsit.push(shengList)
        that.setData({
          Myshenglsit: Myshenglsit
        })
        console.log(Myshenglsit)
        
        // 检查第一个省份是否为直辖市
        const firstProvince = shengList[0];
        if (that.data.municipalities.includes(firstProvince.name)) {
          // 如果是直辖市，直接获取区县列表
          that.getMunicipalityDistricts(firstProvince.cityID, firstProvince.name);
        } else {
          // 普通省份，获取市级列表
          that.getshiList(shengList[0].cityID)
        }
      } else {
        wx.showToast({
          title: res.data.header.msg,
          icon: 'none',
        })
      }
    })
  },
  // 获取直辖市的区县列表
  getMunicipalityDistricts(cityID, cityName) {
    var that = this
    var Myshenglsit = that.data.Myshenglsit
    
    // 为直辖市创建市级列表（重复显示市名）
    const cityList = [{
      cityID: cityID,
      name: cityName
    }];
    
    // 清空第二级和第三级，重新设置
    Myshenglsit.splice(1, Myshenglsit.length - 1);
    
    // 将市级列表添加到第二级
    Myshenglsit.push(cityList);
    
    // 获取区县列表
    ports.ModuleAll.getCityList({
      sessionID: wx.getStorageSync('USER_SESSIONID'),
      cityID: cityID,
      pageNumber: 9999,
      sortTypeName: 1,
      depth: 1
    }).then(res => {
      if (res.data.header.code === 0) {
        var districtList = res.data.body.data.rows
        Myshenglsit.push(districtList)
        
        // 更新currentPickerValue，确保直辖市选择时状态正确
        let newPickerValue = [...that.data.currentPickerValue];
        newPickerValue[1] = 0; // 重置市级选择
        newPickerValue[2] = 0; // 重置县级选择
        
        that.setData({
          Myshenglsit: Myshenglsit,
          currentPickerValue: newPickerValue
        })
      } else {
        wx.showToast({
          title: res.data.header.msg,
          icon: 'none',
        })
      }
    })
  },
  //获取市
  getshiList(cityID) {
    var that = this
    var Myshenglsit = that.data.Myshenglsit
    ports.ModuleAll.getCityList({
      sessionID: wx.getStorageSync('USER_SESSIONID'),
      cityID: cityID,
      pageNumber: 9999,
      sortTypeName: 1,
      depth: 1
    }).then(res => {
      if (res.data.header.code === 0) {
        var shiList = res.data.body.data.rows
        
        // 普通城市，正常处理
        Myshenglsit.push(shiList)
        that.setData({
          Myshenglsit: Myshenglsit
        })
        that.getxianList(shiList[0].cityID)
      } else {
        wx.showToast({
          title: res.data.header.msg,
          icon: 'none',
        })
      }
    })
  },
  //获取县
  getxianList(cityID) {
    var that = this
    var Myshenglsit = that.data.Myshenglsit
    ports.ModuleAll.getCityList({
      sessionID: wx.getStorageSync('USER_SESSIONID'),
      cityID: cityID,
      pageNumber: 9999,
      sortTypeName: 1,
      depth: 1
    }).then(res => {
      if (res.data.header.code === 0) {
        var xianList = res.data.body.data.rows
        Myshenglsit.push(xianList)
        that.setData({
          Myshenglsit: Myshenglsit
        })
      } else {
        wx.showToast({
          title: res.data.header.msg,
          icon: 'none',
        })
      }
    })
    console.log(that.data.Myshenglsit)
  },
  //改变省市县的值
  PickerChange: function (e) {
    console.log(e,"滚动")
    var that = this
    // console.log('修改的列为', e.detail.column, '，值为', e.detail.value);
    var column = e.detail.column //列的索引
    var value = e.detail.value //行的索引
    var Myshenglsit = that.data.Myshenglsit //城市列表数据
    
    // 安全检查：确保数组和索引有效
    if (!Myshenglsit || !Myshenglsit[column] || !Myshenglsit[column][value]) {
      console.error('数组或索引无效:', column, value, Myshenglsit);
      return;
    }
    
    var Mycity = Myshenglsit[column] //获取当前改变的列表数据
    
    //第二列（省份改变）
    if (column == 0) {
      // 检查当前选择的省份是否为直辖市
      const currentProvince = Mycity[value];
      const isMunicipality = that.data.municipalities.includes(currentProvince.name);
      
      // 更新currentPickerValue
      let newPickerValue = [...that.data.currentPickerValue];
      newPickerValue[0] = value;
      newPickerValue[1] = 0; // 重置市级选择
      newPickerValue[2] = 0; // 重置县级选择
      
      if (isMunicipality) {
        // 如果是直辖市，直接获取区县列表
        that.getMunicipalityDistricts(currentProvince.cityID, currentProvince.name);
        that.setData({
          currentPickerValue: newPickerValue
        });
      } else {
        // 普通省份，获取市级列表
        ports.ModuleAll.getCityList({
          sessionID: wx.getStorageSync('USER_SESSIONID'),
          cityID: currentProvince.cityID,
          pageNumber: 9999,
          sortTypeName: 1,
          depth: 1
        }).then(res => {
          if (res.data.header.code === 0) {
            var shiList = res.data.body.data.rows
            Myshenglsit.splice(1, 1)
            Myshenglsit.splice(1, 0, shiList);
            
            //当第二列改变时改变第三列数据
            ports.ModuleAll.getCityList({
              sessionID: wx.getStorageSync('USER_SESSIONID'),
              cityID: shiList[0].cityID,
              pageNumber: 9999,
              sortTypeName: 1,
              depth: 1
            }).then(res => {
              if (res.data.header.code === 0) {
                var xianList = res.data.body.data.rows
                Myshenglsit.splice(2, 1)
                Myshenglsit.splice(2, 0, xianList);
                that.setData({
                  Myshenglsit: Myshenglsit,
                  currentPickerValue: newPickerValue
                })
              }
            })
          }
        })
      }
    }
    //第三列（市级改变）
    if (column == 1) {
      // 获取当前选择的省份索引
      let currentProvinceIndex = that.data.currentPickerValue[0] || 0;
      
      // 安全检查：确保省份索引有效
      if (!Myshenglsit[0] || !Myshenglsit[0][currentProvinceIndex]) {
        console.error('无法获取当前选择的省份:', currentProvinceIndex, Myshenglsit);
        return;
      }
      
      // 检查当前选择的省份是否为直辖市
      const currentProvince = Myshenglsit[0][currentProvinceIndex];
      const isCurrentProvinceMunicipality = that.data.municipalities.includes(currentProvince.name);
      
      // 更新currentPickerValue
      let newPickerValue = [...that.data.currentPickerValue];
      newPickerValue[1] = value;
      newPickerValue[2] = 0; // 重置县级选择
      
      if (isCurrentProvinceMunicipality) {
        // 如果当前省份是直辖市，直接获取区县列表
        that.getMunicipalityDistricts(currentProvince.cityID, currentProvince.name);
        that.setData({
          currentPickerValue: newPickerValue
        });
      } else {
        // 普通城市，获取县级列表
        const selectedCity = Mycity[value];
        ports.ModuleAll.getCityList({
          sessionID: wx.getStorageSync('USER_SESSIONID'),
          cityID: selectedCity.cityID,
          pageNumber: 9999,
          sortTypeName: 1,
          depth: 1
        }).then(res => {
          if (res.data.header.code === 0) {
            var xianList = res.data.body.data.rows
            Myshenglsit.splice(2, 1)
            Myshenglsit.splice(2, 0, xianList);
            that.setData({
              Myshenglsit: Myshenglsit,
              currentPickerValue: newPickerValue
            })
          }
        })
      }
    }
  },
  //我的籍贯
  MyNativeplace(e) {
    var that = this
    var Myshenglsit = that.data.Myshenglsit
    var value = e.detail.value
    
    // 安全检查：确保数组和索引有效
    if (!Myshenglsit || !value || value.length < 3) {
      console.error('数据无效:', value, Myshenglsit);
      return;
    }
    
    //下标
    var value1 = value[0]
    var value2 = value[1]
    var value3 = value[2]
    
    // 安全检查：确保数组存在且索引有效
    if (!Myshenglsit[0] || !Myshenglsit[0][value1] || 
        !Myshenglsit[1] || !Myshenglsit[1][value2] || 
        !Myshenglsit[2] || !Myshenglsit[2][value3]) {
      console.error('数组索引无效:', value1, value2, value3, Myshenglsit);
      return;
    }
    
    //获取选择的省
    var shengarr = Myshenglsit[0]
    var sheng = shengarr[value1]
    //获取选择的市
    var shiarr = Myshenglsit[1]
    var shi = shiarr[value2]
    //获取选择的县
    var xianarr = Myshenglsit[2]
    var xian = xianarr[value3]
    console.log(sheng, shi, xian)
    
    // 检查是否为直辖市
    const isMunicipality = that.data.municipalities.includes(sheng.name);
    
    let shengobj;
    if (isMunicipality) {
      // 直辖市格式：xx市-xx市-xx区/县
      shengobj = sheng.name + '-' + shi.name + '-' + xian.name;
    } else {
      // 普通省份格式：xx省-xx市-xx区/县
      shengobj = sheng.name + '-' + shi.name + '-' + xian.name;
    }
    
    var member = that.data.member;
    member.provinceID = sheng.cityID;
    member.provinceName = sheng.name;
    member.cityID = shi.cityID;
    member.cityName = shi.name;
    member.countyID = xian.cityID;
    member.countyName = xian.name;

    that.setData({
      sheng: sheng,
      shi: shi,
      xian: xian,
      shengobj: shengobj,
      currentPickerValue: value, // 更新当前picker的值
      member: member
    })
    // that.getShortDescription(that.data.shengobj)
  },
})