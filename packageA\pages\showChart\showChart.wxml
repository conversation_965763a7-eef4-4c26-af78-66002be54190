<!--pages/mine/aboutUs/aboutUs.wxml-->
<view class="about" wx:if="{{sensorList.length>0}}">
  <view style="color: red;text-align: center;">远程控制存在一定延迟，请耐心等候!</view>
  <view style="color: red;text-align: center;">{{seq}}</view>
	<view class='user-box' style="box-shadow:0 4rpx 8rpx 0 rgba(0,0,0,0.2),0 6rpx 20rpx 0 rgba(0,0,0,0.05);margin-top:20rpx;border: 1px double var(--themeColor);">
    <view class='ctn cuin'>
      <view class='list' wx:for="{{sensorList}}" wx:key="uniqueId" wx:for-item="item" wx:for-index="index" style="height: 150rpx;width:{{(index+1===sensorList.length)&&(index+1)%3===1?'100%':'33.33%'}};border-bottom: {{index<(sensorList.length-sensorList.length%3)?'1px double var(--themeColor)':''}};border-right: {{(index+1)%3!==0&&index<sensorList.length?'1px double var(--themeColor);':''}}">
      <view bindtap="{{item.isShow?'action':''}}" data-index="{{index}}">
      <view style="font-size: 20rpx;">{{item.moveStatus}}</view>
        <image mode="widthFix" src="{{'/assets/sensor/'+item.code+'1.png'}}" style="opacity: {{item.isShow?'1':'0.1'}};"></image>
        <text>{{item.name}}</text>
      </view>       
      </view>
    </view>
</view>
<view wx:if="{{isOpenAirCondition}}" class="card">
  <view class="airControl">
    <picker mode="selector" bindchange="bindPickerChange" value="{{index}}" range="{{array}}">
      <view class="flex-row justify-between align-center">
              <text>模式：{{array[index]}}</text>
              <text class="iconfont-xiala"></text>
            </view>
    </picker>
  </view>
  <view class="airControl">
    <view style="float: left;">温度：</view>
    <view style="float: left;" class='stepper'>
      <text style="float: left;" type='number' bindtap='bindMinus'>-</text>
      <input disabled="{{true}}" bindinput='bindManual' value='{{num}}'></input>
      <text style="margin-left: 20rpx;" type='number' bindtap='bindPlus'>+</text>
    </view>
  </view>
  <!-- <view class="airControl">灯光：</view> -->
  <view class="airControl">
    <picker mode="selector" bindchange="bindPickerWindChange" value="{{wIndex}}" range="{{windSpeedList}}">
      <view class="flex-row justify-between align-center">
              <text>风速：{{windSpeedList[wIndex]==="auto"?windSpeedList[wIndex]:windSpeedList[wIndex]+"挡"}}</text>
              <text class="iconfont-xiala"></text>
            </view>
    </picker>
  </view>
</view>
</view>
<view wx:else class="noData">没有获取到数据......</view>
