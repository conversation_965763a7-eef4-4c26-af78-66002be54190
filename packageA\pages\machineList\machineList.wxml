<!--packageA/pages/historyChat/historyChat.wxml-->
<view>
  <view class="noText" wx:if="{{machineList.length === 0}}">暂无数据</view>
  <view wx:else>
    <view class="itemBox">
      <ruleAndVideoBox inSide="{{false}}" button-type="primary" button-size="mini" bind:clickArticle="clickArticle" bind:toVideo="toVideo"></ruleAndVideoBox>
    </view>
    <view class="item flex-column justify-around" wx:for="{{machineList}}" wx:key="id" bindtap="href" data-id="{{item.id}}" data-trainid="{{item.trainBusinessID}}">
      <view class="item-title flex-row justify-between">
        <text>{{item.name}}</text>
        <text>{{item.code}}</text>
      </view>
      <view class="item-text">地址：{{item.address}} {{item.distance?"（距离"+item.distance+"）":''}}</view>
      <view class="item-text">在线状态：{{item.onlineType==1?'在线':item.onlineType==2?'离线':'未知'}}</view>
      <view class="item-text ">
      <text>课程名称：{{item.trainBusinessName || '无'}}</text>
      <button type="primary" size="mini" class="bookingBtn">查看详情</button>
      </view>
      <view class="item-text flex-row justify-between">
      <text decode="true">使用状态：{{item.runType==4?'培训中':(item.runType==null || item.runType==1 )?'未&nbsp;&nbsp;&nbsp;&nbsp;知':'空闲中'}}</text>
      <button type="primary" size="mini" class="bookingBtn" catch:tap="toAppointmentConfirm" data-id="{{item.machineID}}" wx:if="{{item.isShow}}" data-distance="{{item.distanceNumber}}">预约</button>
      </view>
      <!-- <button class="btn-sm" catchtap="appointment" data-item="{{item}}">预约</button> -->
    </view>
  </view>

</view>