var ports = require("../../../utils/ports.js");

const statusList = [{
  value: 1,
  name: '新'
},
{
  value: 2,
  name: '已经取消'
},
{
  value: 3,
  name: '报名审核通过'
},
{
  value: 4,
  name: '报名审核拒绝'
},
{
  value: 5,
  name: '取消审核通过'
},
{
  value: 6,
  name: '取消审核拒绝'
},
{
  value: 10,
  name: '已经付款'
},
{
  value: 11,
  name: '已经退款'
},
{
  value: 15,
  name: '报名成功'
},
{
  value: 20,
  name: '签到完成'
},
{
  value: 21,
  name: '验票完成'
},
{
  value: 24,
  name: '考试完成'
},
{
  value: 25,
  name: '开票完成'
},
{
  value: 30,
  name: '完成'
},
]

const payStatusList = [{
  value: 0,
  name: '未付款'
},
{
  value: 1,
  name: '已取消'
},
{
  value: 2,
  name: '已付款'
},
{
  value: 3,
  name: '付款过期'
},
]

// packageA/pages/addSubsidy/addSubsidy.js
Page({

    /**
     * 页面的初始数据
     */
    data: {
        form: {
            name: '',
            applyAmount: "",
            cardCode:'',
            cardBankName:'',
            orderSeq:'',
            examName:'',
            examTel:'',
            examIDCard:'',
            memberBankID:'',
        },
        showBankList:false,
        bankList:[],
        bankNameList:[],
        showTrainCompany:false,
        trainCompany:'',
        memberTrainList:[],
        trainCompanyID:'',
        // 新增字段存储选中的培训数据
        selectedTrainData: {
            companyID: null,
            distributeCompanyID: null,
            customerCompanyID: null,
            playCompanyID: null,
            governmentCompanyID: null
        }
    },

    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {
      this.getMemberBankList()
      this.getMemberTrainList()
      this.getOneMemberDetail()
    },

    showBankList(){
      this.setData({
        showBankList:true
      })
    },

    async getMemberBankList(){
      let res = await ports.ModuleAll.soaQueryBankCard({
        sessionID:wx.getStorageSync('USER_SESSIONID'),
        memberID:wx.getStorageSync('USER_MEMBERID')
      })
      let list = res.data.body.data.rows || []
      let bankNameList = list.map(item => {
        return {
            text: item.bankName,
        }
      });
      this.setData({
        bankList:list,
        bankNameList:bankNameList,
      })
      console.log(list,'银行卡');
      console.log(bankNameList,'银行卡2');
    },

    showTrainCompany() {
      this.setData({
          showTrainCompany: true
      })
    },

    getMemberTrainList() {
      var that = this
      let params = {
          sessionID: wx.getStorageSync('USER_SESSIONID'),
          joinMemberID: wx.getStorageSync('USER_MEMBERID'),
          sortTypeTime: 1,
          pageNumber: 999
      }
      ports.ModuleAll.getMemberTrainList(params).then(res => {
        let rows = res.data.body.data.rows || [];
        rows.forEach(item => {
          item.statusText    = that.filtersList(item.status, statusList);
          item.payStatusText = that.filtersList(item.payStatus, payStatusList);
        });
        let filtered = rows.filter(item => !item.governmentCompanyID);
        let list = filtered.map(item => ({
          text:                item.name,
          value:               item.trainCompanyID,
          companyID:           item.companyID,
          distributeCompanyID: item.distributeCompanyID,
          customerCompanyID:   item.customerCompanyID,
          playCompanyID:       item.playCompanyID,
        }));
        this.setData({
          memberTrainList: list
        });
        console.log(this.data.memberTrainList, 'memberTrainList');
      })
    },
    filtersList(value, arr) {
        if (!value) return '无'
        for (let i = 0; i < arr.length; i++) {
            if (arr[i].value == value) {
                return arr[i].name; // 找到之后就使用return返回找到的项，跳出循环
            }
            if (i == arr.length - 1 && arr[i].value != value) return '无'
        }
    },

    onCancel(){
      this.setData({
        showBankList:false,
        showTrainCompany: false,
      })
    },

    onConfirm(e){
      console.log(e);
      console.log(this.data.bankList,'银行列表');
      this.setData({
        'form.cardBankName':e.detail.value.text,
        'form.cardCode':this.data.bankList[e.detail.index].cardCode,
        showBankList:false,
      })

    },
    onConfirmCPN(e){
      console.log(e);
        let {
            text,
            value,
            governmentCompanyID,
            trainBusinessName,
            companyID,
            distributeCompanyID,
            customerCompanyID,
            playCompanyID
        } = e.detail.value;
        console.log(value, text, governmentCompanyID, trainBusinessName, "ssdsdsd");
        
        // 自动填充补贴名称
        let subsidyName = trainBusinessName || text;
        
        // 根据培训名称设置金额
        let amount = '';
        if (trainBusinessName === '养老护理员初级培训') {
            amount = '900.0';
        }
        
        this.setData({
            trainCompanyID: value,
            trainCompany: text,
            showTrainCompany: false,
            governmentCompanyID: governmentCompanyID,
            'form.name': subsidyName,
            'form.applyAmount': amount,
            // 保存选中的培训数据
            selectedTrainData: {
                companyID: companyID,
                distributeCompanyID: distributeCompanyID,
                customerCompanyID: customerCompanyID,
                playCompanyID: playCompanyID,
                governmentCompanyID: governmentCompanyID
            }
        })
    },

    async getOneMemberDetail(){
      let params = {
        sessionID:wx.getStorageSync('USER_SESSIONID'),
        memberID:wx.getStorageSync('USER_MEMBERID')
      }
      let res = await ports.ModuleAll.getOneMemberDetail(params)
      console.log(res.data.body,'我的信息');
      console.log(res.data.body.name,'我的信息');
      const member = res.data.body
      this.setData({
        'form.examName':res.data.body.name,
        'form.examTel':member.phone,
        'form.examIDCard':member.idNumber,
      })
    },
    /**
     * 生命周期函数--监听页面初次渲染完成
     */
    onReady() {

    },

    /**
     * 生命周期函数--监听页面显示
     */
    onShow() {

    },

    /**
     * 生命周期函数--监听页面隐藏
     */
    onHide() {

    },

    /**
     * 生命周期函数--监听页面卸载
     */
    onUnload() {

    },

    /**
     * 页面相关事件处理函数--监听用户下拉动作
     */
    onPullDownRefresh() {

    },

    /**
     * 页面上拉触底事件的处理函数
     */
    onReachBottom() {

    },

    /**
     * 用户点击右上角分享
     */
    onShareAppMessage() {

    },
    changeName(e) {
        console.log(e);
        let value = e.detail.value;
        this.setData({
            'form.name': value
        })
    },
    changeApplyAmount(e) {
        let value = e.detail.value;
        this.setData({
            'form.applyAmount': value
        })
    },

    changeCardBankName(e) {
        let value = e.detail.value;
        this.setData({
            'form.cardBankName': value
        })
    },
    changeCardCode(e) {
        let value = e.detail.value;
        this.setData({
            'form.cardCode': value
        })
    },
    add() {
        console.log(this.data.form,'参数');
        
        // 构建请求参数
        let requestParams = {
            sessionID: wx.getStorageSync('USER_SESSIONID'),
            memberID: wx.getStorageSync('USER_MEMBERID'),
            status: 1,
            ...this.data.form,
        }
        
        // 添加培训相关参数，如果为null则不传
        if (this.data.selectedTrainData.companyID) {
            requestParams.companyID = this.data.selectedTrainData.companyID;
        }
        if (this.data.selectedTrainData.distributeCompanyID) {
            requestParams.distributeCompanyID = this.data.selectedTrainData.distributeCompanyID;
        }
        if (this.data.selectedTrainData.trainCompanyID) {
            requestParams.trainCompanyID = this.data.trainCompanyID;
        }
        if (this.data.selectedTrainData.customerCompanyID) {
            requestParams.customerCompanyID = this.data.selectedTrainData.customerCompanyID;
        }
        if (this.data.selectedTrainData.playCompanyID) {
            requestParams.playCompanyID = this.data.selectedTrainData.playCompanyID;
        }
        if (this.data.selectedTrainData.governmentCompanyID) {
            requestParams.governmentCompanyID = this.data.selectedTrainData.governmentCompanyID;
        }
        
        ports.ModuleAll.createOneGovernmentSubsidiesApply(requestParams).then(res => {
            if (res.data.header.code === 0) {
                wx.showToast({
                    icon: 'success',
                    title: '创建成功',
                });

                let pages = getCurrentPages();
                let beforePage = pages[pages.length - 2];
                beforePage.onLoad()
                setTimeout(() => {
                    wx.navigateBack()
                }, 2000)
            }
        })
    },
    
})