/* packageA/pages/myAppointment/myAppointment.wxss */

.itemBox{
  background-color: #f2f2f2!important;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  padding-top: 22rpx;
  padding-bottom: 22rpx;
}
.card {
  background: #FFFFFF;
  box-shadow: 0rpx 0rpx 8rpx 0rpx rgba(0,0,0,0.15);
  border-radius: 8rpx;
  margin-bottom: 16rpx;
  padding: 27rpx 32rpx 16rpx;
  width: 90%;
  text-align: left;
}
.card:last-child{
  margin-bottom: 0;
}
.title{
  font-size: 35rpx;
  font-weight: bold;
}
.info-title-box .object-name {
  flex: 1;
  font-weight: 600;
  font-size: 28rpx;
  color: #111111;
  margin-right: 12rpx;
}
.tag{
  background: #EF9600;
  display: flex;
  align-items: center;
  justify-content: center;  
  margin-right: 8rpx;
  color: white;
  height: 30rpx;
  border-radius: 15rpx;
  font-weight: 600;
  font-size: 22rpx;
  color: #FFFFFF;
  padding-left: 18rpx;
  padding-right: 18rpx;
  box-sizing: border-box;
  width: 80rpx;
}
.tag.new{
  padding: 0;
  width: 79rpx;
  height: 29rpx;
}
.tag .icon{
  width: 100%;
  height: 100%;
}
.info-box{
  font-weight: 600;
  font-size: 24rpx;
  color: #111111;
  line-height: 24rpx;
  box-sizing: border-box;
  padding-top: 21rpx;
  padding-bottom: 24rpx;
  border-bottom: 2rpx solid rgba(151, 151, 153,0.4);
}
.info-box .gray-text{
  font-weight: 400;
  font-size: 22rpx;
  color: #999999;
  line-height: 24rpx;
  font-style: normal;
  padding-bottom: 16rpx;
}
.info-btn-box{
  position: relative;
  padding-top: 16rpx;
}
.info-btn-box .gray-text2{
  color: #C3C3C3;
  font-size: 22rpx;
}

.btnAppoint {
  margin: 50rpx auto;
  width: 240rpx;
  height: 58rpx;
  line-height: 50rpx;
  border: 2rpx solid #5c7fcf;
  text-align: center;
  color: #5c7fcf;
  font-size: 30rpx;
  font-weight: 500;
  border-radius: 56rpx;
}
.equipment {
  margin:20rpx 20rpx;
  /* border: 2rpx solid #5c7fcf; */
  text-align: center;
  color: white;
  font-size: 30rpx;
  background-color: #ffbd62;
  border-radius: 10rpx;
}
.gs_circle{
  /* padding:50rpx 0; */
}
.mainPage{
  background-color: #f2f2f2!important;
  min-height: 100vh;
  overflow: hidden;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  padding-bottom: constant(safe-area-inset-bottom); /*兼容 iOS < 11.2*/
  padding-bottom: env(safe-area-inset-bottom); /*兼容 iOS >= 11.2*/
}
.gs_incircle{
  width:225rpx;
  height:225rpx;
  background-color: #cce6ff;
  border-radius:50%;
  padding: 10rpx;
  margin:20rpx auto;
  cursor: pointer;
}
.gs_excircle{
  width:100%;
  height: 100%;
  background-color:#fff;
  border-radius:50%;
  position: relative;
}
.gs_innercircle{
  white-space: pre-wrap;
  width:200rpx;
  height:200rpx;
  background-color: #8bc4f6;
  border-radius:50%;
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  margin: auto;
  display: flex;
  align-items: center;
  justify-content: center;
  color:#fff;
  font-size:36rpx;
}

.info-btn-box .btn{
  font-weight: 600;
  font-size: 24rpx;
}
.btn.disabled{
  background-color: #F7F7F7;
  color: #acacac;
}
.serviceBtn {
  color: #1E7AFF;
}

.videoBtn {
  padding-left: 55rpx;
  padding-right: 55rpx;
  line-height: 40rpx;
  height: 40rpx;
  color: white;
  background: #1E7AFF;
  border-radius: 20rpx;
  margin-right: 50rpx;
  box-sizing: border-box;
}
.enterBtn{
  padding-left: 55rpx;
  padding-right: 55rpx;
  line-height: 40rpx;
  height: 40rpx;
  color: white;
  background: #1E7AFF;
  border-radius: 20rpx;
  margin-right: 50rpx;
  box-sizing: border-box;
}
.allBtn {
  /* position: absolute;
  right: 0; */
  color: var(--themeColor) !important;
  text-decoration: underline !important;
}
.page-section {
  padding: 20rpx 40rpx;
  display: flex;
  flex-direction: row;
  align-items: center;
  box-shadow: rgba(0,0,0,0.1) 0 4rpx 16rpx;
}
.page-section .switch {
  font-weight: 600;
  font-size: 32rpx;
  color: #333333;
  line-height: 32rpx;
  margin-right: 60rpx;
}
.page-section .switch:last-child{
  margin-right: 0;
}
.page-section .switch.checked {
  color: #1E7AFF;
}
.page-box{
  padding-left: 24rpx;
  padding-right: 24rpx;
  box-sizing: border-box;
}
/* 日期选择 */
.picker_group {
  display: flex;
  flex-direction: row;
  align-items: center;
  font-size: 32rpx;
  /* color: white; */
  flex: 1;
  /* border-bottom: 1rpx solid #efefef; */
}
.picker_group.end{
  display: flex;
  justify-content: flex-end;
}
.picker_group picker {
  /* color: white; */
  /* width: 80%; */
  /* min-width: 200rpx;
  height: 60rpx;
  line-height: 55rpx;
  margin: 0 2%;
  padding: 0 4%; */
  /* border: 1rpx solid var(--themeColor); */
  /* border-radius: 10rpx;
  background-color: #ffbd62; */
}
.pickBox{
  padding-top: 25rpx;
  padding-bottom: 23rpx;
}
.picker{
  width: 100%;
  height: 100%;
  text-align: center;
  font-weight: 400;
  font-size: 24rpx;
  color: #333333;
}
.time-picker-btn{
  font-weight: 600;
  font-size: 32rpx;
  color: #1E7AFF;
}
.picker_group .icon{
  width: 33rpx;
  height: 33rpx;
  margin-right: 16rpx;
}
.object-picker-btn{
  font-weight: 400;
  font-size: 24rpx;
  color: #333333;
  justify-content: flex-end;
}
.adBox{
  width: 550rpx;
}

.time-btn {
  color: white;
  width: 100%;
  height: 50rpx;
  line-height: 40rpx;
  margin: 0 2%;
  padding: 0 4%;
  /* border: 1rpx solid var(--themeColor); */
  border-radius: 10rpx;
  background-color: #ffbd62;
}
.page_div{
  display: flex;
  width: 100%;
  justify-content: flex-end;
  box-sizing: border-box;
  /* padding:0rpx 0; */
  padding-bottom: 80rpx;
  background-color: #f2f2f2!important;
}
.page_div .page_sum,
.page_div .page_prev,
.page_div .page_number_div,
.page_div .page_next{
  height: 50rpx;
  line-height: 50rpx;
  font-size:24rpx;
  color: #333;
}
.page_div .page_prev,
.page_div .page_next{
  background-color: rgb(64, 158, 255);
  border-radius: 10rpx;
  color: white;
  padding:0 0rpx;
  margin:0 0rpx;
}
.page_div .page_number_div .pageGo{
  display: inline-block;
  vertical-align: middle;
  width: 50rpx;
  box-sizing: border-box;
  background-color: rgb(64, 158, 255);
  border-radius: 10rpx;
  color: white;
  text-align: center;
  margin:0 0rpx;
}
.page_div .page_number_div input{
  width: 50rpx;
  display: inline-block;
  vertical-align: middle;
  text-align: center;
  border:1px solid rgb(122, 117, 117);
  border-radius: 10rpx;
  background-color: white;
}

.line-box {
  position: relative;
}
.line-box::after {
  position: absolute;
  content: '';
  display: block;
  left: 0;
  right: 0;
  bottom: 0;
  height: 1rpx;
  background-color: #979797;
  opacity: 0.4;
}

.envScoreBtn {
  position: absolute;
  right: 0;
  bottom: 80rpx;
  background: #f7b500;
  color: #fff;
  border-radius: 8rpx;
  padding: 0 20rpx;
  height: 56rpx;
  line-height: 56rpx;
  font-size: 26rpx;
  display: inline-flex;
  align-items: center;
  z-index: 2;
}