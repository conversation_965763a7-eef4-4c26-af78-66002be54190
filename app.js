App({
    //域名定义
    // baseUrl: 'https://www.onefind.club/interface-server/api/',
    //    baseUrl: 'https://3onedataqz.com/interface-server/api/',
    baseUrl: 'http://127.0.0.1:6007/interface-server/api/',

    weixinAppTitle: '咖星联大', //创建第一步修改title  为小程序名称
    logoimg: '/assets/images/logo.png', //logo图片  这个不修改   直接去该目录覆盖logo文件
    mainColor: '#7ed2fe', //主题色
    privacyAgreementID: '8a2f462a8ec060f8018ec240f4aa0156', //隐私协议   两个都是系统文章
    registerAgreementID: 'ff8080819192355a019192bbeec90001', //注册协议  两个都是系统文章
    articleID: 'ff8080819221f43f019232eb454c0013',

    agreementDefineID: '8a2f462a8ec060f8018ec241722b015a', //协议定义ID   每个小程序的协议id都不一样  注册的时候需要用到
    appid: 'wxeb435647128b9044', //(小程序ID)  微信公众平台获取的
    publicNo: 'gh_64ff9b4372de', //原始ID  微信公众平台获取的	  wanghuangu
    weixinID: 'ff80808190e860a90190e89253950000', //微信公众号ID   这个是我们自己的   从公众号管理获取
    weixinAppID: 'c05f46fcf31e4ed8b22ea209dd167dbe', //微信小程序ID   这个是我们自己的   从微信小程序管理获取

    fileBucketID: "8a2f462a850617e301851e5760810d72", //上传文件
    videoID: "ff80808197ab5a940197beb687cb0011",

    // 应用ID：
    // 应用ID 是 当前小程序归属的应用，在后台《应用模块》创建好。应用ID是固定的，每个小程序 在第一步，要修改1下。 在调用一些查询接口，接口定义里面 要传ApplicationID的，直接传这个。
    applicationID: '8a2f462a8ec060f8018ec2415c5e0157', //应用ID

    //站点ID
    //站点ID 是 当前小程序 本身的ID，每个小程序和 app，网站等，都是1个应用里面的具体的站点。在后台 《站点》模块定义好了。在创建小程序的第一步要修改这个siteID。登录接口必须传入这个。
    siteID: '8a2f462a8ec060f8018ec2417fcb01b9', //站点ID
    //公司ID
    // 公司ID是 当前小程序所在平台的公司ID，或者是小程序经营和运营的公司，也就是所有权人的公司。在后台《公司模块》创建好了。 在创建小程序 第一步，就要确认这个 公司companyID。 在小程序展示公司信息的时候，必须使用这个公司ID。
    //在联系我们页面用这个公司。 在意见反馈功能填写这个公司。
    companyID: '8a2f462a8ec060f8018ec23fa54a0153', //公司ID	
    shopID: '44c60dd3adae41349a6703db6575bae4',

    //运营人员身份定义majorID
    supportMajorID: 'ff80808191e959270191ee51f77f0000',
    //助教身份定义majorID
    assistMajorID: 'ff8080819221f43f01922db9d8ea000f',

    //版本ID
    //版本ID是 当前小程序 开发人员定义的版本号。在后台《版本管理》模块 定义好了。如果小程序需要 通过版本号 里面的 发布状态 来显示不同内容的情况下，这个 版本IDversionID 必须 和后台最新定义的保持一致。如果不需要根据版本判断显示内容，则这个ID 不需要。
    //versionID: '', //小程序版本ID


    // 在 小程序启动的时候，就检查本地缓存是否有 deviceID。如果有，则在 注册和登录的时候使用，如果没有，则需要调用接口，传入一些参数，获取deviceID。在登录报错的时候，如果提示 deviceID不对，也需要重新获取，同时保存到本地。
    deviceID: '', //登录设备ID

    //channel模块的id   可以获取到对应id的秘钥或者key之类的  来实现地图定位功能
    QQchannelID: 's', //腾讯地图账号id

    // agreementDefineID协议对象定义ID
    //  如果小程序没有注册协议，在不需要这个。如果需要签署注册协议，则必须有这个。如果在小程序，除了注册协议，还有其他的协议，例如平台入驻协议，拍卖协议等，则需要另外加上 xxxxAgreementDefineID 这样的。所有协议定义都在后台定义好。


    //分类ID
    // 分类Category 是本平台的重要概念。 不同的功能模块，使用不同的分类ID。在后台分类定义模块，设计人员已经按客户的要求 创建了不同的分类定义。分类定义是1个树结构。例如 性别称谓在member里面的字段属性是titleID，在 需要对应后台的1个分类ID， 这个分类下面会有 男，女 不同性别的 分类ID。 一个小程序中，会有很多分类ID，都列在下面
    //categoryId: '811d6c164c49464c81ae4529effae57d', //分类ID
    servicerCategoryID: 'e59c1e8fcd4e453b979aca4d9fee2a83',
    againstCategoryID: '40a3850a4f984e1aa681c6c18e2b7693',
    siteServiceCategoryID: 'c9a32190910349f5a26108f3eee11509',

    // 专业身份majorID
    // 专业身份major 是 平台核心功能，是对每个会员在本平台的身份进行区分的。例如，设计师和设计院主任等，都是不同的身份。在会员注册登录后，会获取到这个会员是否特定的专业身份，需要用到 下面的 majorID。这些majorID是在后台预先定义好的。
    //majorID1: 'ff8080818378cfe301837ce9b36a0006', // 特定身份，每个小程序的不一样  自定义增删


    // objectDefineID意见反馈
    // 在本系统内，所有的数据对象都是1个 object，不同的数据对象就是1个 objectDefine。例如，在真实世界中，1个人是1个对象objectID，1个电脑是1个对象objectID。 人就是一个objectDefineID， 电脑也是1个 objectDefineID。 在发送消息，上传附件，根据轮播图，消息等跳转页面的时候，都需要判断objectDefineID。objectDefineID是在后台模块objectDefine 里面 事先定义好的，在当前小程序内使用的，都需要在这里定义好，例如： 工作经历unitObjectDefineID，意见反馈feedObjectDefineID 等。
    feedObjectDefineID: '8a2f462a5ebc4863015ebc755ff70455', //意见反馈id
    articleObjectDefineID: '8af5993a4fba5335014fbb0c8e7e00b0',
    discussObjectDefineID: '8af5993a4fdaf145014fde20811b0128',
    againstObjectDefineID: '8af5993a4fdaf145014fde21f1a40134',
    machineObjectDefineID: '8a2f462a5da12665015da1c6908a05d2',
    trainBusinessObjectDefineID: '8a2f462a6371016e0163717f1e0f057c',
    //goodsShopObjectDefineID: '8a2f462a626bdc5f01626bf9a87800bc', //商品模块id
    //objectDefineID: '8a2f462a5a01c199015a0834e4f3543e', //预约模块id
    companyObjectDefineID: '8af5993a4fdaf145014fde16dbd900dd', //公司模块id
    //browseobjectDefineID: '8a2f462a63ab75410163af43fedf1414', //浏览ID

    //根据业务场景和客户需求可能需要的支付机构有
    //钱包paywayID
    //金币paywayID
    //微信支付paywayID
    //线下支付paywayID
    //区块链支付paywayID
    paywayID: '8a2f462a8ec060f8018ec2417faa01b8', //支付机构(咖星联大微信支付)
    // withdrawDefineID:'8a2f462a63fe65cf0164012a65763a28',
    //搜索id   一个搜索功能对应一个id   在后台的搜索定义里增删
    articleSearchDefineID: 'ff8080819192355a019192c54fac0002', //文章搜索ID
    goodsSearchDefineID: '8a2f462a8e3afbc7018e50b64cd909c1', //商品搜索ID
    storySearchDefineID: '8a2f462a8e3afbc7018e50b80bfb09c2', //故事或者说课程搜索ID

    //文章ID
    // 如果小程序只有1个 关于我们 页面，则需要定义1个 articleID。一般来说，注册协议里面的文本内容也存储在一个文章里面，也要在这里加上XXXXarticleID 。针对公司介绍，业务介绍等 常见的内容，一般也是一个文章。都在这里加上 xxxxArticleID。所有这里的articleID都是后台的系统文章。
    //很多页面可能有一些提示文字，规则说明等都需要在后台创建对应的系统文章，并把文章id写在下面
    aboutUsArticleID: '8a2f462a8ec060f8018ec241721d0159', //关于我们文章

    // 导航navigator是本系统最主要的对象，每个站点（包含小程序，网站，app）等，都包含了很多导航。有些导航是动态获取的，有些是静态的。通常，在首页上展示热点新闻、推荐商品等，都需要事先在后台定义好导航。 例如热点新闻的导航ID 应该是hotNavigatorID。 如果有其他的，则定义为 XXXXNavigatorID。 3130df1e535f47d9806de5489112b997
    caseNavigatorID: "1c2d9d32c7e84f39b90eb9665055b902", //案例首页导航
    knowNavigatorID: "d470d032-552f-462a-bc8f-8deaac91a600",
    recommendGoodsNavigatorID: '52f2837db77b4a9da350aa07efbb17b3', //推荐商品id
    shopNavigatorID: "6300db2a4a2c4b13b4b64a39d7c23441", //商城导航总id
    questionNavigatorID: "6edf6b0a3e8c424f9239eb0cd89cddf5", //常见问题 导航id
    //动态导航
    newsCenterNavID: "dea37e36f1e8410ea59b0ec3e25f76c8",

    //性别分类定义
    men: '384aba23a5ac4344b5848250d793cd64', //女士
    man: '1d88af067fff4d6e95575e77f7365029', //先生


    //轮播图ID  自定义增删
    //homeSwiperID: '60ccc878418e435bbc8eae779451f5b3', //轮播图ID

    //其他id
    ChinaID: "6ee7a940c0e048b7ba52fc22a6a5ad59",
    shopCategoryID: '', //商城分类总ID

    //作为全局变量  适合各个模块传值
    // 推荐码
    recommendCode: '',
    recommendMemberID: '',

    statusHeight: (wx.getSystemInfoSync().screenHeight - wx.getSystemInfoSync().statusBarHeight - 44) * (750 / wx.getSystemInfoSync().screenWidth) - 102, // 详情页面内容高度 102为详情页面底部盒子高度 44 为页面标题栏固定的高度
    listHeight: (wx.getSystemInfoSync().screenHeight - wx.getSystemInfoSync().statusBarHeight - 44) * (750 / wx.getSystemInfoSync().screenWidth) - 198, // 列表页面内容高度 198为列表页面底部盒子高度 44 为页面标题栏固定的高度
    //我不知道上面再写什么吊东西，屏幕安全区应该不是这样获得的。下面是获取屏幕底部小白条留白高度
    safeAreaBottom: (wx.getWindowInfo().screenHeight - wx.getWindowInfo().safeArea.bottom),
    // 自定义底部导航栏：100rpx=》px + safeAreaBottom 
    cust_bar_height: ((wx.getWindowInfo().screenWidth / 750) * 100) + (wx.getWindowInfo().screenHeight - wx.getWindowInfo().safeArea.bottom),

    onLaunch() {
        // 更新管理器
        const updateManager = wx.getUpdateManager()

        updateManager.onCheckForUpdate(function (res) {
            // 请求完新版本信息的回调
            console.log(res.hasUpdate)
        })

        updateManager.onUpdateReady(function () {
            wx.showModal({
                title: '更新提示',
                content: '新版本已经准备好，是否重启应用？',
                success: function (res) {
                    if (res.confirm) {
                        // 新的版本已经下载好，调用 applyUpdate 应用新版本并重启
                        updateManager.applyUpdate()
                    }
                }
            })
        })

        updateManager.onUpdateFailed(function () {
            // 新版本下载失败
        })

        // 系统信息获取
        wx.getSystemInfo({
            success: (res) => {
                console.log(res);
                this.globalData.systemInfo = res
            }
        })
        const menuButtonInfo = wx.getMenuButtonBoundingClientRect(); //胶囊相关信息
        const menuButtonHeight = menuButtonInfo.height //胶囊高度
        const menuButtonTop = menuButtonInfo.top //胶囊距上边界距离
        this.globalData.menuButtonHeight = menuButtonHeight
        this.globalData.menuButtonTop = menuButtonTop
        console.log(this.globalData)

        // 启动缓存自动清理
        this.initCacheManager();
    },

    /**
     * 初始化缓存管理器
     */
    initCacheManager: function() {
        try {
            const cacheManager = require('./utils/cacheManager');
            console.log('缓存管理器模块加载成功');

            // 启动自动清理，每30分钟清理一次
            cacheManager.startAutoClean(30);

            console.log('缓存自动清理已启动，间隔30分钟');

            // 将缓存管理器添加到全局数据中，方便其他页面使用
            this.globalData.cacheManager = cacheManager;

            // 立即执行一次清理测试
            cacheManager.getCacheReport();

        } catch (error) {
            console.error('初始化缓存管理器失败:', error);
            console.error('错误详情:', error.message);
            console.error('错误堆栈:', error.stack);
        }
    },
    globalData: {
        keyWords: '',
        system: wx.getSystemInfoSync(),
        capsule: wx.getMenuButtonBoundingClientRect(),
    },
})