// packageA/pages/myAppointment/myAppointment.js
var ports = require("../../../utils/ports")
var app = getApp()
var longitude = require('@/utils/longitude.js')
var utils = require('../../../utils/util')
const defaultCheckLearnType = [
  {value: '1', name: '实训/学习',checked:true, workType : '3'},
  {value: '2', name: '考试',checked:false, workType : '10'},
];
Page({

  /**
   * 页面的初始数据
   */
  data: {
    checkLearnType:defaultCheckLearnType,
    currentMachine: 0,
    machineList: [],
    iccid: '',
    id: '',
    flag: '',
    mid: '',
    info: {},
    trainBusinessInfo: {},
    trainCompanyInfo: {},
    appointmentList: [],
    showList: [],
    deviceID: '',
    deviceName: '',
    serviceIDs: [],
    service: {
      serviceID: '',
      CharacteristicIDs: []
    },
    canWrite: false,
    serviceIDSel: '',
    charas: [],
    charValue: '',
    userInput: '',
    distance: 0, //用经纬度计算距离
    bluetoothDeviceName: 'trainMachine',
    _discoveryStarted: false,
    statusList: [
      {num:1,name:"新"},
      {num:2,name:"申请"},
      {num:3,name:"取消"},
      {num:4,name:"预约中"},
      {num:5,name:"撤销"},
      {num:6,name:"签到"},
      {num:10,name:"验票"},
      {num:11,name:"离开"},
      {num:20,name:"失效"},
    ],
    myX: 31.230416,
    myY: 121.473701,
    statusStr: '',
    timeFlag: true,
    connectStr: "进入",
    appointmentIndex: -1,
    ornot:0,//今天是否签到
    btnShow:false,
    workTypeList: [
      {code:0,name:"未选择"},
      {code:1,name:"不限制"},
      {code:2,name:"学习"},
      {code:3,name:"实训"},
      {code:10,name:"考试"},
    ],
    switchList: [
      {code:'',name:"全部",flag:true},
      // {code:2,name:"学习",flag:false},
      {code:3,name:"实训",flag:false},
      {code:10,name:"模拟考试",flag:false},
      // {code:11,name:"已完成",flag:false},


      {code:'',name:"全部",flag:true},
      {status:1,name:"未开始",flag:false},
      {status:6,name:"进行中",flag:false},
      {status:11,name:"已完成",flag:false},
      {status:20,name:"已过期",flag:false},

    ],
    switchList2: [
      {code:'',name:"全部",flag:true},
      // {code:2,name:"学习",flag:false},
      {code:1,name:"未开始",flag:false},
      {code:6,name:"进行中",flag:false},
      {code:11,name:"已完成",flag:false},
      {code:20,name:"已过期",flag:false},
    ],
    switchIndex: 0,
    searchFlag: true,
    sensorList: [],
    openDate:'',
    closeDate:'',
    selectedDate: '',
    //分页数据
    page: 1,
    pageSize: 5,
    showList: [],
    pageNumber:1,
    pagetotal:0,
    page:1,
    isNoData:false,
    isMonth:true,
    changeName:'查看所有预约',
    showEnvScoreDialog: false,
    envScoreList: [],
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    utils.isLoginTimeOut()


    if(wx.getStorageSync('USER_SESSIONID')){
      let nowTime = new Date(utils.formatDate(new Date().getTime(), 'yyyy-MM-dd'))
      let { firstDay, lastDay } = this.getFirstAndLastDayOfMonth(nowTime);
      console.log(firstDay,lastDay)
      let f = utils.formatDate(firstDay, 'yyyy-MM-dd');
      let l = utils.formatDate(lastDay, 'yyyy-MM-dd');
      this.setData({
        openDate: f,
        selectedDate: f.slice(0,f.length-3),
        closeDate: l,
      })
      this.getMachineList()
      

    }else wx.showToast({
      title: '请先登录',
      icon:"none"
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // console.warn('show')

  },
  getMachineList() {
    let params = {
      applicationID: app.applicationID,
      companyID: app.companyID,
      pageNumber: 999
    }
    ports.ModuleAll.getMachineList(params).then(res => {
      let list = res.data.body.data.rows || []
      list.unshift({name:"全部实训舱"});
      this.setData({
        machineList: list ,
      },()=>{
        this.init()
      })
    })
  },
  //input输入双向绑定数据
  inputValue:function(e){
    let name = e.currentTarget.dataset.name;
    let mapName ={};
    mapName[name]=e.detail && e.detail.value;
    // console.log(mapName);
    this.setData(mapName);
  },
  //上一页
  prevFn:function(){
    if(this.data.pageNumber <=1){
      wx.showToast({
        icon:'none',
        title: '已经是最前一页',
      })
      return;
    }
     
    wx.showLoading({
      title: '加载中...',
    })
    this.setData({
      pageNumber:Number(this.data.pageNumber)-1
    })
    this.init()
    console.log(this.data.pageNumber);
    // setTimeout(function(){
    //   wx.hideLoading()
    // },1000)
  },
  //下一页
  nextFn:function(){
    if(this.data.pageNumber === this.data.pagetotal){
      wx.showToast({
        icon:'none',
        title: '已经是最后一页',
      })
      return;
    }
    wx.showLoading({
      title: '加载中...',
    })
    this.setData({
      pageNumber:Number(this.data.pageNumber)+1
    })
    this.init()
    console.log(this.data.pageNumber);
    // setTimeout(function(){
    //   wx.hideLoading()
    // },1000)
  },
  //去到某一页
  pageGo:function(){
    console.log(Number(this.data.pageNumber));
    if(Number(this.data.pageNumber) > this.data.pagetotal){
      this.setData({
        pageNumber:this.data.pagetotal
      })
    }else if(Number(this.data.pageNumber) <= 0){
      this.setData({
        pageNumber:1
      })
    }
    wx.showLoading({
      title: '',
    })
    this.init()
    // setTimeout(function(){
    //   wx.hideLoading()
    // },1000)
  },


  add1month(){
    var current = new Date(this.data.selectedDate);
    var next = new Date(current.getFullYear(), current.getMonth() + 1, 1);
    this.setData({      
      selectedDate:utils.formatDate(next, 'yyyy-MM')
    })  
    this.setData({
      pageNumber: 1,
      showList: []
    })  
    this.init()
  },
  minus1month(){
    var current = new Date(this.data.selectedDate);
    var previous = new Date(current.getFullYear(), current.getMonth() - 1, 1);
    this.setData({      
      selectedDate:utils.formatDate(previous, 'yyyy-MM')
    })  
    this.setData({
      pageNumber: 1,
      showList: []
    })
    this.init()
  },
  bindMachineChange(e) {
    this.setData({
      currentMachine: e.detail.value,
    })
    this.init()
  },

  // 时间段选择  
  bindDateChange(e) {
    this.setData({
      // selectedDate: utils.formatDate(new Date(e.detail.value), 'yyyy-MM-dd'),
      selectedDate: e.detail.value,
      pageNumber: 1,
      showList: []
    })
    this.init()
  },

  calcDate(time) {
    let nowTime = new Date(time)
    let { firstDay, lastDay } = this.getFirstAndLastDayOfMonth(nowTime);
    let f = utils.formatDate(firstDay, 'yyyy-MM-dd');
    let l = utils.formatDate(lastDay, 'yyyy-MM-dd');
    console.log(f,l)
    this.showList1(f,l)
  },
  showList1(start,end) {
    this.setData({
      showList: []
    })
    let list = []
    let timeStamp1 = new Date(start).getTime()
    let timeStamp2 = new Date(end).getTime()
    
    this.data.appointmentList.forEach((item)=>{
      let nowStamp = new Date(item.openDateStr).getTime()
      if (nowStamp>=timeStamp1&&nowStamp<=timeStamp2) {
        list.push(item)
      }
    })
    this.setData({
      showList: list,
      isNoData: !list.length
    })
  },
  showList2(e) {
    let index = e.currentTarget.dataset.index
    let list = this.data.switchList
    for (let i = 0;i < list.length;i ++) {
      if (index===i) {
        list[i].flag=true
      } else {
        list[i].flag=false
      }
    }
    this.setData({
      switchList: list,
      pageNumber: 1,
      page: 1,
      switchIndex: index,
      showList: [],
    })
    this.init()
  },
  getFirstAndLastDayOfMonth(date) {
    let firstDay = new Date(date.getFullYear(), date.getMonth(), 1);
    let lastDay = new Date(date.getFullYear(), date.getMonth() + 1, 0);
    // let f = utils.formatDate(firstDay, 'yyyy-MM');
    // let l = utils.formatDate(lastDay, 'yyyy-MM');
    return {
        firstDay: firstDay,
        lastDay: lastDay
    };
  },
  async init() {
    wx.showLoading({
      title: '加载中...',
    }); 
    // 使用正则表达式
    function cutStringBeforeCharWithRegex(str, charToFind) {
      var regex = new RegExp("(.*)" + charToFind);
      if (regex.test(str)) {
          return RegExp.$1;
      }
      return str;
    }
    var that = this
    let nowTime = new Date(this.data.selectedDate)
    // console.log(this.data.selectedDate);
    let { firstDay, lastDay } = this.getFirstAndLastDayOfMonth(nowTime);
    let f = utils.formatDate(firstDay, 'yyyy-MM-dd');
    let l = utils.formatDate(lastDay, 'yyyy-MM-dd');
    const { page, pageSize, showList } = this.data;
    
    let params = {
      applicationID: app.applicationID,
      sessionID: wx.getStorageSync('USER_SESSIONID'),
      memberID: wx.getStorageSync('USER_MEMBERID'),
      sortTypeOpen:1,
      sortTypeName:1,
      currentPage: page,
      pageNumber: pageSize,
      status:this.data.switchList[this.data.switchIndex].status,
      workType: this.data.switchList[this.data.switchIndex].code==1||this.data.switchList[this.data.switchIndex].code===11?null:this.data.switchList[this.data.switchIndex].code,
      sortTypeOpen: 2,
    }
    if (this.data.isMonth == true) {
      params.beginTime= f;
      params.endTime= l;
    }
    if ((this.data.machineList[this.data.currentMachine])?.id) {
      params.objectID = this.data.machineList[this.data.currentMachine].id
    }
    // 新增：如果是已完成tab，增加status=11参数
    if(this.data.switchList[this.data.switchIndex].code === 11){
      params.status = 11;
    }

   
// 调用 getAppointmentList 接口获取预约列表
ports.ModuleAll.getAppointmentList(params).then(appointment => {
  // 过滤掉状态为 20 的数据
  // const filteredAppointments = (appointment.data.body.data.rows || []).filter(item => item.status !== 20);
  // appointment.data.body.data.rows = filteredAppointments;
      wx.hideLoading()
      // wx.stopPullDownRefresh()
      
      let arr =( appointment.data.body.data.rows || []).map(item=>{
       let address = this.data.machineList.find(m=>m.name == item.objectName)?.address || ''
        return {...item, address,createdTimeString: getNewTimeStr(item.createdTimeStr)}
      });
      let totalPage = appointment.data.body.data.totalPage;
      that.setData({
        pagetotal: totalPage,
        // appointmentList: arr,
      })
      // for (let i = 0;i < that.data.appointmentList.length;i ++) {
        for (let i = 0;i < arr.length;i ++) {

        let now = new Date();
        let year = now.getFullYear(); // 获取当前年份
        let month = now.getMonth(); // 获取当前月份
        let day = now.getDate(); // 获取当前日期
        let zeroTime = new Date(year, month, day); // 当天零点的时间对象
        let zeroTimeTimestamp = zeroTime.getTime() + 1296000000; // 当天零点的时间戳
        // let nowTimeTimestamp = new Date().getTime()
        let c = that.data.workTypeList.find((obj)=>obj.code===arr[i].workType) 
        if (c) {       
          // let str1 = "appointmentList["+ i + "].workTypeStr";  
          // that.setData({
          //   [str1]: c.name
          // })
          arr[i].workTypeStr = c.name
        }
        // let r = that.data.statusList.find((obj)=>obj.num===that.data.appointmentList[i].status)
        let r = that.data.statusList.find((obj)=>obj.num===arr[i].status)
        if (r) {
          // let str2 = "appointmentList["+ i + "].statusStr";  
          // that.setData({
          //   [str2]: r.name
          // })
          arr[i].statusStr = r.name
        }
        // let openStr = that.data.appointmentList[i].openDateStr+" "+cutStringBeforeCharWithRegex(that.data.appointmentList[i].timeIntervalName, "点")+":00";
        let openStr = arr[i].openDateStr+" "+cutStringBeforeCharWithRegex(arr[i].timeIntervalName, "点")+":00";
        let nowTimeTimestamp = new Date().getTime();
        let beginTime = new Date(openStr).getTime() - 1800000;
        let endTime = new Date(openStr).getTime() + 1800000;
        let key = "appointmentList["+ i + "].timeFlag";
        if (nowTimeTimestamp >= beginTime && nowTimeTimestamp <= endTime) {
          
          // that.setData({
          //   [key]: true
          // })
          arr[i].timeFlag = true

        } else {
          // that.setData({
          //   [key]: false
          // })
          arr[i].timeFlag = false

        }       
      }
      // that.setData({
      //   appointmentList: arr,
      //   showList: arr
      // })
      // 合并新旧数据
      console.log(arr);
      // let newAppointmentList = that.data.pageNumber > 1 ? that.data.appointmentList.concat(arr) : arr;
      console.log([...showList, ...arr]);
      that.setData({
        showList: [...showList, ...arr],
        page: page + 1,
        appointmentList: [...showList, ...arr],
        // showList: newAppointmentList,
        isNoData: ![...showList, ...arr].length,
        isRefreshing: false
      });

      // console.log(newAppointmentList);
    })

  },
  //打卡签到
  gosign:function(e){
      let that = this 
      that.setData({
          ornot:1,
      })
      let params = {
        sessionID: wx.getStorageSync('USER_SESSIONID'),
        appointmentID: e.currentTarget.dataset.id,
        shopID: that.data.info.shopID,
        mapAddress: that.data.info.address,
        mapX: that.data.info.mapX,
        mapY: that.data.info.mapY,
      }
      ports.ModuleAll.signinOneAppointment(params).then(res => {
        console.log(res,"开始学习")
      })
  },
  goout:function(e){
    let that = this 
    that.setData({
        ornot:-1,
    })
    let params = {
      sessionID: wx.getStorageSync('USER_SESSIONID'),
      appointmentID: e.currentTarget.dataset.id,
    }
    ports.ModuleAll.leftOneAppointment(params).then(res => {
      console.log(res,"结束学习")
    })

},
  clickAll() {
    let url = `/packageA/pages/myAppointment/myAppointment`;
    wx.navigateTo({
      url: url,
    })
  },
  toBluetooth(e) {
    if (!wx.getStorageSync('USER_SESSIONID')) {
      return wx.showToast({
        title: '请先登录',
        icon: 'none'
      })
    }

    this.toNewIndex1()
    return

    wx.showLoading({
      title: '正在连接......',
      mask: true,
    })
    this.setData({
      bluetoothDeviceName: this.data.info.name, //传入设备名
      appointmentIndex: e.currentTarget.dataset.index,
      mid: e.currentTarget.dataset.id,
      appointmentID: e.currentTarget.dataset.aid,
      tabIndex: e.currentTarget.dataset.type===3?1:0,
      orderSeq: e.currentTarget.dataset.seq,
    })
    // this.setData({
    //   connectStr: '正在连接......'
    // })
    // wx.navigateTo({
    //   url: `/packageA/pages/bluetooth/bluetooth?machineName=${this.data.info.name}`,
    // })
    ports.ModuleAll.getMachineDetail({machineID: this.data.mid}).then(res => {
      if (res.data.header.code===0) {
        this.setData({
          bid: res.data.body.data.iccid,
          info: res.data.body.data,
        })
        this.manualOpenBluetoothAdapter();
      }
      
    })
    
  },
  enterDoor(){
    console.log('info:22',this.data.distance)
    let thatNew = this
    if(this.data.distance > 100){
      wx.showModal({
        title: '提示',
        content: '您当前距离实训舱过远，无法开门,是否查看学习内容',
        complete: (res) => {
          if (res.cancel) {
            console.log('取消');
          }
          if (res.confirm) {
            console.log('确定');
            this.toNewIndex1()
          }
        }
      })
      return
    }
    let checkedTime = wx.getStorageSync('checkedTime')
    const appHour = checkedTime.name.match(/\d+/)
    const appDate = new Date(checkedTime.openDateStr)
    const currentDate = new Date()
    if(!(this.isWithinTimeRange(appHour)&&appDate.toDateString==currentDate.toDateString)){
      console.log("时间不对");
      wx.showModal({
        title: '提示',
        content: '当前未到进入时间，请检查选择的学习时段以及实训舱,是否查看学习内容',
        complete: (res) => {
          if (res.cancel) {
            console.log('取消');
          }
      
          if (res.confirm) {
            console.log('确定');
            this.toNewIndex1()
          }
        }
      })
      return
    }
    
   
  },
  isWithinTimeRange(hour){
    // 创建一个新的 Date 对象表示当前时间
    const now = new Date();
    // 获取当前时间的小时和分钟
    const currentHour = now.getHours();
    const currentMinute = now.getMinutes();
    // 构建目标时间范围的开始和结束时间
    const targetStart = new Date(now);
    targetStart.setHours(hour - 1, 30, 0, 0); 
    const targetEnd = new Date(now);
    targetEnd.setHours(hour, 50, 0, 0); 
    // 比较当前时间是否在目标时间范围内
    return now >= targetStart && now <= targetEnd;
  },
  toNewIndex1(openDoorSuccess){
    let checkedTime = wx.getStorageSync('checkedTime') || {};
    let  {beginTime,endTime} = checkedTime.showTime ? this.formatShowTime(checkedTime.showTime,checkedTime.openDate) :{}
   
    let workType = this.data.checkLearnType.find(item=>item.checked)?.workType;
    let url =  `/packageA/pages/newIndex1/newIndex1?nextBeginTime=${beginTime}&nextEndTime=${endTime}&=workType=${workType}&openDoorSuccess=${!!openDoorSuccess}`
    
    wx.navigateTo({
      url: url,
    })
  },
  formatShowTime(showTime,openDate){
    let times = showTime.split('-')
    let months = openDate.split('-')
    const today = new Date();
    const currentDate =  (today.getMonth() + 1) + '月' + today.getDate() + '日'
    // 构造 beginTime 字符串
    const beginTime = `${months[1]}月${months[2]}日 ${times[0]}`;
    // 对于 endTime，只保留时间部分
    const endTimeFormatted = `${times[1].split(':')[0]}:${times[1].split(':')[1]}`;
    return {
      beginTime,
      endTime: endTimeFormatted
    };
  },
  deleteOneAppointment(e) {
    let appointmentID = e.currentTarget.dataset.id;
    let params = {
      sessionID: wx.getStorageSync('USER_SESSIONID'),
      appointmentID: appointmentID,
    }
    ports.ModuleAll.deleteOneAppointment(params).then(res => {
      wx.showToast({
        title: '删除成功',
        icon: 'none',
      })
      this.onPullDownRefresh()
    })
  },
    //手动搜索设备
  //第一步
  manualOpenBluetoothAdapter() {
    wx.setStorageSync('blueDeviceID', '')
    this.closeBluetoothAdapter()
    this.openBluetoothAdapter()
    // clearInterval(date)
  },
    //移除蓝牙
    closeBluetoothAdapter() {
      wx.closeBluetoothAdapter()
      this.setData({
        _discoveryStarted: false
      })
    },
      //开始扫描
  //第二步
  openBluetoothAdapter() {
    var that = this
    //初始化蓝牙模块所有接口只能初始化后才能调佣
    wx.openBluetoothAdapter({
      //蓝牙初始化成功
      success: (res) => {
        console.log('openBluetoothAdapter success', res)
        this.startBluetoothDevicesDiscovery() //开始搜寻附近的蓝牙外围设备
      },
      //蓝牙初始化失败
      fail: (res) => {
        //手机蓝牙未打开或不支持使用蓝牙返回10001
        if (res.errCode === 10001) {
          //监听用户的蓝牙是否打开（监听蓝牙的状态的改变）也可以调用蓝牙模块的所有API。开发者在开发中应该考虑兼容用户在使用小程序过程中打开/关闭蓝牙开关的情况，并给出必要的提示
          // wx.showLoading({
          //   title: '请打开蓝牙',
          // })
          wx.hideLoading()
          wx.showToast({
            title: '请打开蓝牙',
            icon: 'none',
          })
          wx.onBluetoothAdapterStateChange(function (res) {
            wx.hideLoading()
            if (res.available) {
              // wx.showToast({
              //   title: '搜索设备中...',
              //   icon: 'none'
              // }, 1000)
              that.startBluetoothDevicesDiscovery()
            }
          })
        } else {
          wx.hideLoading()
          wx.showToast({
            title: '连接失败，请重试',
            icon: 'none',
          })
        }
      }
    })
  },
    //扫描并发现外围设备
  //第三步
  startBluetoothDevicesDiscovery() {
    var that = this
    if (this.data._discoveryStarted) return
    this.setData({
      _discoveryStarted: true
    })
    //调用API扫描发现外围设备
    wx.startBluetoothDevicesDiscovery({
      // services:["6E400001-B5A3-F393-E0A9-E50E24DCCA9E"],
      // allowDuplicatesKey: true,
      interval: 1000,
      success: (res) => {
        console.log('startBluetoothDevicesDiscovery success', res)
        // 设置定时器，30秒后停止查找
        setTimeout(function() {
          if (that.data.searchFlag) {
            wx.hideLoading();
            wx.showToast({
              title: '未搜索到设备',
              icon: 'none',
            }) 
            wx.stopBluetoothDevicesDiscovery({
              success: function(res) {
                console.log('停止查找蓝牙设备', res)
              }
            })
          }
        }, 30000)
        // this.getBluetoothDevices()
        //监听蓝牙发现的设备
        this.onBluetoothDeviceFound()
      },
    })
  },
    //监听蓝牙搜索设备
  //第四步
  onBluetoothDeviceFound() {
    var deviceData = []
    var that = this
    //返回扫描到的设备
    wx.onBluetoothDeviceFound((res) => {
      console.log('返回扫描到的设备', res);
      res.devices.forEach(device => {
        if ((!device.name && !device.localName) || !device.connectable) {
          // wx.hideLoading();
          // wx.showToast({
          //   title: '未搜索到设备',
          //   icon: 'none',
          // }) 
          return
        }
        console.log(device.localName,this.data.bid)
        if(device.localName===this.data.bid||device.name===this.data.bid) {
          console.log(123)
          //deviceData.push(device)
          that.createBLEConnection(device.deviceId,device.name)
          // flag = false
          that.data.searchFlag = false
          return
        }
        // console.log(device, '可连接的设备');
        // that.data.bluetoothDeviceName == device.name
        // that.data.bluetoothDeviceName == device.localName
        // if (device.name.includes(that.data.bluetoothDeviceName) || device.localName.includes(that.data.bluetoothDeviceName)){
        // console.log('符合条件', device);
        //发现设备的id
        // let deviceList = that.data.devices
        // deviceList.some(function (item) {
        //   return item.deviceId == device.deviceId
        // })
        // console.log(findSome, '打印');
        // if (!findSome) {
        //   deviceList.push(device)
        //   that.setData({
        //     devices: deviceList
        //   })
        // }
        //}
      })
      // res.devices.forEach(device => {
        
      // })

    })
  },
    //点击事件创建设备连接
    createBLEConnection(id,name) {
      // console.log(e, '点击事件');
      // wx.showLoading({
      //   title: '连接中'
      // })
      //保存deviceId,name到本地
      let deviceId = id
      wx.setStorageSync('blueDeviceID', id)
      wx.setStorageSync('blueDeviceName', name)
      //this.updateMacAddress(deviceId)
  
  
      //调用API连接设备
      wx.createBLEConnection({
        //连接的设备id
        deviceId,
        //连接成功
        success: (res) => {
          
          // this.door()
          
          console.log('连接成功', res);
  
          //获得service
          wx.getBLEDeviceServices({
            deviceId, // 搜索到设备的 deviceId
            success: (res) => {
              
              let serviceIDs = []
              for (let i = 0; i < res.services.length; i++) {
                if (res.services[i].isPrimary) {
                  serviceIDs.push(res.services[i].uuid)
                  // 可根据具体业务需要，选择一个主服务进行通信
                }
                //保存serviceID们到本地
                wx.setStorageSync('blueServiceIDs', serviceIDs)
                
              }
            },fail:(res)=>{
              console.log(res,999);
            }
          })
          //读写特征值
          wx.getBLEDeviceCharacteristics({
            deviceId, // 搜索到设备的 deviceId
            serviceId:wx.getStorageSync('blueServiceIDs'), // 上一步中找到的某个服务
            success: (res) => {
              console.log(res,123456)
              for (let i = 0; i < res.characteristics.length; i++) {
                let item = res.characteristics[i]
                
                if (item.properties.write) { // 该特征值可写
                  // 本示例是向蓝牙设备发送一个 0x00 的 16 进制数据
                  // 实际使用时，应根据具体设备协议发送数据
                  let buffer = new ArrayBuffer(1)
                  let dataView = new DataView(buffer)
                  dataView.setUint8(0, 0)
                  wx.writeBLECharacteristicValue({
                    deviceId,
                    serviceId,
                    characteristicId: item.uuid,
                    value: buffer,
                  })
                }
                if (item.properties.read) { // 该特征值可读
                  wx.readBLECharacteristicValue({
                    deviceId,
                    serviceId,
                    characteristicId: item.uuid,
                  })
                }
                if (item.properties.notify || item.properties.indicate) {
                  // 必须先启用 wx.notifyBLECharacteristicValueChange 才能监听到设备 onBLECharacteristicValueChange 事件
                  wx.notifyBLECharacteristicValueChange({
                    deviceId,
                    serviceId,
                    characteristicId: item.uuid,
                    state: true,
                  })
                }
              }
            },fail:(res)=>{
              console.log(res,999);
            }
          })
          /* wx.onBLECharacteristicValueChange(characteristic => {
            // TODO 收到的信息为ArrayBuffer类型，可根据自己的需要转换 可发送给父组件用来回显
            console.log("收到原始的数据", characteristic, characteristic.value);
            // 测试向设备发送数据
            // this.writeBLECharacteristicValue(JSON.stringify({"FAN":"OFF"}))
          });
          
          //向蓝牙设备写入二进制数据
          writeBLECharacteristicValue(jsonStr);
           
            let arrayBufferValue = str2ab(jsonStr);
            console.log("发送数据给蓝牙", "原始字符串", jsonStr, "转换arrayBuffer", arrayBufferValue);
  
            wx.writeBLECharacteristicValue({
              deviceId: this._deviceId,
              serviceId: this._serviceId, 
              characteristicId: this._characteristicId,
              value: arrayBufferValue, // 只能发送arrayBuffer类型数据
              
              success(res) {
                console.log("消息发送成功", res.errMsg);
                wx.showToast({ title: "消息发送成功", icon: "none" });
              },
              fail(e) {
                console.log("发送消息失败", e);
                wx.showToast({ title: "发送消息失败,错误信息: " + e.errMsg, icon: "none" });
              },
            });
  
   */
  
          //停止蓝牙搜索设备
          //this.stopBluetoothDevicesDiscovery()
          wx.stopBluetoothDevicesDiscovery({
            success: function(res) {
              console.log('停止查找蓝牙设备', res)
            }
          })
          
          let str1 = 'showList['+this.data.appointmentIndex+'].disable'
          this.setData({
            [str1] : true
          })
          let str2 = 'showList['+this.data.appointmentIndex+'].btnShow'
          this.setData({
            [str2] : true
          })
          this.door()
          // setTimeout(() => {
          //   wx.navigateBack({
          //     delta: 1
          //   })
          // }, 500)
        },
        fail: (err) => {
          wx.hideLoading();
          wx.showToast({
            title: '连接失败，请重试',
            icon: 'none'
          })
          console.log(err, '连接失败');
        }
      })
    },
  getBluetoothData() {
    if (wx.getStorageSync('blueDeviceID')) {
      this.setData({
        deviceID: wx.getStorageSync('blueDeviceID'),
        deviceName: wx.getStorageSync('blueDeviceName'),
        serviceIDs: wx.getStorageSync('blueServiceIDs'),
      })
      //再次调用getDetail，发送开门命令
      ports.ModuleMachine.getMachineDetail({
        machineID: this.data.id
      }).then(res => {})


    }
  },
  //获取BEL的特征
  getBLEDeviceCharacteristics(e) {
    //调用设备服务的API
    let deviceId = wx.getStorageSync('blueDeviceID')
    var serviceId = e.currentTarget.dataset.item;
    this.setData({
      charas: [],
      serviceIDSel: serviceId
    })
    wx.getBLEDeviceCharacteristics({
      deviceId,
      serviceId,
      success: (res) => {
        let charas = []
        for (let i = 0; i < res.characteristics.length; i++) {
          let chara = {}
          let that = this
          let item = res.characteristics[i]
          chara.id = item.uuid
          chara.readable = "indicate:" + item.properties.indicate + ",notify:" + item.properties.notify + ",read:" + item.properties.read + ",write:" + item.properties.write
          charas.push(chara)
          this.setData({
            charas
          })

          if (item.properties.notify || item.properties.indicate) {
            // 启用蓝牙低功耗设备特征值变化时的 notify 功能
            // 必须先启用 wx.notifyBLECharacteristicValueChange 才能监听到设备 onBLECharacteristicValueChange 事件
            // 启用特征值通知
            wx.notifyBLECharacteristicValueChange({
              deviceId, // 蓝牙设备ID
              serviceId, // 服务的UUID
              characteristicId: wx.getStorageSync('uuid'), // 特征值的UUID
              state: true, // true 表示启用通知
              success: function (res) {
                console.log('启用通知成功', res);
                console.log(item.properties.notify, 123456)
              },
              fail: function (err) {
                console.error('启用通知失败', err);
              },
              catch (error) {
                console.error("启用通知过程中发生错误", error);
              }
            });
            // 监听特征值变化
            wx.onBLECharacteristicValueChange(function (characteristic) {
              // 处理接收到的数据
              let dataView = new DataView(characteristic.value);
              let receivedData = '';
              for (let i = 0; i < dataView.byteLength; i++) {
                receivedData += String.fromCharCode(dataView.getUint8(i));
              }
              console.log('接收到的数据:', receivedData);
              // 根据业务逻辑处理接收到的数据
            });

          }

        }
      },
      //错误
      fail(res) {
        console.error('getBLEDeviceCharacteristics', res)
      }
    })
  },
  //可读特征值
  readBLECharacteristicValue(e) {
    console.log(e);
    let that = this
    let uuid = e.currentTarget.dataset.item;
    wx.setStorageSync('uuid', uuid)
    wx.readBLECharacteristicValue({
      deviceId: wx.getStorageSync('blueDeviceID'),
      serviceId: this.data.serviceIDSel,
      characteristicId: uuid,
      success: function (res) {},
      fail: function () {
        that.setData({
          charValue: '不可读'
        })
      }
    })
    // 监听特征值变化
    wx.onBLECharacteristicValueChange(function (characteristic) {
      let res = ab2hex(characteristic.value)
      that.setData({
        charValue: res
      })
      wx.setStorageSync('characteristic', characteristic)
      console.log(wx.getStorageSync('characteristic'))
      console.log("收到原始的数据", characteristic, characteristic.value);
      // 处理接收到的数据
      let dataView = new DataView(characteristic.value);
      let receivedData = '';
      // 假设设备发送的数据是UTF-8编码的字符串
      for (let i = 0; i < dataView.byteLength; i++) {
        receivedData += String.fromCharCode(dataView.getUint8(i));
      }

      console.log('接收到的数据:', receivedData);
      /* // 处理接收到的数据
        // 将 ArrayBuffer 转换为合适的格式，例如十六进制字符串
        let dataView = new DataView(characteristic.value);
        //转格式为十六进制字符串，暂时不用
        //let hexString = Array.from(dataView).map(byte => byte.toString(16).padStart(2, '0')).join(' '); 
        console.log('接收到的数据:', dataView);
      
        // 根据业务逻辑处理接收到的数据
        // 例如，可以在这里更新应用的状态或UI  */
    })
  },
  //写BEI特征值
  writeBLECharacteristicValue(jsonData) {
    // 向蓝牙设备发送数据
    //let uuid = wx.getStorageSync('uuid');
    const serviceId = '0000FFF0-0000-1000-8000-00805F9B34FB' //写死 每个蓝牙都一样
    const characteristicId = "0000FFF2-0000-1000-8000-00805F9B34FB" //写死 每个蓝牙都一样
    let asciiArray = jsonToAscii(jsonData)
    let buffer = new ArrayBuffer(asciiArray.length);
    let dataView = new DataView(buffer)
    for (let i = 0; i < asciiArray.length; i++) {
      dataView.setUint8(i, asciiArray[i]);
    }
    wx.writeBLECharacteristicValue({
      deviceId: wx.getStorageSync('blueDeviceID'),
      serviceId,
      characteristicId,
      value: buffer,
      success(res) {
        // alert("消息发送成功")
      },
      fail(e) {
        // alert("发送消息失败: " + e.errMsg,);
      },
    })
    wx.onBLECharacteristicValueChange((characteristic) => {
      console.log(characteristic, "characteristic")
    })
  },

  light(){
    this.action('GD_1','on')
  },
  door(){
    wx.hideLoading();
          // wx.showToast({
          //   title: '连接成功',
          //   icon: 'none',
          //   duration:3000
          // })
 
    //let code = e.currentTarget.dataset.id
    //let command = ""
    //let r = this.data.sensorList.find((sensor)=>{sensor.code==="door"})
    this.action('door','unlock')
  },
  //按下按钮对应的操作
  action(sensor,act) {
    let that = this
          //调用写特征值（开门命令
      //this.writeBLECharacteristicValue(jsonData)
    this.getCommand(sensor,act).then(jsonData => {
      if (jsonData == 'fail')
        return alert('获取通讯key失败')
      wx.closeBLEConnection({
        deviceId: wx.getStorageSync('blueDeviceID'), // 蓝牙设备ID
        success: (res) => {
          wx.removeStorageSync('blueDeviceID')
          console.log('断开蓝牙连接成功', res);
        },
        fail: (err) => {
          console.log('断开蓝牙连接失败', err);
        }
      });  
      // if (!wx.getStorageSync('blueDeviceID'))
      //   return alert('请连接蓝牙设备') 
      let params= {
        sessionID: wx.getStorageSync('USER_SESSIONID'),
        machineID: that.data.mid,
        operateType: 13,
        operateName: '蓝牙连接',
        lastScanMac: that.data.info.macAddress,
      }
      ports.ModuleAll.operateOneMachine(params).then(res => {
        if (res.data.header.code===0) {
          console.log("开始一次机器使用记录")
        }
      })
      wx.showToast({
        title: '请在听到门锁啪嗒一声后，再推门进入',
        icon: 'none',
        duration: 1000
      })
      setTimeout(()=>{
        wx.setStorageSync('enterFlag',true)
        this.setData({
          enterFlag: true
        })
        let machineID = that.data.mid
        let appointmentID = that.data.appointmentID;
        let trainBusinessID = that.data.info.trainBusinessID
        let shopID = that.data.info.shopID;
        let mapAddress = that.data.info.address;
        let mapX = that.data.info.mapX;
        let mapY = that.data.info.mapY;
        let tabIndex = that.data.tabIndex;
        let orderSeq = that.data.orderSeq;
        wx.navigateTo({
          url: `/packageA/pages/learningOperation/learningOperation?appointmentID=${appointmentID}&machineID=${machineID}&shopID=${shopID}&mapAddress=${mapAddress}&mapX=${mapX}&mapY=${mapY}&trainBusinessID=${trainBusinessID}&tabIndex=${tabIndex}&seq=${orderSeq}`,
        })
      },1000)
      //调用写特征值（开门命令
      this.writeBLECharacteristicValue(jsonData)
    })
  },
  //获取蓝牙命令Json
  getCommand(sensorCode,commandValue) {
    return new Promise(resolve => {
      //先获取实训仓的 所有传感器列表
      ports.ModuleMachine.getSensorList({
        machineID: this.data.mid,
        pageNumber: 999
      }).then(res => {
        if (res.data.header.code == 0) {
          let sensorID = ''
          //筛选出 传入名称 的传感器
          res.data.body.data.rows.forEach(row => {
            if (row.code == sensorCode)
              sensorID = row.sensorID
          });
          //获取该传感器的操作命令，先 写死 unlock 开门
          ports.ModuleMachine.getOneMachineCommandForSanOne({
            machineID: this.data.mid,
            // sensorID,
            code:'door',
            commandValue,
          }).then(res2 => {
            console.log(res2,"操作命令")
            if (res2.data.header.code == 0) resolve(res2.data.body.data)
            else return resolve('fail')
          })
        } else return resolve('fail')
      })
    })
  },

  getSensor() {
    var that = this;
    ports.ModuleMachine.getSensorList({
      machineID: this.data.id,
      pageNumber: 99,
    }).then(res => {
      if (res.data.header.code == 0) {
        that.setData({
          sensorList: res.data.body.rows
        })
      } else return alert('更新失败')
    })
  },
  getDistance() {
    function calculateDistance(lat1, lng1, lat2, lng2) {
        var that = this;
        let rad1 = lat1 * Math.PI / 180.0;
        let rad2 = lat2 * Math.PI / 180.0;
        let a = rad1 - rad2;
        let b = lng1 * Math.PI / 180.0 - lng2 * Math.PI / 180.0;
        let s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2) + Math.cos(rad1) * Math.cos(
          rad2) * Math.pow(
          Math.sin(b / 2), 2)));
        s = s * 6378.137;
        s = Math.round(s * 10000) / 10000;
        s = s.toString();
        s = s.substring(0, s.indexOf('.') + 2);
        console.log('距离：', s);
        return s; //返回距离
    } 
    wx.getLocation({
      type: 'gcj02', // 默认为 wgs84 返回 gps 坐标，gcj02 返回可用于 wx.openLocation 的坐标
      altitude: true, // true 会返回高度信息
      isHighAccuracy: true, //开启高精度定位
      highAccuracyExpireTime: 3100, //高精度定位超时时间(ms)
      success: (res)=> {
        console.log(res, '获取位置成功');
        console.log(this.info, '获取位置成功');
        // let distance = calculateDistance(this.data.info.mapX,this.data.info.mapY, res.latitude, res.longitude)
        let distance = longitude.calculateDistance(this.data.info.mapY,this.data.info.mapX, res.latitude, res.longitude)
        console.log(distance,"距离")
        // let distance = longitude.calculateDistance(this.info.mapX, this.info.mapY, res.latitude, res.longitude)
        this.setData({
          distance
        })
        this.setData({
          myX: res.latitude
        })
        this.setData({
          myY: res.longitude
        })
      }
    })

  },
  navigateToVideo: function(e) {
    const appointmentID = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: '/packageA/pages/memberStudyVideoDetail/memberStudyVideoDetail?appointmentID=' + appointmentID
    });
  },
    /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    const shouldRefresh = wx.getStorageSync('refreshData');
    if (shouldRefresh) {
      // 清除标志
      wx.removeStorageSync('refreshData');
      // 执行数据刷新操作
      this.init()
      // this.loadData();
    }
    this.getBluetoothData()
  },


  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.setData({
      showList: [], // 清空列表
      page: 1       // 重置页码
    });
    this.init()
    wx.stopPullDownRefresh(); 
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    this.init()
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },
  // 下拉刷新事件处理函数
  refresh() {
    console.log('下拉刷新')
    // this.setData({
    //   isRefreshing: true,
    //   appointmentList: [],
    //   showList:[],
    //   pageNumber: 1,
    // },()=>{
    //   this.pageGo()
    // });
  },

  // 触底加载事件处理函数
  loadMore() {
    // this.data.pageNumber++;
    // this.init();
    this.nextFn()
    console.log('触底加载事件')
  },

  changeAll(){
    if (this.data.isMonth == true) {
      this.setData({
        page: 1,
        isMonth:false,
        changeName:'按月检查',
      })
      this.init()
    }else{
      this.setData({
        page: 1,
        isMonth:true,
        changeName:'查看所有预约',
      })
      this.init()
    }
  },
  showEnvScore(e) {
    const appointmentID = e.currentTarget.dataset.id;
    const sessionID = wx.getStorageSync('USER_SESSIONID');
    const params = {
      sessionID,
      objectID: appointmentID
    };
    ports.ModuleAll.getObjectDiscussList(params).then(res => {
      let list = (res.data.body.data.rows || []);
      let html = '';
      if (list.length === 0) {
        html = '<div style="text-align:center;color:#999;font-size:28rpx;">未评价</div>';
      } else {
        html = list.map(item => {
          let imgHtml = '';
          if (item.attachmentDtos && item.attachmentDtos.length > 0) {
            imgHtml = '<div style="margin-top:8rpx;">图片：' + item.attachmentDtos.map(img => `<img src=\"${img.url}\" style=\"width:120rpx;height:120rpx;margin-right:8rpx;display:inline-block;\" />`).join('') + '</div>';
          }
          return (
            `<div style=\"margin-bottom:24rpx;\">` +
            `<div>${item.memberName || ''}对你评分</div>` +
            `<div>分数：${item.score || ''}</div>` +
            `<div>内容：${item.conTent || ''}</div>` +
            imgHtml +
            `</div>`
          );
        }).join('<div style=\"border-bottom:1px solid #eee;margin:8rpx 0;\"></div>');
      }
      this.setData({
        envScoreList: list,
        showEnvScoreDialog: true
      });
    });
  },
  onHideEnvScoreModal() {
    this.setData({ hideEnvScoreModal: true });
  },
  onCloseEnvScoreDialog() {
    this.setData({ showEnvScoreDialog: false });
  },
})

// 去掉年str
function getNewTimeStr (str){
  if (!str) return '';
  const [datePart, timePart] = str.split(' ');
  if (!datePart || !timePart) return '';
  const [year, month, day] = datePart.split('-');
  return `${month}-${day} ${timePart}`;
}