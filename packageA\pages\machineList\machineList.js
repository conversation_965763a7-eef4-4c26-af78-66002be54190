// packageA/pages/machineList/machineList.js
var ports = require("../../../utils/ports")
var app = getApp()
var longitude = require('@/utils/longitude.js')


function buildUrl_n(base, params) {
  const encodedParams = Object.entries(params)
    .map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(typeof value === 'object' && value !== null ? JSON.stringify(value) : (value || ''))}`)
    .join('&');
  return `${base}?${encodedParams}`;
}

Page({

  /**
   * 页面的初始数据
   */
  data: {
    machineList:[],
    isLocating: false,
    city: {name:"上海"}, //当前城市信息
    checkedMachine:null
  },
  formatShowTime(showTime,openDate){
    let times = showTime.split('-')
    let months = openDate.split('-')
    const today = new Date();
    const currentDate =  (today.getMonth() + 1) + '月' + today.getDate() + '日'
    // 构造 beginTime 字符串
    const beginTime = `${months[1]}月${months[2]}日 ${times[0]}`;
    // 对于 endTime，只保留时间部分
    const endTimeFormatted = `${times[1].split(':')[0]}:${times[1].split(':')[1]}`;
    return {
      beginTime,
      endTime: endTimeFormatted
    };
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.getMachineList()

    
    let checkedTime = wx.getStorageSync('checkedTime') || {};
    const {beginTime,endTime} = checkedTime.showTime ? this.formatShowTime(checkedTime.showTime,checkedTime.openDate) :{}
    this.setData({
      nextBeginTime: beginTime,
      nextEndTime: endTime
    })

  },
  clickArticle() {
    let url='/packageA/pages/agreement/agreement?articleID=' + app.articleID;
    wx.navigateTo({
      url: url,
    })
  },
  getMachineList() {
    // debugger;
    let params = {
      applicationID: app.applicationID,
      companyID: app.companyID,
      pageNumber: 999
      // customerCompanyID:app.companyID
    }
    ports.ModuleAll.getMachineList(params).then(res => {
      wx.stopPullDownRefresh()
      let list = res.data.body.data.rows || []
      for (let i = 0;i < list.length;i ++) {
        this.getDistance(list[i].mapX,list[i].mapY,i)
        this.getMemberTrain(list[i].trainBusinessID,i)
      }
      this.setData({
        machineList: list
      })
      console.log(this.data.machineList);
    })
  },
  getMemberTrain(id,i) {
    let params = {
      applicationID: app.applicationID,
      sessionID: wx.getStorageSync('USER_SESSIONID'),
      trainBusinessID: id,
      joinMemberID: wx.getStorageSync('USER_MEMBERID')
    }
    ports.ModuleAll.getMemberTrainList(params).then(res => {
      
      let list = res.data.body.data.rows || []
      let tempStr = "machineList["+ i + "].isShow"; 
      this.setData({
        [tempStr]: list.length === 0 ? false : true
      })
    })
  },
  getDistance(mapX,mapY,i) {
    function calculateDistance(lat1, lng1, lat2, lng2) {
        var that = this;
        let rad1 = lat1 * Math.PI / 180.0;
        let rad2 = lat2 * Math.PI / 180.0;
        let a = rad1 - rad2;
        let b = lng1 * Math.PI / 180.0 - lng2 * Math.PI / 180.0;
        let s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2) + Math.cos(rad1) * Math.cos(
          rad2) * Math.pow(
          Math.sin(b / 2), 2)));
        s = s * 6378.137;
        s = Math.round(s * 10000) / 10000;
        s = s.toString();
        s = s.substring(0, s.indexOf('.') + 2);
        console.log('距离：', s);
        return s; //返回距离
    } 
    function formatDistance(distanceInMeters) {
      if (distanceInMeters >= 1000) {
        // 将距离从米转换为千米，并保留两位小数
        const distanceInKilometers = (distanceInMeters / 1000).toFixed(2);
        return `${distanceInKilometers} 千米`;
      } else {
        return `${distanceInMeters} 米`;
      }
    }
    
    wx.getLocation({
      type: 'gcj02', // 默认为 wgs84 返回 gps 坐标，gcj02 返回可用于 wx.openLocation 的坐标
      altitude: true, // true 会返回高度信息
      isHighAccuracy: true, //开启高精度定位
      highAccuracyExpireTime: 3100, //高精度定位超时时间(ms)
      success: (res)=> {
        console.log(res, '获取位置成功');
        console.log(this.info, '获取位置成功');
        // let distance = calculateDistance(this.data.info.mapX,this.data.info.mapY, res.latitude, res.longitude)
        let distance = longitude.calculateDistance(mapY,mapX, res.latitude, res.longitude)
        let distanceNumber = distance;
        // console.log(distance,"距离")
        let tempStr = "machineList["+ i + "].distance"; 
        let numKey   =  "machineList["+ i + "].distanceNumber"; 
        
        this.setData({
          [tempStr]: formatDistance(distance),
          [numKey]:  distanceNumber
        })
        // let distance = longitude.calculateDistance(this.info.mapX, this.info.mapY, res.latitude, res.longitude)
      }
    })

  },
  href(e){
    let id = e.currentTarget.dataset.id
    let trainid = e.currentTarget.dataset.trainid
    let url = `/packageA/pages/machineDetail/machineDetail?id=${id}&trainid=${trainid}`
    // let url = `/packageA/pages/machineDetail/machineDetail?scene=19d28aa3912b4e29989257c48594ebcb`
    wx.navigateTo({
      url,
    })
  },
  appointment(e){
    
    let item = e.currentTarget.dataset.item
    console.log(item,'+++');
    let url = `/packageA/pages/appointment/appointment?machineID=${item.id}&trainBusinessID=${item.trainBusinessID}&machineName=${item.name}`
    wx.navigateTo({
      url,
    })
  },
  // 确认预约页
  toAppointmentConfirm(e){
    let id = e.currentTarget.dataset.id
    let distance = e.currentTarget.dataset.distance
    
    ports.ModuleAll.getMachineDetail({machineID:id}).then(resOne => {
      
      let info = resOne.data?.body?.data || {}
      info.machineName=info.name
      info.id=info.machineID
      const checkedMachine = {
        id:info.id,
        machineName:info.machineName,
        address: info.address,
        code:info.code,
        distance:this.data.distance || '',
        bizType:info.bizType
      }
      wx.setStorageSync('checkedMachine', checkedMachine)
      const {id,machineName,machineDistance,address,code,bizType} = checkedMachine;
    const data = {
      city:this.data.city,
      checkedMachine:{id,machineName,machineDistance,address,code,bizType},
      nextBeginTime: this.data.nextBeginTime,
      nextEndTime: this.data.nextEndTime,
      isLocating:this.data.isLocating,
      distance: distance || 0,
      friend1:{},
      friend2:{}
    }

    console.log(data);
    let url = buildUrl_n('/packageA/pages/appointmentConfirm/appointmentConfirm',data)
      wx.navigateTo({
        url: url,
      })
    })
    return
    
  },
  toVideo(e){
    let url = `/packageA/pages/caseList/caseList?navigatorID=${app.videoID}`
    wx.navigateTo({
      url
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.getMachineList()
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})